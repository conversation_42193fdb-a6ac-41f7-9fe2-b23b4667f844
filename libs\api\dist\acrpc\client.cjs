"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } } function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; } function _optionalChainDelete(ops) { const result = _optionalChain(ops); return result == null ? true : result; }




var _chunk467PIPJEcjs = require('../chunk-467PIPJE.cjs');
require('../chunk-Q7SFCCGT.cjs');

// src/acrpc/client.ts
var _zod = require('zod');
function isEndpoint(schemaEntry) {
  return schemaEntry != null && typeof schemaEntry === "object" && ("input" in schemaEntry && (schemaEntry["input"] instanceof _zod.z.ZodType || schemaEntry["input"] === null || schemaEntry["input"] === void 0) && ("output" in schemaEntry && (schemaEntry["output"] instanceof _zod.z.ZodType || schemaEntry["output"] === null || schemaEntry["output"] === void 0)));
}
var HttpError = class extends Error {
  
  
  
  
  constructor(method, url, status, description) {
    super(`Fetch at ${method.toUpperCase()} ${url} failed, status ${status}, description: '${description}'`);
    this.method = method;
    this.url = url;
    this.status = status;
    this.description = description;
  }
};
function getLocalStorage() {
  if (typeof window !== "undefined") {
    return _nullishCoalesce(window.localStorage, () => ( null));
  }
  if (typeof globalThis !== void 0) {
    return _nullishCoalesce(globalThis.localStorage, () => ( null));
  }
  return null;
}
function parsePathToMasterPaths(path) {
  if (!path.length) {
    return [];
  }
  const parts = path.split("/").slice(1);
  const masterPaths = [];
  for (let i = 0; i < parts.length; i++) {
    masterPaths.push("/" + parts.slice(0, i + 1).join("/"));
  }
  return masterPaths;
}
function initMasterPathMapEntryFactory(map) {
  return function initMasterPathMapEntry(path) {
    const masterPaths = parsePathToMasterPaths(path);
    for (const masterPath of masterPaths) {
      if (!map.has(masterPath)) {
        map.set(masterPath, []);
      }
      map.get(masterPath).push(path);
    }
  };
}
function normalizeMasterPathMap(masterPathMap) {
  for (const [masterPath, paths] of masterPathMap) {
    masterPathMap.set(masterPath, [...new Set(paths)]);
  }
}
function fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap) {
  for (const [masterPath, paths] of masterPathMap) {
    for (const path of paths) {
      if (!reverseMasterPathMap.has(path)) {
        reverseMasterPathMap.set(path, []);
      }
      reverseMasterPathMap.get(path).push(masterPath);
    }
  }
}
function hydrateInvalidPathCacheSet(invalidPathCacheSet) {
  const invalidPaths = _optionalChain([getLocalStorage, 'call', _ => _(), 'optionalAccess', _2 => _2.getItem, 'call', _3 => _3("acrpc:invalid-paths")]);
  if (invalidPaths) {
    try {
      const parsedInvalidPaths = JSON.parse(invalidPaths);
      for (const invalidPath of parsedInvalidPaths) {
        invalidPathCacheSet.add(invalidPath);
      }
    } catch (error) {
      console.error("Error parsing invalid paths", error);
      _optionalChain([getLocalStorage, 'call', _4 => _4(), 'optionalAccess', _5 => _5.removeItem, 'call', _6 => _6("acrpc:invalid-paths")]);
    }
  }
}
function invalidatePathCache2Factory(masterPathMap, reverseMasterPathMap, invalidPathCacheSet) {
  return function invalidatePathCache2(path, depth) {
    const masterPaths = _nullishCoalesce(masterPathMap.get(path), () => ( []));
    _chunk467PIPJEcjs.dir.call(void 0, 
      "invalidating path cache",
      {
        path,
        depth,
        masterPaths
      }
    );
    const masterPath = masterPaths[Math.max(0, masterPaths.length - depth)];
    const paths = _nullishCoalesce(reverseMasterPathMap.get(masterPath), () => ( []));
    _chunk467PIPJEcjs.dir.call(void 0, 
      "invalidating path cache 2",
      {
        masterPath,
        paths
      }
    );
    for (const path2 of paths) {
      invalidPathCacheSet.add(path2);
    }
    _optionalChain([getLocalStorage, 'call', _7 => _7(), 'optionalAccess', _8 => _8.setItem, 'call', _9 => _9(
      "acrpc:invalid-paths",
      JSON.stringify([...invalidPathCacheSet])
    )]);
  };
}
function createClient(schema, options) {
  const transformer = _nullishCoalesce(options.transformer, () => ( _chunk467PIPJEcjs.jsonTransformer));
  const url = options.entrypointUrl;
  const masterPathMap = /* @__PURE__ */ new Map();
  const reverseMasterPathMap = /* @__PURE__ */ new Map();
  const invalidPathCacheSet = /* @__PURE__ */ new Set();
  const initMasterPathMapEntry = initMasterPathMapEntryFactory(masterPathMap);
  const invalidatePathCache2 = invalidatePathCache2Factory(
    masterPathMap,
    reverseMasterPathMap,
    invalidPathCacheSet
  );
  _chunk467PIPJEcjs.dir.call(void 0, {
    invalidPathCacheSet
  });
  const baseFetch = _nullishCoalesce(options.fetch, () => ( fetch));
  const baseInit = { ...options.init };
  function fillClientFetcher(schema2, names, result) {
    for (const [name, schemaEntry] of Object.entries(schema2)) {
      const kebabName = _chunk467PIPJEcjs.kebabTransformer.transform(name);
      if (isEndpoint(schemaEntry)) {
        let parseArgs = function(args) {
          if (schemaEntry.input === null) {
            return [void 0, { ...args[0] }];
          }
          return [args[0], args[1]];
        };
        const path = ["", ...names].join("/");
        const method = name;
        initMasterPathMapEntry(path);
        const obj = {
          [method]: async function(...args) {
            const [input, init] = parseArgs(args);
            if (schemaEntry.input != null && !input) {
              throw new Error("Input data argument not provided.");
            }
            _chunk467PIPJEcjs.log.call(void 0, `Performing ${method.toUpperCase()} ${path}...`);
            const currentUrl = new URL(path, url);
            const isInvalidCache = invalidPathCacheSet.has(path);
            const requestInit = {
              ...baseInit,
              ...init,
              headers: {
                ...baseInit.headers,
                ..._optionalChain([init, 'optionalAccess', _10 => _10.headers]),
                ...isInvalidCache ? {
                  "Cache-Control": "reload"
                } : null
              },
              method: method.toUpperCase()
            };
            if (schemaEntry.input !== null && input !== void 0) {
              const serializedInput = transformer.serialize(input);
              if (method === "get") {
                currentUrl.searchParams.set(
                  "__body",
                  encodeURIComponent(serializedInput)
                );
              } else {
                requestInit.headers["Content-Type"] = "application/json";
                requestInit.body = serializedInput;
              }
            }
            const fetch2 = _nullishCoalesce(_optionalChain([init, 'optionalAccess', _11 => _11.fetch]), () => ( baseFetch));
             _optionalChainDelete([init, 'optionalAccess', _12 => delete _12.fetch]);
            const fetchResult = await fetch2(
              currentUrl.origin + currentUrl.pathname + currentUrl.search,
              requestInit
            );
            if (!_optionalChain([init, 'optionalAccess', _13 => _13.skipInterceptor])) {
              await _optionalChain([options, 'access', _14 => _14.interceptor, 'optionalCall', _15 => _15({
                method,
                path,
                response: fetchResult,
                ctx: _optionalChain([init, 'optionalAccess', _16 => _16.ctx])
              })]);
            }
            if (fetchResult.ok) {
              let output = null;
              if (schemaEntry.output !== null) {
                const rawOutput = await fetchResult.text();
                output = transformer.deserialize(rawOutput);
              }
              _chunk467PIPJEcjs.dir.call(void 0, {
                autoScopeInvalidationDepth: schemaEntry.autoScopeInvalidationDepth,
                invalidate: schemaEntry.invalidate
              });
              _chunk467PIPJEcjs.dir.call(void 0, 
                "before invalidations",
                {
                  masterPathMap,
                  // cacheVersionMap,
                  invalidPathCacheSet
                }
              );
              const autoScopeInvalidationDepth = _nullishCoalesce(schemaEntry.autoScopeInvalidationDepth, () => ( 0));
              if (autoScopeInvalidationDepth) {
                invalidatePathCache2(path, autoScopeInvalidationDepth);
              }
              if (schemaEntry.invalidate) {
                for (const invalidate of schemaEntry.invalidate) {
                  invalidatePathCache2(invalidate, 0);
                }
              }
              _chunk467PIPJEcjs.dir.call(void 0, 
                "after invalidations",
                {
                  masterPathMap,
                  // cacheVersionMap,
                  invalidPathCacheSet
                }
              );
              return output;
            }
            throw new HttpError(
              method,
              path,
              fetchResult.status,
              await fetchResult.text() || fetchResult.statusText
            );
          }
        };
        Object.assign(result, obj);
      } else {
        const nestedResult = result[name] = {};
        fillClientFetcher(
          schemaEntry,
          [...names, kebabName],
          nestedResult
        );
      }
    }
    return result;
  }
  const fetcher = fillClientFetcher(
    schema,
    [],
    {}
  );
  normalizeMasterPathMap(masterPathMap);
  fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap);
  hydrateInvalidPathCacheSet(invalidPathCacheSet);
  return {
    fetcher
  };
}



exports.HttpError = HttpError; exports.createClient = createClient;
//# sourceMappingURL=client.cjs.map