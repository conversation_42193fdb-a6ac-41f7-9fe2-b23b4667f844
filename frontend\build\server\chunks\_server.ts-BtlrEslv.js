import { A as API_URL } from './private-DXTbUqrb.js';

const locales = ["en", "ru"];
const url = "https://commune.my";
const apiUrl = API_URL;
function getXhtmlLink(locale, route) {
  return `<xhtml:link rel="alternate" hreflang="${locale === null ? "x-default" : locale}" href="${url}${locale ? `/${locale}` : ""}/${route}" />`;
}
function getUrl(...routes) {
  const route = routes.join("/");
  return `
<url>
	<loc>${url}/${route}</loc>
	${locales.map((locale) => getXhtmlLink(locale, route)).join("\n")}
	${getXhtmlLink(null, route)}
</url>
		`.trim();
}
function generateBasicPages() {
  const routes = [
    "",
    "the-law",
    "rules",
    "new-english",
    "new-calendar"
  ];
  return routes.map((route) => getUrl(route));
}
function generateCommunePages(ids) {
  return ids.map((id) => getUrl("communes", id));
}
function generateReactorPostPages(ids) {
  return ids.map((id) => getUrl("reactor", id));
}
function generateReactorHubPages(ids) {
  return ids.map((id) => getUrl("reactor", "hubs", id));
}
function generateReactorCommunityPages(ids) {
  return ids.map((id) => getUrl("reactor", "communities", id));
}
function generateSitemap(data) {
  const lines = [
    '<?xml version="1.0" encoding="UTF-8" ?>',
    '<urlset xmlns="https://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="https://www.w3.org/1999/xhtml">',
    ...generateBasicPages(),
    ...generateCommunePages(data.communeIds),
    ...generateReactorPostPages(data.reactorPostIds),
    ...generateReactorHubPages(data.reactorHubIds),
    ...generateReactorCommunityPages(data.reactorCommunityIds),
    "</urlset>"
  ];
  return lines.join("\n");
}
const SITEMAP_KEY = "BycS9tEQKMvasaJGni12BUBVl5xl1E7fsGqocvG5xKe5es3XWlrMXeRGYvJ7r3iS";
async function GET() {
  const sitemapGenerationData = await fetch(
    `${apiUrl}/sitemap/generation-data?key=${SITEMAP_KEY}`
  ).then((res) => res.json());
  console.dir({
    sitemapGenerationData
  }, { depth: null });
  const sitemap = generateSitemap(sitemapGenerationData);
  return new Response(
    sitemap,
    {
      headers: {
        "Content-Type": "application/xml"
      }
    }
  );
}

export { GET };
//# sourceMappingURL=_server.ts-BtlrEslv.js.map
