import * as universal from '../entries/pages/__locale__/_layout.ts.js';
import * as server from '../entries/pages/__locale__/_layout.server.ts.js';

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/[[locale]]/+layout.ts";
export { server };
export const server_id = "src/routes/[[locale]]/+layout.server.ts";
export const imports = ["_app/immutable/nodes/3.CIONd69m.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/Bzak7iHL.js"];
export const stylesheets = [];
export const fonts = [];
