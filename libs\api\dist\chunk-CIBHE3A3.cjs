"use strict";Object.defineProperty(exports, "__esModule", {value: true});

var _chunkQ7SFCCGTcjs = require('./chunk-Q7SFCCGT.cjs');

// src/sitemap.ts
var sitemap_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, sitemap_exports, {
  GetSitemapGenerationDataOutputSchema: () => GetSitemapGenerationDataOutputSchema
});
var _zod = require('zod');

// src/common.ts
var common_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, common_exports, {
  FormDataToObject: () => FormDataToObject,
  ImageSchema: () => ImageSchema,
  ImagesSchema: () => ImagesSchema,
  JsonStringToObject: () => JsonStringToObject,
  LocalizationLocaleSchema: () => LocalizationLocaleSchema,
  LocalizationLocalesSchema: () => LocalizationLocalesSchema,
  LocalizationSchema: () => LocalizationSchema,
  LocalizationsSchema: () => LocalizationsSchema,
  ObjectWithIdSchema: () => ObjectWithIdSchema,
  PaginationSchema: () => PaginationSchema,
  WebsiteLocaleSchema: () => WebsiteLocaleSchema,
  createdAt: () => createdAt,
  deletedAt: () => deletedAt,
  email: () => email,
  id: () => id,
  idOrNull: () => idOrNull,
  imageUrl: () => imageUrl,
  maybeImageUrl: () => maybeImageUrl,
  pagination: () => pagination,
  parseInput: () => parseInput,
  parseUnknown: () => parseUnknown,
  query: () => query,
  searchIds: () => searchIds,
  searchQuery: () => searchQuery,
  stringToDate: () => stringToDate,
  updatedAt: () => updatedAt,
  url: () => url
});

// src/consts.ts
var consts_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, consts_exports, {
  ALLOWED_IMAGE_FILE_TYPES: () => ALLOWED_IMAGE_FILE_TYPES,
  MAX_IMAGE_FILE_SIZE: () => MAX_IMAGE_FILE_SIZE,
  PAGE_SIZE: () => PAGE_SIZE
});
var PAGE_SIZE = 20;
var ALLOWED_IMAGE_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
var MAX_IMAGE_FILE_SIZE = 5 * 1024 * 1024;

// src/common.ts

var id = _zod.z.string().nanoid();
var idOrNull = id.nullable().default(null);
var url = _zod.z.string().url();
var email = _zod.z.string().email();
var query = _zod.z.string().nonempty();
var imageUrl = _zod.z.string().nonempty();
var maybeImageUrl = imageUrl.nullable();
var createdAt = _zod.z.date();
var updatedAt = _zod.z.date();
var deletedAt = _zod.z.date().nullable();
var searchIds = _zod.z.array(id).min(1);
var searchQuery = _zod.z.string().nonempty();
var stringToDate = _zod.z.union([_zod.z.number(), _zod.z.string(), _zod.z.date()]).pipe(_zod.z.coerce.date());
function JsonStringToObject(schema) {
  return _zod.z.string().transform((value) => JSON.parse(value)).pipe(_zod.z.object(schema));
}
function FormDataToObject(schema) {
  return _zod.z.object({
    data: JsonStringToObject(schema)
  });
}
var ObjectWithIdSchema = _zod.z.object({ id });
var WebsiteLocaleSchema = _zod.z.enum(["en", "ru"]);
var LocalizationLocaleSchema = _zod.z.enum(["en", "ru"]);
var LocalizationLocalesSchema = _zod.z.array(LocalizationLocaleSchema).min(1);
var LocalizationSchema = _zod.z.object({
  locale: LocalizationLocaleSchema,
  value: _zod.z.string().nonempty()
});
var LocalizationsSchema = _zod.z.array(LocalizationSchema);
var ImageSchema = _zod.z.object({
  id,
  url: _zod.z.string(),
  createdAt: stringToDate,
  updatedAt: stringToDate
});
var ImagesSchema = _zod.z.array(ImageSchema);
var pagination = {
  offset: _zod.z.coerce.number().int().default(0),
  limit: _zod.z.coerce.number().int().positive().max(100).default(PAGE_SIZE),
  page: _zod.z.coerce.number().int().positive().default(1),
  size: _zod.z.coerce.number().int().positive().max(100).default(PAGE_SIZE)
};
var PaginationSchema = _zod.z.object({
  page: pagination.page,
  size: pagination.size
}).default({
  page: 1,
  size: PAGE_SIZE
});
function parseInput(schema, value) {
  return schema.parse(value);
}
function parseUnknown(schema, value) {
  return schema.parse(value);
}

// src/sitemap.ts
var GetSitemapGenerationDataOutputSchema = _zod.z.object({
  communeIds: _zod.z.array(id),
  reactorPostIds: _zod.z.array(id),
  reactorHubIds: _zod.z.array(id),
  reactorCommunityIds: _zod.z.array(id)
});

// src/auth.ts
var auth_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, auth_exports, {
  SendOtpInputSchema: () => SendOtpInputSchema,
  SendOtpOutputSchema: () => SendOtpOutputSchema,
  SigninInputSchema: () => SigninInputSchema,
  SignupInputSchema: () => SignupInputSchema,
  SuccessfulOutputSchema: () => SuccessfulOutputSchema,
  otp: () => otp
});


// src/user.ts
var user_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, user_exports, {
  CreateUserTitleInputSchema: () => CreateUserTitleInputSchema,
  DeleteUserInviteInputSchema: () => DeleteUserInviteInputSchema,
  GetMeOutputSchema: () => GetMeOutputSchema,
  GetUserInvitesInputSchema: () => GetUserInvitesInputSchema,
  GetUserInvitesOutputSchema: () => GetUserInvitesOutputSchema,
  GetUserNoteInputSchema: () => GetUserNoteInputSchema,
  GetUserNoteOutputSchema: () => GetUserNoteOutputSchema,
  GetUserOutputSchema: () => GetUserOutputSchema,
  GetUserTitlesInputSchema: () => GetUserTitlesInputSchema,
  GetUserTitlesOutputSchema: () => GetUserTitlesOutputSchema,
  GetUsersInputSchema: () => GetUsersInputSchema,
  GetUsersOutputSchema: () => GetUsersOutputSchema,
  SimpleUserSchema: () => SimpleUserSchema,
  UpdateUserInputSchema: () => UpdateUserInputSchema,
  UpdateUserNoteInputSchema: () => UpdateUserNoteInputSchema,
  UpdateUserTitleInputSchema: () => UpdateUserTitleInputSchema,
  UpsertUserInviteInputSchema: () => UpsertUserInviteInputSchema,
  UserRoleSchema: () => UserRoleSchema,
  userDescription: () => userDescription,
  userImage: () => userImage,
  userName: () => userName,
  userNoteText: () => userNoteText,
  userTitleColor: () => userTitleColor,
  userTitleIsActive: () => userTitleIsActive,
  userTitleName: () => userTitleName
});

var userName = LocalizationsSchema.min(1);
var userDescription = LocalizationsSchema;
var userImage = imageUrl.nullable();
var userTitleName = LocalizationsSchema.min(1);
var userTitleIsActive = _zod.z.boolean();
var userTitleColor = _zod.z.string().nonempty().nullable();
var userNoteText = _zod.z.string().nonempty();
var UserRoleSchema = _zod.z.enum([
  "admin",
  "moderator",
  "user"
]);
var SimpleUserSchema = _zod.z.object({
  id,
  name: userName,
  image: userImage
});
var GetMeOutputSchema = _zod.z.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: imageUrl.nullable(),
  createdAt,
  updatedAt
});
var GetUsersInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetUserOutputSchema = _zod.z.object({
  id,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: userImage,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetUsersOutputSchema = _zod.z.array(GetUserOutputSchema);
var UpdateUserInputSchema = _zod.z.object({
  id,
  name: userName.optional(),
  description: userDescription.optional()
});
var CreateUserTitleInputSchema = _zod.z.object({
  userId: id,
  name: userTitleName,
  isActive: userTitleIsActive,
  color: userTitleColor
});
var UpdateUserTitleInputSchema = _zod.z.object({
  id,
  name: userTitleName.optional(),
  isActive: userTitleIsActive.optional(),
  color: userTitleColor.optional()
});
var GetUserTitlesInputSchema = _zod.z.object({
  userId: id,
  ids: searchIds.optional(),
  isActive: userTitleIsActive.optional()
});
var GetUserTitlesOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    userId: id,
    name: userTitleName,
    isActive: userTitleIsActive,
    color: userTitleColor,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var GetUserNoteInputSchema = _zod.z.object({
  userId: id
});
var GetUserNoteOutputSchema = _zod.z.object({
  text: userNoteText.nullable()
});
var UpdateUserNoteInputSchema = _zod.z.object({
  userId: id,
  text: userNoteText.nullable()
});
var GetUserInvitesInputSchema = _zod.z.object({
  pagination: PaginationSchema
});
var GetUserInvitesOutputSchema = _zod.z.array(_zod.z.object({
  id,
  email,
  name: _zod.z.string().nonempty().nullable(),
  locale: LocalizationLocaleSchema,
  isUsed: _zod.z.boolean()
}));
var UpsertUserInviteInputSchema = _zod.z.object({
  email,
  name: _zod.z.string().nonempty().nullable(),
  locale: LocalizationLocaleSchema
});
var DeleteUserInviteInputSchema = _zod.z.object({
  id
});

// src/auth.ts
var otp = _zod.z.string().nonempty().length(6);
var SendOtpInputSchema = _zod.z.object({
  email
});
var SendOtpOutputSchema = _zod.z.object({
  isSent: _zod.z.boolean()
});
var SignupInputSchema = _zod.z.object({
  referrerId: id.nullable(),
  email,
  otp
});
var SigninInputSchema = _zod.z.object({
  email,
  otp
});
var SuccessfulOutputSchema = _zod.z.object({
  id,
  email,
  role: UserRoleSchema
});

// src/commune.ts
var commune_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, commune_exports, {
  CommuneInvitationStatusSchema: () => CommuneInvitationStatusSchema,
  CommuneJoinRequestStatusSchema: () => CommuneJoinRequestStatusSchema,
  CommuneMemberTypeSchema: () => CommuneMemberTypeSchema,
  CreateCommuneInputSchema: () => CreateCommuneInputSchema,
  CreateCommuneInvitationInputSchema: () => CreateCommuneInvitationInputSchema,
  CreateCommuneJoinRequestInputSchema: () => CreateCommuneJoinRequestInputSchema,
  CreateCommuneMemberInputSchema: () => CreateCommuneMemberInputSchema,
  GetCommuneInvitationsInputSchema: () => GetCommuneInvitationsInputSchema,
  GetCommuneInvitationsOutputSchema: () => GetCommuneInvitationsOutputSchema,
  GetCommuneJoinRequestsInputSchema: () => GetCommuneJoinRequestsInputSchema,
  GetCommuneJoinRequestsOutputSchema: () => GetCommuneJoinRequestsOutputSchema,
  GetCommuneMemberOutputSchema: () => GetCommuneMemberOutputSchema,
  GetCommuneMembersInputSchema: () => GetCommuneMembersInputSchema,
  GetCommuneMembersOutputSchema: () => GetCommuneMembersOutputSchema,
  GetCommuneOutputSchema: () => GetCommuneOutputSchema,
  GetCommunesInputSchema: () => GetCommunesInputSchema,
  GetCommunesOutputSchema: () => GetCommunesOutputSchema,
  TransferHeadStatusInputSchema: () => TransferHeadStatusInputSchema,
  UpdateCommuneInputSchema: () => UpdateCommuneInputSchema,
  communeDescription: () => communeDescription,
  communeMemberActorType: () => communeMemberActorType,
  communeMemberName: () => communeMemberName,
  communeName: () => communeName
});

var CommuneMemberTypeSchema = _zod.z.enum(["user"]);
var communeName = LocalizationsSchema.min(1);
var communeDescription = LocalizationsSchema;
var communeMemberActorType = CommuneMemberTypeSchema;
var communeMemberName = _zod.z.union([userName, communeName]);
var TransferHeadStatusInputSchema = _zod.z.object({
  communeId: id,
  newHeadUserId: id
});
var GetCommunesInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  userId: id
}).partial();
var GetCommuneOutputSchema = _zod.z.object({
  id,
  name: communeName,
  description: communeDescription,
  headMember: _zod.z.object({
    actorType: communeMemberActorType,
    actorId: id,
    name: communeMemberName,
    image: maybeImageUrl
  }),
  memberCount: _zod.z.number().int().positive(),
  image: maybeImageUrl,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetCommunesOutputSchema = _zod.z.array(GetCommuneOutputSchema);
var CreateCommuneInputSchema = _zod.z.object({
  headUserId: id.optional(),
  name: communeName,
  description: communeDescription
});
var UpdateCommuneInputSchema = _zod.z.object({
  id,
  name: communeName.optional(),
  description: communeDescription.optional()
});
var GetCommuneMembersInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  communeId: id
});
var GetCommuneMemberOutputSchema = _zod.z.object({
  id,
  actorType: communeMemberActorType,
  actorId: id,
  name: communeMemberName,
  image: maybeImageUrl,
  createdAt,
  deletedAt: deletedAt.nullable()
});
var GetCommuneMembersOutputSchema = _zod.z.array(GetCommuneMemberOutputSchema);
var CreateCommuneMemberInputSchema = _zod.z.object({
  communeId: id,
  userId: id
});
var CommuneInvitationStatusSchema = _zod.z.enum(["pending", "accepted", "rejected", "expired"]);
var GetCommuneInvitationsInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneInvitationsOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneInvitationStatusSchema,
    createdAt: _zod.z.date(),
    updatedAt: _zod.z.date()
  })
);
var CreateCommuneInvitationInputSchema = _zod.z.object({
  communeId: id,
  userId: id
});
var CommuneJoinRequestStatusSchema = _zod.z.enum(["pending", "accepted", "rejected"]);
var GetCommuneJoinRequestsInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneJoinRequestsOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneJoinRequestStatusSchema,
    createdAt: _zod.z.date(),
    updatedAt: _zod.z.date()
  })
);
var CreateCommuneJoinRequestInputSchema = _zod.z.object({
  communeId: id,
  userId: id
});

// src/reactor.ts
var reactor_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, reactor_exports, {
  AnonimifyCommentInputSchema: () => AnonimifyCommentInputSchema,
  CommentEntityTypeSchema: () => CommentEntityTypeSchema,
  CreateCommentInputSchema: () => CreateCommentInputSchema,
  CreateCommunityInputSchema: () => CreateCommunityInputSchema,
  CreateHubInputSchema: () => CreateHubInputSchema,
  CreateLensInputSchema: () => CreateLensInputSchema,
  CreatePostInputSchema: () => CreatePostInputSchema,
  DeleteCommentInputSchema: () => DeleteCommentInputSchema,
  DeletePostInputSchema: () => DeletePostInputSchema,
  GetCommentsInputSchema: () => GetCommentsInputSchema,
  GetCommentsOutputSchema: () => GetCommentsOutputSchema,
  GetCommunitiesInputSchema: () => GetCommunitiesInputSchema,
  GetCommunitiesOutputSchema: () => GetCommunitiesOutputSchema,
  GetHubsInputSchema: () => GetHubsInputSchema,
  GetHubsOutputSchema: () => GetHubsOutputSchema,
  GetLensesOutputSchema: () => GetLensesOutputSchema,
  GetPostImagesInputSchema: () => GetPostImagesInputSchema,
  GetPostImagesOutputSchema: () => GetPostImagesOutputSchema,
  GetPostOutputSchema: () => GetPostOutputSchema,
  GetPostsInputSchema: () => GetPostsInputSchema,
  GetPostsOutputSchema: () => GetPostsOutputSchema,
  PostImageSchema: () => PostImageSchema,
  PostUsefulnessSchema: () => PostUsefulnessSchema,
  RatingSchema: () => RatingSchema,
  RatingTypeSchema: () => RatingTypeSchema,
  UpdateCommentInputSchema: () => UpdateCommentInputSchema,
  UpdateCommentRatingInputSchema: () => UpdateCommentRatingInputSchema,
  UpdateCommentRatingOutputSchema: () => UpdateCommentRatingOutputSchema,
  UpdateCommunityInputSchema: () => UpdateCommunityInputSchema,
  UpdateHubInputSchema: () => UpdateHubInputSchema,
  UpdateLensInputSchema: () => UpdateLensInputSchema,
  UpdatePostInputSchema: () => UpdatePostInputSchema,
  UpdatePostRatingInputSchema: () => UpdatePostRatingInputSchema,
  UpdatePostRatingOutputSchema: () => UpdatePostRatingOutputSchema,
  UpdatePostUsefulnessInputSchema: () => UpdatePostUsefulnessInputSchema,
  UpdatePostUsefulnessOutputSchema: () => UpdatePostUsefulnessOutputSchema,
  commentBody: () => commentBody,
  communityDescription: () => communityDescription,
  communityImage: () => communityImage,
  communityName: () => communityName,
  hubDescription: () => hubDescription,
  hubImage: () => hubImage,
  hubName: () => hubName,
  lensCode: () => lensCode,
  lensName: () => lensName,
  postBody: () => postBody,
  postTitle: () => postTitle,
  postUsefulness: () => postUsefulness
});


// src/tag.ts
var tag_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, tag_exports, {
  CreateTagInputSchema: () => CreateTagInputSchema,
  GetTagsInputSchema: () => GetTagsInputSchema,
  GetTagsOutputSchema: () => GetTagsOutputSchema,
  UpdateTagInputSchema: () => UpdateTagInputSchema,
  tagName: () => tagName
});

var tagName = LocalizationsSchema.min(1);
var GetTagsInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetTagsOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    name: tagName,
    deletedAt: deletedAt.optional()
  })
);
var CreateTagInputSchema = _zod.z.object({
  name: tagName
});
var UpdateTagInputSchema = _zod.z.object({
  id,
  name: tagName
});

// src/reactor.ts
var RatingTypeSchema = _zod.z.enum(["like", "dislike"]);
var RatingSchema = _zod.z.object({
  likes: _zod.z.number().int().nonnegative(),
  dislikes: _zod.z.number().int().nonnegative(),
  status: RatingTypeSchema.nullable()
});
var hubName = LocalizationsSchema.min(1);
var hubDescription = LocalizationsSchema.min(1);
var hubImage = maybeImageUrl;
var communityName = LocalizationsSchema.min(1);
var communityDescription = LocalizationsSchema.min(1);
var communityImage = maybeImageUrl;
var postUsefulness = _zod.z.number().int().min(0).max(10);
var PostUsefulnessSchema = _zod.z.object({
  value: postUsefulness.nullable(),
  count: _zod.z.number().int().nonnegative(),
  totalValue: _zod.z.number().min(0).max(10).nullable()
});
var postTitle = LocalizationsSchema.min(1);
var postBody = LocalizationsSchema.min(1);
var PostImageSchema = _zod.z.object({
  id,
  url: imageUrl
});
var GetPostOutputSchema = _zod.z.object({
  id,
  hub: _zod.z.object({
    id,
    name: hubName,
    image: hubImage
  }).nullable(),
  community: _zod.z.object({
    id,
    name: communityName,
    image: communityImage
  }).nullable(),
  author: SimpleUserSchema,
  rating: RatingSchema,
  usefulness: PostUsefulnessSchema,
  title: postTitle,
  body: postBody,
  tags: _zod.z.array(
    _zod.z.object({
      id,
      name: tagName
    })
  ),
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetPostsInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  id: id.optional(),
  lensId: id.nullable()
});
var GetPostsOutputSchema = _zod.z.array(GetPostOutputSchema);
var GetPostImagesInputSchema = _zod.z.object({
  id
});
var GetPostImagesOutputSchema = _zod.z.array(PostImageSchema);
var CreatePostInputSchema = _zod.z.object({
  hubId: id.nullable(),
  communityId: id.nullable(),
  title: postTitle,
  body: postBody,
  tagIds: _zod.z.array(id),
  imageIds: _zod.z.array(id)
});
var UpdatePostInputSchema = _zod.z.object({
  id,
  title: postTitle.optional(),
  body: postBody.optional(),
  tagIds: _zod.z.array(id).optional(),
  imageIds: _zod.z.array(id).optional()
});
var DeletePostInputSchema = _zod.z.object({
  id,
  reason: _zod.z.string().nonempty().nullable()
});
var UpdatePostRatingInputSchema = _zod.z.object({
  id,
  type: RatingTypeSchema
});
var UpdatePostRatingOutputSchema = RatingSchema;
var UpdatePostUsefulnessInputSchema = _zod.z.object({
  id,
  value: postUsefulness.nullable()
});
var UpdatePostUsefulnessOutputSchema = PostUsefulnessSchema;
var CommentEntityTypeSchema = _zod.z.enum(["post", "comment"]);
var commentBody = LocalizationsSchema.min(1);
var GetCommentsInputSchema = _zod.z.union([
  _zod.z.object({
    id,
    entityType: _zod.z.never().optional(),
    entityId: _zod.z.never().optional()
  }),
  _zod.z.object({
    id: _zod.z.never().optional(),
    entityType: CommentEntityTypeSchema,
    entityId: id
  })
]);
var GetCommentsOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    path: _zod.z.string().nonempty(),
    author: SimpleUserSchema.nullable(),
    isAnonymous: _zod.z.boolean(),
    anonimityReason: _zod.z.string().nonempty().nullable(),
    rating: RatingSchema,
    body: commentBody.nullable(),
    childrenCount: _zod.z.number().int().nonnegative(),
    deleteReason: _zod.z.string().nonempty().nullable(),
    createdAt,
    updatedAt,
    deletedAt
  })
);
var CreateCommentInputSchema = _zod.z.object({
  entityType: CommentEntityTypeSchema,
  entityId: id,
  body: commentBody
});
var UpdateCommentInputSchema = _zod.z.object({
  id,
  body: commentBody.optional()
});
var DeleteCommentInputSchema = _zod.z.object({
  id,
  reason: _zod.z.string().nonempty().nullable()
});
var UpdateCommentRatingInputSchema = _zod.z.object({
  id,
  type: RatingTypeSchema
});
var UpdateCommentRatingOutputSchema = RatingSchema;
var AnonimifyCommentInputSchema = _zod.z.object({
  id,
  reason: _zod.z.string().nonempty().nullable()
});
var lensName = _zod.z.string().nonempty();
var lensCode = _zod.z.string().nonempty();
var GetLensesOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    name: lensName,
    code: lensCode
  })
);
var CreateLensInputSchema = _zod.z.object({
  name: lensName,
  code: lensCode
});
var UpdateLensInputSchema = _zod.z.object({
  id,
  name: lensName.optional(),
  code: lensCode.optional()
});
var GetHubsInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetHubsOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    headUser: SimpleUserSchema,
    image: hubImage,
    name: hubName,
    description: hubDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateHubInputSchema = _zod.z.object({
  headUserId: id.nullable(),
  name: hubName,
  description: hubDescription
});
var UpdateHubInputSchema = _zod.z.object({
  id,
  name: hubName.optional(),
  description: hubDescription.optional()
});
var GetCommunitiesInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  hubId: id
}).partial();
var GetCommunitiesOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    hub: _zod.z.object({
      id,
      name: hubName,
      image: hubImage
    }).nullable(),
    headUser: SimpleUserSchema,
    image: communityImage,
    name: communityName,
    description: communityDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateCommunityInputSchema = _zod.z.object({
  hubId: id.nullable(),
  headUserId: id.nullable(),
  name: communityName,
  description: communityDescription
});
var UpdateCommunityInputSchema = _zod.z.object({
  id,
  name: communityName.optional(),
  description: communityDescription.optional()
});

// src/rating.ts
var rating_exports = {};
_chunkQ7SFCCGTcjs.__export.call(void 0, rating_exports, {
  CreateUserFeedbackInputSchema: () => CreateUserFeedbackInputSchema,
  GetKarmaPointsInputSchema: () => GetKarmaPointsInputSchema,
  GetKarmaPointsOutputSchema: () => GetKarmaPointsOutputSchema,
  GetUserFeedbacksInputSchema: () => GetUserFeedbacksInputSchema,
  GetUserFeedbacksOutputSchema: () => GetUserFeedbacksOutputSchema,
  GetUserSummaryInputSchema: () => GetUserSummaryInputSchema,
  GetUserSummaryOutputSchema: () => GetUserSummaryOutputSchema,
  SpendKarmaPointInputSchema: () => SpendKarmaPointInputSchema,
  karmaPointComment: () => karmaPointComment,
  karmaPointQuantity: () => karmaPointQuantity,
  userFeedbackText: () => userFeedbackText,
  userFeedbackValue: () => userFeedbackValue
});

var karmaPointQuantity = _zod.z.number().int();
var karmaPointComment = LocalizationsSchema.min(1);
var GetKarmaPointsInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  userId: id
});
var GetKarmaPointsOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    author: SimpleUserSchema,
    quantity: karmaPointQuantity,
    comment: karmaPointComment
  })
);
var SpendKarmaPointInputSchema = _zod.z.object({
  sourceUserId: id,
  targetUserId: id,
  quantity: karmaPointQuantity,
  comment: karmaPointComment
});
var userFeedbackValue = _zod.z.number().int().min(0).max(10);
var userFeedbackText = LocalizationsSchema.min(1);
var GetUserFeedbacksInputSchema = _zod.z.object({
  pagination: PaginationSchema,
  userId: id
});
var GetUserFeedbacksOutputSchema = _zod.z.array(
  _zod.z.object({
    id,
    author: SimpleUserSchema.nullable(),
    isAnonymous: _zod.z.boolean(),
    value: userFeedbackValue,
    text: userFeedbackText
  })
);
var CreateUserFeedbackInputSchema = _zod.z.object({
  sourceUserId: id,
  targetUserId: id,
  value: userFeedbackValue,
  isAnonymous: _zod.z.boolean(),
  text: userFeedbackText
});
var GetUserSummaryInputSchema = _zod.z.object({
  userId: id
});
var GetUserSummaryOutputSchema = _zod.z.object({
  rating: _zod.z.number().int(),
  karma: _zod.z.number().int(),
  rate: _zod.z.number().min(0).max(10).nullable()
});











exports.consts_exports = consts_exports; exports.common_exports = common_exports; exports.sitemap_exports = sitemap_exports; exports.user_exports = user_exports; exports.auth_exports = auth_exports; exports.commune_exports = commune_exports; exports.tag_exports = tag_exports; exports.reactor_exports = reactor_exports; exports.rating_exports = rating_exports;
//# sourceMappingURL=chunk-CIBHE3A3.cjs.map