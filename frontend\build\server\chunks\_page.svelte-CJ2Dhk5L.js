import { u as push, Q as copy_payload, T as assign_payload, w as pop, x as head, z as escape_html, y as attr, N as ensure_array_like, G as attr_style } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import { f as fetchWithAuth } from './fetch-with-auth-DyBoKb7G.js';
import '@formatjs/intl-localematcher';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import './index-CT944rr3.js';
import { f as formatDate } from './format-date-DgRnEWcB.js';
import { M as Modal } from './modal-BDhz9azZ.js';
import { L as Localized_input } from './localized-input-BFX4O5ct.js';
import { L as Localized_textarea } from './localized-textarea-SdDnJXwN.js';
import './schema-CmMg_B_X.js';
import './client-BUddp2Wf.js';
import './index2-DkUtb91y.js';

/* empty css                                                                              */
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Hub — Reactor of Commune" },
      head: "Head",
      createdOn: "Created on",
      editHub: "Edit Hub",
      uploadImage: "Upload Image",
      deleteImage: "Delete Image",
      communities: "Communities",
      noCommunities: "No communities found",
      loadingMoreCommunities: "Loading more communities...",
      editHubTitle: "Edit Hub",
      hubName: "Hub Name",
      hubDescription: "Hub Description",
      hubNamePlaceholder: "Enter hub name",
      hubDescriptionPlaceholder: "Enter hub description",
      save: "Save",
      cancel: "Cancel",
      saving: "Saving...",
      hubUpdatedSuccess: "Hub updated successfully!",
      errorUpdatingHub: "Failed to update hub",
      required: "This field is required",
      uploadImageTitle: "Upload Hub Image",
      upload: "Upload",
      uploading: "Uploading...",
      imageUploadedSuccess: "Image uploaded successfully!",
      errorUploadingImage: "Failed to upload image",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileType: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      uploadImageMaxSize: "Upload an image (JPG, PNG, WebP), max 5MB.",
      confirmDeleteImage: "Are you sure you want to delete this image?",
      deleteImageTitle: "Delete Image",
      delete: "Delete",
      deleting: "Deleting...",
      imageDeletedSuccess: "Image deleted successfully!",
      errorDeletingImage: "Failed to delete image"
    },
    ru: {
      _page: {
        title: "Хаб — Реактор Коммуны"
      },
      head: "Глава",
      createdOn: "Создан",
      editHub: "Редактировать хаб",
      uploadImage: "Загрузить изображение",
      deleteImage: "Удалить изображение",
      communities: "Сообщества",
      noCommunities: "Сообщества не найдены",
      loadingMoreCommunities: "Загружаем больше сообществ...",
      editHubTitle: "Редактировать хаб",
      hubName: "Название хаба",
      hubDescription: "Описание хаба",
      hubNamePlaceholder: "Введите название хаба",
      hubDescriptionPlaceholder: "Введите описание хаба",
      save: "Сохранить",
      cancel: "Отмена",
      saving: "Сохранение...",
      hubUpdatedSuccess: "Хаб успешно обновлен!",
      errorUpdatingHub: "Не удалось обновить хаб",
      required: "Это поле обязательно",
      uploadImageTitle: "Загрузить изображение хаба",
      upload: "Загрузить",
      uploading: "Загрузка...",
      imageUploadedSuccess: "Изображение загружено успешно!",
      errorUploadingImage: "Не удалось загрузить изображение",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      uploadImageMaxSize: "Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",
      confirmDeleteImage: "Вы уверены, что хотите удалить это изображение?",
      deleteImageTitle: "Удалить изображение",
      delete: "Удалить",
      deleting: "Удаление...",
      imageDeletedSuccess: "Изображение удалено успешно!",
      errorDeletingImage: "Не удалось удалить изображение"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let hub = data.hub;
  let showEditModal = false;
  let isUpdating = false;
  let updateError = null;
  let updateSuccess = null;
  let hubName = [];
  let hubDescription = [];
  let showUploadModal = false;
  let isUploading = false;
  let uploadError = null;
  let uploadSuccess = null;
  let selectedFile = null;
  let previewUrl = null;
  let showDeleteImageModal = false;
  let isDeleting = false;
  let deleteError = null;
  let deleteSuccess = null;
  let communities = data.communities;
  let isHasMoreCommunities = data.isHasMoreCommunities;
  function closeEditModal() {
    showEditModal = false;
  }
  function validateEditForm() {
    if (!hubName.some((item) => item.value.trim().length > 0)) {
      updateError = t.required;
      return false;
    }
    if (!hubDescription.some((item) => item.value.trim().length > 0)) {
      updateError = t.required;
      return false;
    }
    return true;
  }
  async function handleUpdateHub() {
    if (!validateEditForm()) return;
    isUpdating = true;
    updateError = null;
    updateSuccess = null;
    try {
      await api.reactor.hub.patch({ id: hub.id, name: hubName, description: hubDescription });
      updateSuccess = t.hubUpdatedSuccess;
      setTimeout(
        () => {
          refresh();
        },
        1500
      );
    } catch (err) {
      updateError = err instanceof Error ? err.message : t.errorUpdatingHub;
      console.error(err);
    } finally {
      isUpdating = false;
    }
  }
  function closeUploadModal() {
    showUploadModal = false;
    selectedFile = null;
    previewUrl = null;
  }
  async function handleUploadImage() {
    if (!selectedFile) {
      uploadError = t.pleaseSelectImage;
      return;
    }
    isUploading = true;
    uploadError = null;
    uploadSuccess = null;
    try {
      const formData = new FormData();
      formData.append("image", selectedFile);
      const response = await fetchWithAuth(`/api/reactor/hub/${hub.id}/image`, { method: "PUT", body: formData });
      if (!response.ok) {
        throw new Error(`${t.errorUploadingImage}: ${response.statusText}`);
      }
      uploadSuccess = t.imageUploadedSuccess;
      setTimeout(
        () => {
          window.location.reload();
        },
        1500
      );
    } catch (err) {
      uploadError = err instanceof Error ? err.message : t.errorUploadingImage;
      console.error(err);
    } finally {
      isUploading = false;
    }
  }
  function closeDeleteImageModal() {
    showDeleteImageModal = false;
  }
  async function handleDeleteImage() {
    isDeleting = true;
    deleteError = null;
    deleteSuccess = null;
    try {
      const response = await fetchWithAuth(`/api/reactor/hub/${hub.id}/image`, { method: "DELETE" });
      if (!response.ok) {
        throw new Error(`${t.errorDeletingImage}: ${response.statusText}`);
      }
      deleteSuccess = t.imageDeletedSuccess;
      setTimeout(
        () => {
          window.location.reload();
        },
        1500
      );
    } catch (err) {
      deleteError = err instanceof Error ? err.message : t.errorDeletingImage;
      console.error(err);
    } finally {
      isDeleting = false;
    }
  }
  function refresh() {
    window.location.reload();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(getAppropriateLocalization(hub.name) || "Hub")} — ${escape_html(t._page.title)}</title>`;
    });
    $$payload2.out.push(`<div class="container my-4 mb-5"><div class="row mb-4"><div class="col-md-4 col-lg-3 mb-3"><div class="hub-image-container svelte-19f9mw5">`);
    if (hub.image) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<img${attr("src", `/images/${hub.image}`)}${attr("alt", getAppropriateLocalization(hub.name) || "Hub")} class="hub-image svelte-19f9mw5"/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="hub-image-placeholder svelte-19f9mw5"><i class="bi bi-collection fs-1 text-muted"></i></div>`);
    }
    $$payload2.out.push(`<!--]--></div> `);
    if (data.canEdit) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="mt-3 d-grid gap-2"><button class="btn btn-outline-primary btn-sm"><i class="bi bi-upload me-1"></i> ${escape_html(t.uploadImage)}</button> `);
      if (hub.image) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<button class="btn btn-outline-danger btn-sm"><i class="bi bi-trash me-1"></i> ${escape_html(t.deleteImage)}</button>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div> <div class="col-md-8 col-lg-9"><div class="d-flex justify-content-between align-items-start mb-3"><h1 class="mb-0">${escape_html(getAppropriateLocalization(hub.name) || "Unknown Hub")}</h1> `);
    if (data.canEdit) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<button class="btn btn-primary"><i class="bi bi-pencil me-1"></i> ${escape_html(t.editHub)}</button>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div> <p class="text-muted mb-3 fs-5">${escape_html(getAppropriateLocalization(hub.description) || "")}</p> <div class="row g-3"><div class="col-sm-6"><div class="d-flex align-items-center"><div class="me-3">`);
    if (hub.headUser.image) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<img${attr("src", `/images/${hub.headUser.image}`)}${attr("alt", getAppropriateLocalization(hub.headUser.name))} class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>`);
    }
    $$payload2.out.push(`<!--]--></div> <div><a${attr("href", toLocaleHref(`/users/${hub.headUser.id}`))} class="fw-medium" style="text-decoration: none;">${escape_html(getAppropriateLocalization(hub.headUser.name))}</a></div></div></div> <div class="col-sm-6"><div class="small text-muted">${escape_html(t.createdOn)}:</div> <div class="fw-medium">${escape_html(formatDate(hub.createdAt, locale))}</div></div></div></div></div> <div class="mt-5"><h2 class="mb-4">${escape_html(t.communities)}</h2> `);
    if (communities.length === 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-5"><i class="bi bi-collection fs-1 text-muted mb-3"></i> <p class="text-muted">${escape_html(t.noCommunities)}</p></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      const each_array = ensure_array_like(communities);
      $$payload2.out.push(`<div class="row g-4"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let community = each_array[$$index];
        $$payload2.out.push(`<div class="col-md-6 col-lg-4"><div class="card shadow-sm h-100 svelte-19f9mw5"><div class="card-body d-flex flex-column"><div class="community-card-image-container mb-3 svelte-19f9mw5">`);
        if (community.image) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<img${attr("src", `/images/${community.image}`)}${attr("alt", getAppropriateLocalization(community.name) || "Community")} class="community-card-image svelte-19f9mw5"/>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<div class="community-card-image-placeholder svelte-19f9mw5"><i class="bi bi-people fs-2 text-muted"></i></div>`);
        }
        $$payload2.out.push(`<!--]--></div> <h5 class="card-title mb-2"><a${attr("href", toLocaleHref(`/reactor/communities/${community.id}`))} style="text-decoration: none;">${escape_html(getAppropriateLocalization(community.name) || "No name?")}</a></h5> <p class="card-text text-muted mb-3 flex-grow-1">${escape_html(getAppropriateLocalization(community.description) || "")}</p> <div class="d-flex align-items-center mt-auto"><div class="me-2">`);
        if (community.headUser.image) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<img${attr("src", `/images/${community.headUser.image}`)}${attr("alt", getAppropriateLocalization(community.headUser.name))} class="rounded-circle" style="width: 32px; height: 32px; object-fit: cover;"/>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;"><i class="bi bi-person-fill text-white"></i></div>`);
        }
        $$payload2.out.push(`<!--]--></div> <div class="small"><div class="fw-medium">${escape_html(getAppropriateLocalization(community.headUser.name))}</div></div></div></div></div></div>`);
      }
      $$payload2.out.push(`<!--]--></div> `);
      if (isHasMoreCommunities) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="text-center py-3 mt-4">`);
        {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]--></div> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div> `);
    if (data.canEdit) {
      $$payload2.out.push("<!--[-->");
      Modal($$payload2, {
        show: showEditModal,
        title: t.editHubTitle,
        onClose: closeEditModal,
        onSubmit: handleUpdateHub,
        submitText: isUpdating ? t.saving : t.save,
        cancelText: t.cancel,
        submitDisabled: isUpdating || !hubName.some((item) => item.value.trim().length > 0) || !hubDescription.some((item) => item.value.trim().length > 0),
        isSubmitting: isUpdating,
        children: ($$payload3) => {
          if (updateError) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(updateError)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> `);
          if (updateSuccess) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(updateSuccess)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> <form>`);
          Localized_input($$payload3, {
            locale,
            id: "hub-name",
            label: t.hubName,
            placeholder: t.hubNamePlaceholder,
            required: true,
            get value() {
              return hubName;
            },
            set value($$value) {
              hubName = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----> `);
          Localized_textarea($$payload3, {
            locale,
            id: "hub-description",
            label: t.hubDescription,
            placeholder: t.hubDescriptionPlaceholder,
            rows: 4,
            required: true,
            get value() {
              return hubDescription;
            },
            set value($$value) {
              hubDescription = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----></form>`);
        }
      });
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (data.canEdit) {
      $$payload2.out.push("<!--[-->");
      Modal($$payload2, {
        show: showUploadModal,
        title: t.uploadImageTitle,
        onClose: closeUploadModal,
        onSubmit: handleUploadImage,
        submitText: isUploading ? t.uploading : t.upload,
        cancelText: t.cancel,
        submitDisabled: !selectedFile || isUploading,
        isSubmitting: isUploading,
        size: "lg",
        children: ($$payload3) => {
          if (uploadSuccess) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(uploadSuccess)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> `);
          if (uploadError) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(uploadError)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> <form><div class="mb-3"><label for="imageInput" class="form-label">${escape_html(t.pleaseSelectImage)}</label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"${attr("disabled", isUploading, true)}/> <p class="form-text text-muted">${escape_html(t.uploadImageMaxSize)}</p> `);
          if (previewUrl) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="mt-3 text-center"><img${attr("src", previewUrl)} alt="Preview" class="img-thumbnail"${attr_style("", { "max-height": "200px" })}/></div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--></div></form>`);
        }
      });
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (data.canEdit && hub.image) {
      $$payload2.out.push("<!--[-->");
      Modal($$payload2, {
        show: showDeleteImageModal,
        title: t.deleteImageTitle,
        onClose: closeDeleteImageModal,
        onSubmit: handleDeleteImage,
        submitText: isDeleting ? t.deleting : t.delete,
        cancelText: t.cancel,
        submitDisabled: isDeleting,
        isSubmitting: isDeleting,
        children: ($$payload3) => {
          if (deleteSuccess) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(deleteSuccess)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> `);
          if (deleteError) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(deleteError)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> <p>${escape_html(t.confirmDeleteImage)}</p>`);
        }
      });
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]-->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CJ2Dhk5L.js.map
