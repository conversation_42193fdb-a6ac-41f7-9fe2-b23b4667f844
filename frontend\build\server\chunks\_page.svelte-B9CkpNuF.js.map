{"version": 3, "file": "_page.svelte-B9CkpNuF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/users/_id_/_page.svelte.js"], "sourcesContent": ["import { x as head, y as attr, z as escape_html, G as attr_class, F as attr_style, J as stringify, w as pop, u as push } from \"../../../../../../chunks/index.js\";\nimport \"../../../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../chunks/exports.js\";\nimport \"../../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { g as getUserRateColor } from \"../../../../../../chunks/get-user-rate-color.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"— Commune\" },\n      userNotFound: \"User not found\",\n      userDetails: \"User Details\",\n      joinedOn: \"Joined on\",\n      dateFormatLocale: \"en-US\",\n      userNote: \"Personal Note\",\n      userNotePlaceholder: \"Write your personal note about this user...\",\n      saved: \"Saved...\",\n      rating: \"Rating\",\n      karma: \"Karma\",\n      rate: \"Rate\",\n      noImage: \"No image available\",\n      userImageAlt: \"User image\",\n      socialOpinion: \"Social Opinion\"\n    },\n    ru: {\n      _page: { title: \"— Коммуна\" },\n      userNotFound: \"Пользователь не найден\",\n      userDetails: \"Информация о пользователе\",\n      joinedOn: \"Дата регистрации\",\n      dateFormatLocale: \"ru-RU\",\n      userNote: \"Личная заметка\",\n      userNotePlaceholder: \"Напишите свою личную заметку об этом пользователе...\",\n      saved: \"Сохранено...\",\n      rating: \"Рейтинг\",\n      karma: \"Карма\",\n      rate: \"Оценка\",\n      noImage: \"Нет доступных изображений\",\n      userImageAlt: \"Изображение пользователя\",\n      socialOpinion: \"Общественное мнение\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const {\n    user,\n    locale,\n    getAppropriateLocalization,\n    ratingSummary,\n    toLocaleHref\n  } = data;\n  const t = i18n[locale];\n  let userNote = data.userNote;\n  const userName = getAppropriateLocalization(user.name);\n  const userDescription = getAppropriateLocalization(user.description);\n  const joinDate = user ? new Date(user.createdAt) : /* @__PURE__ */ new Date();\n  const formattedDate = joinDate.toLocaleDateString(t.dateFormatLocale, { year: \"numeric\", month: \"long\", day: \"numeric\" });\n  const getBadgeClass = (role) => {\n    switch (role) {\n      case \"admin\":\n        return \"bg-danger\";\n      case \"moderator\":\n        return \"bg-warning\";\n      default:\n        return \"bg-primary\";\n    }\n  };\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(userName)} ${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container py-4\"><div class=\"row\"><div class=\"col-lg-8\">`);\n  if (user.image) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div style=\"height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;\"><img${attr(\"src\", `/images/${user.image}`)}${attr(\"alt\", `${t.userImageAlt}`)} style=\"width: 100%; height: 100%; object-fit: contain;\"/></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div class=\"bg-light text-center rounded mb-4 d-flex align-items-center justify-content-center\" style=\"height: 300px;\"><span class=\"text-muted\">${escape_html(t.noImage)}</span></div>`);\n  }\n  $$payload.out.push(`<!--]--> <div class=\"mb-4\"><div class=\"d-flex justify-content-between align-items-center mb-3\"><h2 class=\"mb-0\">${escape_html(userName)}</h2> <span${attr_class(`badge ${getBadgeClass(user.role)}`, \"svelte-1hels8p\")}>${escape_html(user.role)}</span></div> <p class=\"lead text-muted\">${escape_html(userDescription || \"\")}</p></div></div> <div class=\"col-lg-4\"><div class=\"card shadow-sm mb-4\"><div class=\"card-body\"><h5 class=\"card-title\">${escape_html(t.userDetails)}</h5> <hr/> <div class=\"d-flex align-items-center\"><i class=\"bi bi-calendar-date me-2 text-primary\"></i> <span>${escape_html(t.joinedOn)} ${escape_html(formattedDate)}</span></div></div></div> `);\n  if (ratingSummary) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"card shadow-sm mb-4\"><div class=\"card-body\"><h5 class=\"card-title\">${escape_html(t.socialOpinion)}</h5> <hr/> <div class=\"row g-3\"><div class=\"col-4\"><div class=\"rating-block border rounded p-3 text-center svelte-1hels8p\" style=\"border-color: #fd7e14 !important;\"><div class=\"rating-label text-muted small mb-1\">${escape_html(t.rating)}</div> <div class=\"rating-value fw-bold\" style=\"color: #fd7e14; font-size: 1.5rem;\">${escape_html(ratingSummary.rating)}</div></div></div> <div class=\"col-4\"><a${attr(\"href\", toLocaleHref(`/users/${user.id}/karma`))} class=\"karma-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p\" style=\"border-color: #d63384 !important;\"><div class=\"karma-label text-muted small mb-1\">${escape_html(t.karma)}</div> <div class=\"karma-value fw-bold\" style=\"color: #d63384; font-size: 1.5rem;\">${escape_html(ratingSummary.karma)}</div></a></div> <div class=\"col-4\"><a${attr(\"href\", toLocaleHref(`/users/${user.id}/feedback`))} class=\"rate-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p\" style=\"border-color: #6c757d !important;\"><div class=\"rate-label text-muted small mb-1\">${escape_html(t.rate)}</div> <div${attr_class(`rate-value fw-bold ${stringify(ratingSummary.rate === null ? \"text-muted\" : \"\")}`)}${attr_style(`font-size: 1.5rem; ${stringify(ratingSummary.rate !== null ? `color: ${getUserRateColor(ratingSummary.rate)};` : \"\")}`)}>${escape_html(ratingSummary.rate !== null ? ratingSummary.rate.toFixed(1) : \"N/A\")}</div></a></div></div></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"card shadow-sm mb-4\"><div class=\"card-body\"><h5 class=\"card-title\">${escape_html(t.userNote)}</h5> <hr/> <div class=\"mb-2\"><textarea class=\"form-control\" rows=\"4\"${attr(\"placeholder\", t.userNotePlaceholder)}>`);\n  const $$body = escape_html(userNote);\n  if ($$body) {\n    $$payload.out.push(`${$$body}`);\n  }\n  $$payload.out.push(`</textarea></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div></div></div></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;AACnC,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,gBAAgB,EAAE,OAAO;AAC/B,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,mBAAmB,EAAE,6CAA6C;AACxE,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,YAAY,EAAE,YAAY;AAChC,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;AACnC,MAAM,YAAY,EAAE,wBAAwB;AAC5C,MAAM,WAAW,EAAE,2BAA2B;AAC9C,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,gBAAgB,EAAE,OAAO;AAC/B,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,mBAAmB,EAAE,sDAAsD;AACjF,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,OAAO,EAAE,2BAA2B;AAC1C,MAAM,YAAY,EAAE,0BAA0B;AAC9C,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,0BAA0B;AAC9B,IAAI,aAAa;AACjB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,EAAE,MAAM,QAAQ,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD,EAAE,MAAM,eAAe,GAAG,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC;AACtE,EAAE,MAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,IAAI,IAAI,EAAE;AAC/E,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;AAC3H,EAAE,MAAM,aAAa,GAAG,CAAC,IAAI,KAAK;AAClC,IAAI,QAAQ,IAAI;AAChB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,WAAW;AAC1B,MAAM,KAAK,WAAW;AACtB,QAAQ,OAAO,YAAY;AAC3B,MAAM;AACN,QAAQ,OAAO,YAAY;AAC3B;AACA,EAAE,CAAC;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC9F,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mEAAmE,CAAC,CAAC;AAC3F,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wHAAwH,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,gEAAgE,CAAC,CAAC;AAC5R,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gJAAgJ,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AAChN,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gHAAgH,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,sHAAsH,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,+GAA+G,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC3qB,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,sNAAsN,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,oFAAoF,EAAE,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,wCAAwC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,yLAAyL,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,mFAAmF,EAAE,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,uLAAuL,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,aAAa,CAAC,IAAI,KAAK,IAAI,GAAG,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,aAAa,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,IAAI,KAAK,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAC1iD,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wFAAwF,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,qEAAqE,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7P,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC;AACtC,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,CAAC,CAAC;AAC9D,EAAE,GAAG,EAAE;AACP;;;;"}