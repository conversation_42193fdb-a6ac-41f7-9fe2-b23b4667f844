import { u as push, y as attr, G as attr_style, J as attr_class, z as escape_html, w as pop } from './index-0Ke2LYl0.js';
import { L as Locale_switcher } from './locale-switcher-DOwGQW5O.js';
import './current-user-BM0W6LNm.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';

function Header($$payload, $$props) {
  push();
  const i18n = {
    ru: {
      navGap: "mx-auto",
      theLaw: "Право",
      rules: "Правила",
      newEnglish: "Новый английский",
      newCalendar: "Новый календарь",
      reactor: "Реактор",
      users: "Пользователи",
      communes: "Коммуны",
      profile: "Профиль"
    },
    en: {
      navGap: "mx-auto",
      theLaw: "The Law",
      rules: "Rules",
      newEnglish: "New English",
      newCalendar: "New Calendar",
      reactor: "Reactor",
      users: "Users",
      communes: "Communes",
      profile: "Profile"
    }
  };
  const { user, locale, routeLocale, toLocaleHref } = $$props;
  const t = i18n[locale];
  $$payload.out.push(`<nav class="navbar navbar-expand-lg sticky-top compact-navbar svelte-19phhed"><div class="container"><a${attr("href", toLocaleHref("/"))} class="navbar-brand py-0 ps-5"><img src="/images/full-v3-transparent.svg" alt="Site Logo"${attr("height", 60)}${attr("width", 60)}${attr_style("", { width: "auto" })}/></a> <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button> <div class="collapse navbar-collapse" id="navbarNav"><ul class="navbar-nav mx-auto"><li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/the-law"))} class="nav-link">${escape_html(t.theLaw)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/rules"))} class="nav-link">${escape_html(t.rules)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/new-english"))} class="nav-link">${escape_html(t.newEnglish)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/new-calendar"))} class="nav-link">${escape_html(t.newCalendar)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/reactor"))} class="nav-link">${escape_html(t.reactor)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/users"))} class="nav-link">${escape_html(t.users)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-19phhed")}><a${attr("href", toLocaleHref("/communes"))} class="nav-link">${escape_html(t.communes)}</a></li></ul> <ul class="navbar-nav"${attr_style("", { position: "relative" })}><li class="nav-item"><a${attr("href", toLocaleHref("/profile"))} class="btn btn-primary btn-sm">${escape_html(t.profile)}</a></li> <li class="nav-item">`);
  Locale_switcher($$payload, { currentLocale: routeLocale });
  $$payload.out.push(`<!----></li> `);
  if (user?.role === "admin") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<li class="nav-item"${attr_style("", { position: "absolute", right: "-105px" })}><a${attr("href", toLocaleHref("/admin"))} class="btn btn-primary btn-sm">Админка</a></li>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></ul></div></div></nav>`);
  pop();
}
const boostyIcon = "data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cpath%20fill='%23495057'%20d='M2.661%2014.337L6.801%200h6.362L11.88%204.444l-.038.077l-3.378%2011.733h3.15q-1.982%204.934-3.086%207.733c-5.816-.063-7.442-4.228-6.02-9.155M8.554%2024l7.67-11.035h-3.25l2.83-7.073c4.852.508%207.137%204.33%205.791%208.952C20.16%2019.81%2014.344%2024%208.68%2024h-.127z'%20/%3e%3c/svg%3e";
function Footer($$payload, $$props) {
  push();
  const i18n = {
    ru: {
      logoAlt: "Логотип Цифрового Сообщества «Коммуна»",
      title: "О нас",
      about: "Цифровое Сообщество «Коммуна» - где общие ценности создают будущее. Мы строим жизнь в сообществе, которое поддерживает сотрудничество, взаимопомощь и развитие.",
      social: { telegram: "https://t.me/ds_commune_ru" },
      links: {
        title: "Cсылки",
        reactor: "Реактор",
        communes: "Коммуны",
        users: "Пользователи",
        newCalendar: "Новый календарь",
        newEnglish: "Новый английский",
        theLaw: "Право",
        rules: "Правила"
      },
      contactUs: {
        title: "Связаться с нами"
      },
      legal: {
        disclaimer: "Цифровое Сообщество «Коммуна». Все права защищены."
      }
    },
    en: {
      logoAlt: "Digital Society «Commune» Logo",
      title: "About Us",
      about: "Digital Society «Commune» - where common values create the future. We're building a vibrant community dedicated to fostering collaboration, mutual aid, and development.",
      social: { telegram: "https://t.me/ds_commune_en" },
      links: {
        title: "Quick Links",
        reactor: "Reactor",
        communes: "Communes",
        users: "Users",
        newCalendar: "New Calendar",
        newEnglish: "New English",
        theLaw: "The Law",
        rules: "Rules"
      },
      contactUs: { title: "Contact Us" },
      legal: {
        disclaimer: "Digital Society «Commune». All rights reserved."
      }
    }
  };
  const { locale, toLocaleHref } = $$props;
  const t = i18n[locale];
  const contactUsEmail = "<EMAIL>";
  $$payload.out.push(`<footer class="footer svelte-og7cjd"><div class="container"><div class="row gy-4"><div class="col-lg-4 col-md-6"><div class="about-section svelte-og7cjd"><a${attr("href", toLocaleHref("/"))} class="d-inline-block mb-3"><img src="/images/full-v3-transparent.svg"${attr("alt", t.logoAlt)}${attr("width", 80)}${attr("height", 80)} class="footer-logo svelte-og7cjd"/></a> <h5 class="fw-bold mb-3">${escape_html(t.title)}</h5> <p class="text-muted">${escape_html(t.about)}</p> <div class="social-icons svelte-og7cjd"><a${attr("href", t.social.telegram)} class="social-icon svelte-og7cjd" aria-label="Telegram" target="_blank" rel="noopener noreferrer"><i class="bi bi-telegram"></i></a> <a href="https://github.com/ds-commune" class="social-icon svelte-og7cjd" aria-label="GitHub" target="_blank" rel="noopener noreferrer"><i class="bi bi-github"></i></a> <a href="https://boosty.to/ds.commune" class="social-icon svelte-og7cjd" aria-label="Boosty" target="_blank" rel="noopener noreferrer"><img${attr("src", boostyIcon)} alt="Boosty"${attr("width", 24)}${attr("height", 24)}/></a></div></div></div> <div class="col-lg-4 col-md-6"><h5 class="fw-bold mb-3">${escape_html(t.links.title)}</h5> <ul class="list-unstyled quick-links svelte-og7cjd"><li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/the-law"))} class="svelte-og7cjd">${escape_html(t.links.theLaw)}</a></li> <li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/rules"))} class="svelte-og7cjd">${escape_html(t.links.rules)}</a></li> <li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/new-english"))} class="svelte-og7cjd">${escape_html(t.links.newEnglish)}</a></li> <li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/new-calendar"))} class="svelte-og7cjd">${escape_html(t.links.newCalendar)}</a></li> <li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/reactor"))} class="svelte-og7cjd">${escape_html(t.links.reactor)}</a></li> <li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/users"))} class="svelte-og7cjd">${escape_html(t.links.users)}</a></li> <li class="svelte-og7cjd"><a${attr("href", toLocaleHref("/communes"))} class="svelte-og7cjd">${escape_html(t.links.communes)}</a></li></ul></div> <div class="col-lg-4 col-md-12"><h5 class="fw-bold mb-3">${escape_html(t.contactUs.title)}</h5> <ul class="list-unstyled contact-info svelte-og7cjd"><li class="svelte-og7cjd"><i class="bi bi-envelope svelte-og7cjd"></i> <a${attr("href", `mailto:${contactUsEmail}`)} target="_blank" rel="noopener noreferrer">${escape_html(contactUsEmail)}</a></li></ul></div></div> <hr class="divider svelte-og7cjd"/> <div class="copyright svelte-og7cjd"><p class="svelte-og7cjd">${escape_html((/* @__PURE__ */ new Date()).getFullYear())} ${escape_html(t.legal.disclaimer)}</p></div></div></footer>`);
  pop();
}
function _layout($$payload, $$props) {
  const { children, data } = $$props;
  const { user, locale, routeLocale, toLocaleHref } = data;
  $$payload.out.push(`<div class="page-wrapper svelte-100cqw2">`);
  Header($$payload, { user, locale, toLocaleHref, routeLocale });
  $$payload.out.push(`<!----> <main class="container flex-grow-1 mb-5">`);
  children($$payload);
  $$payload.out.push(`<!----></main> `);
  Footer($$payload, { locale, toLocaleHref });
  $$payload.out.push(`<!----></div>`);
}

export { _layout as default };
//# sourceMappingURL=_layout.svelte-mkqezp6n.js.map
