import { u as push, Q as copy_payload, T as assign_payload, w as pop, x as head, z as escape_html, y as attr, N as ensure_array_like } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as goto } from './client-BUddp2Wf.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { f as formatDate } from './format-date-DgRnEWcB.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import { M as Modal } from './modal-BDhz9azZ.js';
import { L as Localized_input } from './localized-input-BFX4O5ct.js';
import { L as Localized_textarea } from './localized-textarea-SdDnJXwN.js';
import { U as User_picker } from './user-picker-3qltaNhi.js';
import './exports-DxMY0jlE.js';
import './index2-DkUtb91y.js';
import './state.svelte-BMxoNtw-.js';
import './schema-CmMg_B_X.js';

function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Hubs — Reactor of Commune" },
      hubs: "Hubs",
      createHub: "Create Hub",
      noHubs: "No hubs found",
      head: "Head",
      errorFetchingHubs: "Failed to fetch hubs",
      errorOccurred: "An error occurred while fetching hubs",
      loadingMore: "Loading more hubs...",
      createdOn: "Created on",
      createHubTitle: "Create New Hub",
      hubName: "Hub Name",
      hubDescription: "Hub Description",
      headUserPlaceholder: "Leave empty to use current user",
      hubNamePlaceholder: "Enter hub name",
      hubDescriptionPlaceholder: "Enter hub description",
      create: "Create",
      cancel: "Cancel",
      creating: "Creating...",
      hubCreatedSuccess: "Hub created successfully!",
      errorCreatingHub: "Failed to create hub",
      required: "This field is required",
      searchPlaceholder: "Search hubs..."
    },
    ru: {
      _page: {
        title: "Хабы — Реактор Коммуны"
      },
      hubs: "Хабы",
      createHub: "Создать хаб",
      noHubs: "Хабы не найдены",
      head: "Глава",
      errorFetchingHubs: "Не удалось загрузить хабы",
      errorOccurred: "Произошла ошибка при загрузке хабов",
      loadingMore: "Загружаем больше хабов...",
      createdOn: "Создан",
      createHubTitle: "Создать новый хаб",
      hubName: "Название хаба",
      hubDescription: "Описание хаба",
      headUserPlaceholder: "Оставьте пустым для использования текущего пользователя",
      hubNamePlaceholder: "Введите название хаба",
      hubDescriptionPlaceholder: "Введите описание хаба",
      create: "Создать",
      cancel: "Отмена",
      creating: "Создаём...",
      hubCreatedSuccess: "Хаб успешно создан!",
      errorCreatingHub: "Не удалось создать хаб",
      required: "Это поле обязательно",
      searchPlaceholder: "Поиск хабов..."
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let hubs = data.hubs;
  let showCreateModal = false;
  let searchInputValue = data.searchQuery || "";
  let isHasMoreHubs = data.isHasMoreHubs;
  let isCreating = false;
  let createError = null;
  let createSuccess = null;
  let hubName = [];
  let hubDescription = [];
  let headUserId = null;
  function closeCreateModal() {
    showCreateModal = false;
  }
  function validateCreateForm() {
    if (!hubName.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }
    if (!hubDescription.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }
    return true;
  }
  async function handleCreateHub() {
    if (!validateCreateForm()) return;
    isCreating = true;
    createError = null;
    createSuccess = null;
    try {
      const { id } = await api.reactor.hub.post({
        headUserId: headUserId || data.user?.id || "",
        name: hubName,
        description: hubDescription
      });
      createSuccess = t.hubCreatedSuccess;
      setTimeout(
        () => {
          goto(toLocaleHref(`/reactor/hubs/${id}`));
        },
        1500
      );
    } catch (err) {
      createError = err instanceof Error ? err.message : t.errorCreatingHub;
      console.error(err);
    } finally {
      isCreating = false;
    }
  }
  function truncateDescription(text, maxLength = 200) {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(t._page.title)}</title>`;
    });
    $$payload2.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4 gap-3"><h1 class="mb-0">${escape_html(t.hubs)}</h1> <div class="d-flex align-items-center gap-3"><div class="search-container svelte-si10zv"><input type="text" class="form-control svelte-si10zv"${attr("placeholder", t.searchPlaceholder)}${attr("value", searchInputValue)} style="min-width: 250px;"/></div> `);
    if (data.user?.role === "admin") {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<button class="btn btn-primary">${escape_html(t.createHub)}</button>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div></div> `);
    if (hubs.length === 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noHubs)}</p></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      const each_array = ensure_array_like(hubs);
      $$payload2.out.push(`<div class="row g-4"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let hub = each_array[$$index];
        $$payload2.out.push(`<div class="col-12"><div class="card shadow-sm h-100 svelte-si10zv"><div class="row g-0 h-100"><div class="col-md-3 col-lg-2"><div class="hub-image-container svelte-si10zv">`);
        if (hub.image) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<img${attr("src", `/images/${hub.image}`)}${attr("alt", getAppropriateLocalization(hub.name) || "Hub")} class="hub-image svelte-si10zv"/>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<div class="hub-image-placeholder svelte-si10zv"><i class="bi bi-collection fs-1 text-muted"></i></div>`);
        }
        $$payload2.out.push(`<!--]--></div></div> <div class="col-md-9 col-lg-10"><div class="card-body d-flex flex-column h-100 p-4"><div class="d-flex justify-content-between align-items-start mb-3"><h4 class="card-title mb-0 flex-grow-1"><a${attr("href", toLocaleHref(`/reactor/hubs/${hub.id}`))} style="text-decoration: none;">${escape_html(getAppropriateLocalization(hub.name) || "No name?")}</a></h4> <small class="text-muted ms-3">${escape_html(t.createdOn)}
                      ${escape_html(formatDate(hub.createdAt, locale))}</small></div> <p class="card-text text-muted mb-3 flex-grow-1">${escape_html(truncateDescription(getAppropriateLocalization(hub.description) || ""))}</p> <div class="d-flex align-items-center"><div class="me-3">`);
        if (hub.headUser.image) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<img${attr("src", `/images/${hub.headUser.image}`)}${attr("alt", getAppropriateLocalization(hub.headUser.name))} class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>`);
        }
        $$payload2.out.push(`<!--]--></div> <div><a${attr("href", toLocaleHref(`/users/${hub.headUser.id}`))} class="fw-medium" style="text-decoration: none;">${escape_html(getAppropriateLocalization(hub.headUser.name))}</a></div></div></div></div></div></div></div>`);
      }
      $$payload2.out.push(`<!--]--></div>`);
    }
    $$payload2.out.push(`<!--]--> `);
    if (isHasMoreHubs) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-3">`);
      {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div> `);
    if (data.user?.role === "admin") {
      $$payload2.out.push("<!--[-->");
      Modal($$payload2, {
        show: showCreateModal,
        title: t.createHubTitle,
        onClose: closeCreateModal,
        onSubmit: handleCreateHub,
        submitText: isCreating ? t.creating : t.create,
        cancelText: t.cancel,
        submitDisabled: isCreating || !hubName.some((item) => item.value.trim().length > 0) || !hubDescription.some((item) => item.value.trim().length > 0),
        isSubmitting: isCreating,
        children: ($$payload3) => {
          if (createError) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(createError)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> `);
          if (createSuccess) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(createSuccess)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> <form>`);
          User_picker($$payload3, {
            locale,
            label: t.head,
            placeholder: t.headUserPlaceholder,
            get selectedUserId() {
              return headUserId;
            },
            set selectedUserId($$value) {
              headUserId = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----> <div class="form-text mb-3">${escape_html(t.headUserPlaceholder)}</div> `);
          Localized_input($$payload3, {
            locale,
            id: "hub-name",
            label: t.hubName,
            placeholder: t.hubNamePlaceholder,
            required: true,
            get value() {
              return hubName;
            },
            set value($$value) {
              hubName = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----> `);
          Localized_textarea($$payload3, {
            locale,
            id: "hub-description",
            label: t.hubDescription,
            placeholder: t.hubDescriptionPlaceholder,
            rows: 4,
            required: true,
            get value() {
              return hubDescription;
            },
            set value($$value) {
              hubDescription = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----></form>`);
        }
      });
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]-->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CZ6wcDMB.js.map
