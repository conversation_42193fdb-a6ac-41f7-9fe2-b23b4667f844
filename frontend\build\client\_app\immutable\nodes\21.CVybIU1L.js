import{e as He}from"../chunks/CVTn1FV4.js";import{g as me}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{p as Je,av as M,aw as Ke,o as Me,g as e,ax as h,f as w,h as We,d as a,t as b,b as D,c as qe,s as r,u as n,$ as Ge,r as t,aR as Qe}from"../chunks/RHWQbow4.js";import{d as Ve,e as Xe,s as i}from"../chunks/BlWcudmi.js";import{i as W}from"../chunks/CtoItwj4.js";import{s as E,a as Ye}from"../chunks/BdpLTtcP.js";import{s as ce}from"../chunks/Cxg-bych.js";import{s as Ze}from"../chunks/CaC9IHEK.js";import"../chunks/DiZKRWcx.js";import{g as ea}from"../chunks/DGxS2cwR.js";const aa=async({fetch:m,params:d,url:_})=>{const{fetcher:g}=me(),[[o],k,N]=await Promise.all([g.user.list.get({ids:[d.id]},{fetch:m,ctx:{url:_}}),g.user.note.get({userId:d.id},{fetch:m,ctx:{url:_}}),g.rating.summary.get({userId:d.id},{fetch:m,ctx:{url:_}})]);if(!o)throw He(404,"User not found");return{user:o,userNote:k.text,ratingSummary:N}},ha=Object.freeze(Object.defineProperty({__proto__:null,load:aa},Symbol.toStringTag,{value:"Module"})),ta=(m,d,_)=>{const g=m.target;h(d,g.value,!0),_()};var ua=w('<div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"><img style="width: 100%; height: 100%; object-fit: contain;"/></div>'),ra=w('<div class="bg-light text-center rounded mb-4 d-flex align-items-center justify-content-center" style="height: 300px;"><span class="text-muted"> </span></div>'),sa=w('<div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title"> </h5> <hr/> <div class="row g-3"><div class="col-4"><div class="rating-block border rounded p-3 text-center svelte-1hels8p" style="border-color: #fd7e14 !important;"><div class="rating-label text-muted small mb-1"> </div> <div class="rating-value fw-bold" style="color: #fd7e14; font-size: 1.5rem;"> </div></div></div> <div class="col-4"><a class="karma-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p" style="border-color: #d63384 !important;"><div class="karma-label text-muted small mb-1"> </div> <div class="karma-value fw-bold" style="color: #d63384; font-size: 1.5rem;"> </div></a></div> <div class="col-4"><a class="rate-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p" style="border-color: #6c757d !important;"><div class="rate-label text-muted small mb-1"> </div> <div> </div></a></div></div></div></div>'),ia=w('<small class="text-muted"> </small>'),oa=w('<div class="container py-4"><div class="row"><div class="col-lg-8"><!> <div class="mb-4"><div class="d-flex justify-content-between align-items-center mb-3"><h2 class="mb-0"> </h2> <span> </span></div> <p class="lead text-muted"> </p></div></div> <div class="col-lg-4"><div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title"> </h5> <hr/> <div class="d-flex align-items-center"><i class="bi bi-calendar-date me-2 text-primary"></i> <span> </span></div></div></div> <!> <div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title"> </h5> <hr/> <div class="mb-2"><textarea class="form-control" rows="4"></textarea></div> <!></div></div></div></div></div>');function xa(m,d){Je(d,!0);const _={en:{_page:{title:"— Commune"},userNotFound:"User not found",userDetails:"User Details",joinedOn:"Joined on",dateFormatLocale:"en-US",userNote:"Personal Note",userNotePlaceholder:"Write your personal note about this user...",saved:"Saved...",rating:"Rating",karma:"Karma",rate:"Rate",noImage:"No image available",userImageAlt:"User image",socialOpinion:"Social Opinion"},ru:{_page:{title:"— Коммуна"},userNotFound:"Пользователь не найден",userDetails:"Информация о пользователе",joinedOn:"Дата регистрации",dateFormatLocale:"ru-RU",userNote:"Личная заметка",userNotePlaceholder:"Напишите свою личную заметку об этом пользователе...",saved:"Сохранено...",rating:"Рейтинг",karma:"Карма",rate:"Оценка",noImage:"Нет доступных изображений",userImageAlt:"Изображение пользователя",socialOpinion:"Общественное мнение"}},{fetcher:g}=me(),o=n(()=>d.data.user),k=n(()=>d.data.locale),N=n(()=>d.data.getAppropriateLocalization),c=n(()=>d.data.ratingSummary),q=n(()=>d.data.toLocaleHref),l=n(()=>_[e(k)]);let p=M(Ke(d.data.userNote)),f=M(null),F=M(!1);Me(()=>{e(p)!==void 0&&h(p,e(p)||"",!0)});const G=async()=>{var u;await g.user.note.put({userId:e(o).id,text:((u=e(p))==null?void 0:u.trim())||null}),h(F,!0),setTimeout(()=>{h(F,!1)},2e3)},_e=()=>{e(f)&&clearTimeout(e(f)),h(f,setTimeout(()=>{G()},3e3),!0)},ge=()=>{e(f)&&(clearTimeout(e(f)),h(f,null)),G()},Q=n(()=>e(N)(e(o).name)),pe=n(()=>e(N)(e(o).description)),fe=n(()=>e(o)?new Date(e(o).createdAt):new Date),be=n(()=>e(fe).toLocaleDateString(e(l).dateFormatLocale,{year:"numeric",month:"long",day:"numeric"})),he=u=>{switch(u){case"admin":return"bg-danger";case"moderator":return"bg-warning";default:return"bg-primary"}};var B=oa();We(u=>{b(()=>Ge.title=`${e(Q)??""} ${e(l)._page.title??""}`)});var V=a(B),A=a(V),X=a(A);{var xe=u=>{var s=ua(),v=a(s);t(s),b(()=>{E(v,"src",`/images/${e(o).image}`),E(v,"alt",`${e(l).userImageAlt}`)}),D(u,s)},ye=u=>{var s=ra(),v=a(s),y=a(v,!0);t(v),t(s),b(()=>i(y,e(l).noImage)),D(u,s)};W(X,u=>{e(o).image?u(xe):u(ye,!1)})}var Y=r(X,2),S=a(Y),j=a(S),De=a(j,!0);t(j);var I=r(j,2),Ee=a(I,!0);t(I),t(S);var Z=r(S,2),we=a(Z,!0);t(Z),t(Y),t(A);var ee=r(A,2),O=a(ee),ae=a(O),L=a(ae),Ne=a(L,!0);t(L);var te=r(L,4),ue=r(a(te),2),Ce=a(ue);t(ue),t(te),t(ae),t(O);var re=r(O,2);{var ke=u=>{var s=sa(),v=a(s),y=a(v),Se=a(y,!0);t(y);var oe=r(y,4),P=a(oe),de=a(P),T=a(de),je=a(T,!0);t(T);var le=r(T,2),Ie=a(le,!0);t(le),t(de),t(P);var R=r(P,2),$=a(R),H=a($),Oe=a(H,!0);t(H);var ve=r(H,2),Le=a(ve,!0);t(ve),t($),t(R);var ne=r(R,2),J=a(ne),K=a(J),Ue=a(K,!0);t(K);var C=r(K,2),ze=a(C,!0);t(C),t(J),t(ne),t(oe),t(v),t(s),b((Pe,Te,Re,$e)=>{i(Se,e(l).socialOpinion),i(je,e(l).rating),i(Ie,e(c).rating),E($,"href",Pe),i(Oe,e(l).karma),i(Le,e(c).karma),E(J,"href",Te),i(Ue,e(l).rate),ce(C,1,`rate-value fw-bold ${e(c).rate===null?"text-muted":""}`),Ze(C,`font-size: 1.5rem; ${Re??""}`),i(ze,$e)},[()=>e(q)(`/users/${e(o).id}/karma`),()=>e(q)(`/users/${e(o).id}/feedback`),()=>e(c).rate!==null?`color: ${ea(e(c).rate)};`:"",()=>e(c).rate!==null?e(c).rate.toFixed(1):"N/A"]),D(u,s)};W(re,u=>{e(c)&&u(ke)})}var se=r(re,2),ie=a(se),U=a(ie),Fe=a(U,!0);t(U);var z=r(U,4),x=a(z);Qe(x),x.__input=[ta,p,_e],t(z);var Be=r(z,2);{var Ae=u=>{var s=ia(),v=a(s,!0);t(s),b(()=>i(v,e(l).saved)),D(u,s)};W(Be,u=>{e(F)&&u(Ae)})}t(ie),t(se),t(ee),t(V),t(B),b(u=>{i(De,e(Q)),ce(I,1,u,"svelte-1hels8p"),i(Ee,e(o).role),i(we,e(pe)||""),i(Ne,e(l).userDetails),i(Ce,`${e(l).joinedOn??""} ${e(be)??""}`),i(Fe,e(l).userNote),E(x,"placeholder",e(l).userNotePlaceholder),Ye(x,e(p))},[()=>`badge ${he(e(o).role)}`]),Xe("blur",x,ge),D(m,B),qe()}Ve(["input"]);export{xa as component,ha as universal};
