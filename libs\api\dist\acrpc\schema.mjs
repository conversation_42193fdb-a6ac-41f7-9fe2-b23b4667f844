import {
  auth_exports,
  common_exports,
  commune_exports,
  rating_exports,
  reactor_exports,
  tag_exports,
  user_exports
} from "../chunk-UDAIHPC2.mjs";
import {
  superjsonTransformer
} from "../chunk-NKKQI6P3.mjs";
import "../chunk-J5LGTIGS.mjs";

// src/acrpc/schema.ts
var CACHE_CONTROL_TEN_MINUTES = "no-cache";
var CACHE_CONTROL_HOUR = "no-cache";
var CACHE_CONTROL_IMMUTABLE = "no-cache";
var DEFAULT_CACHE_CONTROL = CACHE_CONTROL_TEN_MINUTES;
var schema = {
  auth: {
    otp: {
      post: {
        input: auth_exports.SendOtpInputSchema,
        output: auth_exports.SendOtpOutputSchema,
        isMetadataUsed: false
      }
    },
    signUp: {
      post: {
        input: auth_exports.SignupInputSchema,
        output: auth_exports.SuccessfulOutputSchema,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    },
    signIn: {
      post: {
        input: auth_exports.SigninInputSchema,
        output: auth_exports.SuccessfulOutputSchema,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    },
    signOut: {
      get: {
        input: null,
        output: null,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    }
  },
  commune: {
    transferHeadStatus: {
      post: {
        input: commune_exports.TransferHeadStatusInputSchema,
        output: null,
        autoScopeInvalidationDepth: 2
      }
    },
    list: {
      get: {
        input: commune_exports.GetCommunesInputSchema,
        output: commune_exports.GetCommunesOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL,
        isMetadataRequired: false
      }
    },
    post: {
      input: commune_exports.CreateCommuneInputSchema,
      output: common_exports.ObjectWithIdSchema,
      autoScopeInvalidationDepth: 1
    },
    patch: {
      input: commune_exports.UpdateCommuneInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    delete: {
      input: common_exports.ObjectWithIdSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    member: {
      list: {
        get: {
          input: commune_exports.GetCommuneMembersInputSchema,
          output: commune_exports.GetCommuneMembersOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: commune_exports.CreateCommuneMemberInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    invitation: {
      list: {
        get: {
          input: commune_exports.GetCommuneInvitationsInputSchema,
          output: commune_exports.GetCommuneInvitationsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: commune_exports.CreateCommuneInvitationInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      accept: {
        post: {
          input: common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      },
      reject: {
        post: {
          input: common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      }
    },
    joinRequest: {
      list: {
        get: {
          input: commune_exports.GetCommuneJoinRequestsInputSchema,
          output: commune_exports.GetCommuneJoinRequestsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: commune_exports.CreateCommuneJoinRequestInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      accept: {
        post: {
          input: common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      },
      reject: {
        post: {
          input: common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      }
    }
  },
  rating: {
    karma: {
      list: {
        get: {
          input: rating_exports.GetKarmaPointsInputSchema,
          output: rating_exports.GetKarmaPointsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: rating_exports.SpendKarmaPointInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1,
        invalidate: ["/rating/summary"]
      }
    },
    feedback: {
      list: {
        get: {
          input: rating_exports.GetUserFeedbacksInputSchema,
          output: rating_exports.GetUserFeedbacksOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: rating_exports.CreateUserFeedbackInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1,
        invalidate: ["/rating/summary"]
      }
    },
    summary: {
      get: {
        input: rating_exports.GetUserSummaryInputSchema,
        output: rating_exports.GetUserSummaryOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      }
    }
  },
  reactor: {
    post: {
      list: {
        get: {
          input: reactor_exports.GetPostsInputSchema,
          output: reactor_exports.GetPostsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: reactor_exports.CreatePostInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: reactor_exports.UpdatePostInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: reactor_exports.DeletePostInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      rating: {
        post: {
          input: reactor_exports.UpdatePostRatingInputSchema,
          output: reactor_exports.UpdatePostRatingOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      usefulness: {
        post: {
          input: reactor_exports.UpdatePostUsefulnessInputSchema,
          output: reactor_exports.UpdatePostUsefulnessOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      image: {
        list: {
          get: {
            input: reactor_exports.GetPostImagesInputSchema,
            output: reactor_exports.GetPostImagesOutputSchema,
            cacheControl: DEFAULT_CACHE_CONTROL
          }
        }
      }
    },
    comment: {
      list: {
        get: {
          input: reactor_exports.GetCommentsInputSchema,
          output: reactor_exports.GetCommentsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: reactor_exports.CreateCommentInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: reactor_exports.UpdateCommentInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: reactor_exports.DeleteCommentInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      rating: {
        post: {
          input: reactor_exports.UpdateCommentRatingInputSchema,
          output: reactor_exports.UpdateCommentRatingOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      anonimify: {
        post: {
          input: reactor_exports.AnonimifyCommentInputSchema,
          output: null,
          autoScopeInvalidationDepth: 2
        }
      }
    },
    lens: {
      list: {
        get: {
          input: null,
          output: reactor_exports.GetLensesOutputSchema,
          cacheControl: CACHE_CONTROL_IMMUTABLE
        }
      },
      post: {
        input: reactor_exports.CreateLensInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: reactor_exports.UpdateLensInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    hub: {
      list: {
        get: {
          input: reactor_exports.GetHubsInputSchema,
          output: reactor_exports.GetHubsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: reactor_exports.CreateHubInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: reactor_exports.UpdateHubInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
      // delete: {
      //     input: Common.ObjectWithIdSchema,
      //     output: null,
      //     enableAutoScopeInvalidation: true,
      // },
    },
    community: {
      list: {
        get: {
          input: reactor_exports.GetCommunitiesInputSchema,
          output: reactor_exports.GetCommunitiesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: reactor_exports.CreateCommunityInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: reactor_exports.UpdateCommunityInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
      // delete: {
      //     input: Common.ObjectWithIdSchema,
      //     output: null,
      //     enableAutoScopeInvalidation: true,
      // },
    }
  },
  tag: {
    list: {
      get: {
        input: tag_exports.GetTagsInputSchema,
        output: tag_exports.GetTagsOutputSchema,
        cacheControl: CACHE_CONTROL_HOUR
      }
    },
    post: {
      input: tag_exports.CreateTagInputSchema,
      output: common_exports.ObjectWithIdSchema,
      autoScopeInvalidationDepth: 1
    },
    patch: {
      input: tag_exports.UpdateTagInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    delete: {
      input: common_exports.ObjectWithIdSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    }
  },
  user: {
    list: {
      get: {
        input: user_exports.GetUsersInputSchema,
        output: user_exports.GetUsersOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      }
    },
    me: {
      get: {
        input: null,
        output: user_exports.GetMeOutputSchema,
        cacheControl: CACHE_CONTROL_HOUR
      }
    },
    patch: {
      input: user_exports.UpdateUserInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    title: {
      list: {
        get: {
          input: user_exports.GetUserTitlesInputSchema,
          output: user_exports.GetUserTitlesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: user_exports.CreateUserTitleInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: user_exports.UpdateUserTitleInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    note: {
      get: {
        input: user_exports.GetUserNoteInputSchema,
        output: user_exports.GetUserNoteOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      },
      put: {
        input: user_exports.UpdateUserNoteInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    invite: {
      list: {
        get: {
          input: user_exports.GetUserInvitesInputSchema,
          output: user_exports.GetUserInvitesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      put: {
        input: user_exports.UpsertUserInviteInputSchema,
        output: common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: user_exports.DeleteUserInviteInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    }
  }
};
var transformer = superjsonTransformer;
export {
  CACHE_CONTROL_HOUR,
  CACHE_CONTROL_IMMUTABLE,
  CACHE_CONTROL_TEN_MINUTES,
  DEFAULT_CACHE_CONTROL,
  schema,
  transformer
};
//# sourceMappingURL=schema.mjs.map