{"version": 3, "file": "_page.svelte-CZWzdeFu.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/communities/_id_/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, x as head, z as escape_html, y as attr, F as attr_style } from \"../../../../../../chunks/index.js\";\nimport \"../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nimport { f as fetchWithAuth } from \"../../../../../../chunks/fetch-with-auth.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../chunks/exports.js\";\nimport \"../../../../../../chunks/state.svelte.js\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../../chunks/format-date.js\";\nimport { M as Modal } from \"../../../../../../chunks/modal.js\";\nimport { L as Localized_input } from \"../../../../../../chunks/localized-input.js\";\nimport { L as Localized_textarea } from \"../../../../../../chunks/localized-textarea.js\";\n/* empty css                                                                              */\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Community — Reactor of Commune\" },\n      head: \"Head\",\n      createdOn: \"Created on\",\n      editCommunity: \"Edit Community\",\n      uploadImage: \"Upload Image\",\n      deleteImage: \"Delete Image\",\n      hub: \"Hub\",\n      noHub: \"No hub\",\n      editCommunityTitle: \"Edit Community\",\n      communityName: \"Community Name\",\n      communityDescription: \"Community Description\",\n      communityNamePlaceholder: \"Enter community name\",\n      communityDescriptionPlaceholder: \"Enter community description\",\n      save: \"Save\",\n      cancel: \"Cancel\",\n      saving: \"Saving...\",\n      communityUpdatedSuccess: \"Community updated successfully!\",\n      errorUpdatingCommunity: \"Failed to update community\",\n      required: \"This field is required\",\n      uploadImageTitle: \"Upload Community Image\",\n      upload: \"Upload\",\n      uploading: \"Uploading...\",\n      imageUploadedSuccess: \"Image uploaded successfully!\",\n      errorUploadingImage: \"Failed to upload image\",\n      pleaseSelectImage: \"Please select an image to upload\",\n      invalidFileType: \"Invalid file type. Please upload a JPG, PNG, or WebP image.\",\n      fileTooLarge: \"File is too large. Maximum size is 5MB.\",\n      uploadImageMaxSize: \"Upload an image (JPG, PNG, WebP), max 5MB.\",\n      confirmDeleteImage: \"Are you sure you want to delete this image?\",\n      deleteImageTitle: \"Delete Image\",\n      delete: \"Delete\",\n      deleting: \"Deleting...\",\n      imageDeletedSuccess: \"Image deleted successfully!\",\n      errorDeletingImage: \"Failed to delete image\"\n    },\n    ru: {\n      _page: {\n        title: \"Сообщество — Реактор Коммуны\"\n      },\n      head: \"Глава\",\n      createdOn: \"Создано\",\n      editCommunity: \"Редактировать сообщество\",\n      uploadImage: \"Загрузить изображение\",\n      deleteImage: \"Удалить изображение\",\n      hub: \"Хаб\",\n      noHub: \"Нет хаба\",\n      editCommunityTitle: \"Редактировать сообщество\",\n      communityName: \"Название сообщества\",\n      communityDescription: \"Описание сообщества\",\n      communityNamePlaceholder: \"Введите название сообщества\",\n      communityDescriptionPlaceholder: \"Введите описание сообщества\",\n      save: \"Сохранить\",\n      cancel: \"Отмена\",\n      saving: \"Сохранение...\",\n      communityUpdatedSuccess: \"Сообщество успешно обновлено!\",\n      errorUpdatingCommunity: \"Не удалось обновить сообщество\",\n      required: \"Это поле обязательно\",\n      uploadImageTitle: \"Загрузить изображение сообщества\",\n      upload: \"Загрузить\",\n      uploading: \"Загрузка...\",\n      imageUploadedSuccess: \"Изображение загружено успешно!\",\n      errorUploadingImage: \"Не удалось загрузить изображение\",\n      pleaseSelectImage: \"Пожалуйста, выберите изображение для загрузки\",\n      invalidFileType: \"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.\",\n      fileTooLarge: \"Файл слишком большой. Максимальный размер - 5MB.\",\n      uploadImageMaxSize: \"Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.\",\n      confirmDeleteImage: \"Вы уверены, что хотите удалить это изображение?\",\n      deleteImageTitle: \"Удалить изображение\",\n      delete: \"Удалить\",\n      deleting: \"Удаление...\",\n      imageDeletedSuccess: \"Изображение удалено успешно!\",\n      errorDeletingImage: \"Не удалось удалить изображение\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let community = data.community;\n  let showEditModal = false;\n  let isUpdating = false;\n  let updateError = null;\n  let updateSuccess = null;\n  let communityName = [];\n  let communityDescription = [];\n  let showUploadModal = false;\n  let isUploading = false;\n  let uploadError = null;\n  let uploadSuccess = null;\n  let selectedFile = null;\n  let previewUrl = null;\n  let showDeleteImageModal = false;\n  let isDeleting = false;\n  let deleteError = null;\n  let deleteSuccess = null;\n  function closeEditModal() {\n    showEditModal = false;\n  }\n  function validateEditForm() {\n    if (!communityName.some((item) => item.value.trim().length > 0)) {\n      updateError = t.required;\n      return false;\n    }\n    if (!communityDescription.some((item) => item.value.trim().length > 0)) {\n      updateError = t.required;\n      return false;\n    }\n    return true;\n  }\n  async function handleUpdateCommunity() {\n    if (!validateEditForm()) return;\n    isUpdating = true;\n    updateError = null;\n    updateSuccess = null;\n    try {\n      await api.reactor.community.patch({\n        id: community.id,\n        name: communityName,\n        description: communityDescription\n      });\n      updateSuccess = t.communityUpdatedSuccess;\n      setTimeout(\n        () => {\n          refresh();\n        },\n        1500\n      );\n    } catch (err) {\n      updateError = err instanceof Error ? err.message : t.errorUpdatingCommunity;\n      console.error(err);\n    } finally {\n      isUpdating = false;\n    }\n  }\n  function closeUploadModal() {\n    showUploadModal = false;\n    selectedFile = null;\n    previewUrl = null;\n  }\n  async function handleUploadImage() {\n    if (!selectedFile) {\n      uploadError = t.pleaseSelectImage;\n      return;\n    }\n    isUploading = true;\n    uploadError = null;\n    uploadSuccess = null;\n    try {\n      const formData = new FormData();\n      formData.append(\"image\", selectedFile);\n      const response = await fetchWithAuth(`/api/reactor/community/${community.id}/image`, { method: \"PUT\", body: formData });\n      if (!response.ok) {\n        throw new Error(`${t.errorUploadingImage}: ${response.statusText}`);\n      }\n      uploadSuccess = t.imageUploadedSuccess;\n      setTimeout(\n        () => {\n          window.location.reload();\n        },\n        1500\n      );\n    } catch (err) {\n      uploadError = err instanceof Error ? err.message : t.errorUploadingImage;\n      console.error(err);\n    } finally {\n      isUploading = false;\n    }\n  }\n  function closeDeleteImageModal() {\n    showDeleteImageModal = false;\n  }\n  async function handleDeleteImage() {\n    isDeleting = true;\n    deleteError = null;\n    deleteSuccess = null;\n    try {\n      const response = await fetchWithAuth(`/api/reactor/community/${community.id}/image`, { method: \"DELETE\" });\n      if (!response.ok) {\n        throw new Error(`${t.errorDeletingImage}: ${response.statusText}`);\n      }\n      deleteSuccess = t.imageDeletedSuccess;\n      setTimeout(\n        () => {\n          window.location.reload();\n        },\n        1500\n      );\n    } catch (err) {\n      deleteError = err instanceof Error ? err.message : t.errorDeletingImage;\n      console.error(err);\n    } finally {\n      isDeleting = false;\n    }\n  }\n  function refresh() {\n    window.location.reload();\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(getAppropriateLocalization(community.name) || \"Community\")} — ${escape_html(t._page.title)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"container my-4 mb-5\"><div class=\"row mb-4\"><div class=\"col-md-4 col-lg-3 mb-3\"><div class=\"community-image-container svelte-14fieot\">`);\n    if (community.image) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<img${attr(\"src\", `/images/${community.image}`)}${attr(\"alt\", getAppropriateLocalization(community.name) || \"Community\")} class=\"community-image svelte-14fieot\"/>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<div class=\"community-image-placeholder svelte-14fieot\"><i class=\"bi bi-people fs-1 text-muted\"></i></div>`);\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    if (data.canEdit) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"mt-3 d-grid gap-2\"><button class=\"btn btn-outline-primary btn-sm\"><i class=\"bi bi-upload me-1\"></i> ${escape_html(t.uploadImage)}</button> `);\n      if (community.image) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<button class=\"btn btn-outline-danger btn-sm\"><i class=\"bi bi-trash me-1\"></i> ${escape_html(t.deleteImage)}</button>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div> <div class=\"col-md-8 col-lg-9\"><div class=\"d-flex justify-content-between align-items-start mb-3\"><h1 class=\"mb-0\">${escape_html(getAppropriateLocalization(community.name) || \"Unknown Community\")}</h1> `);\n    if (data.canEdit) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<button class=\"btn btn-primary\"><i class=\"bi bi-pencil me-1\"></i> ${escape_html(t.editCommunity)}</button>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div> <p class=\"text-muted mb-3 fs-5\">${escape_html(getAppropriateLocalization(community.description) || \"\")}</p> <div class=\"row g-3\">`);\n    if (community.hub) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"col-sm-6\"><div class=\"d-flex align-items-center\"><div class=\"me-3\">`);\n      if (community.hub.image) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<img${attr(\"src\", `/images/${community.hub.image}`)}${attr(\"alt\", getAppropriateLocalization(community.hub.name))} class=\"rounded\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n        $$payload2.out.push(`<div class=\"rounded bg-secondary d-flex align-items-center justify-content-center\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-collection text-white\"></i></div>`);\n      }\n      $$payload2.out.push(`<!--]--></div> <div><a${attr(\"href\", toLocaleHref(`/reactor/hubs/${community.hub.id}`))} class=\"fw-medium\" style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(community.hub.name))}</a></div></div></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> <div class=\"col-sm-6\"><div class=\"d-flex align-items-center\"><div class=\"me-3\">`);\n    if (community.headUser.image) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<img${attr(\"src\", `/images/${community.headUser.image}`)}${attr(\"alt\", getAppropriateLocalization(community.headUser.name))} class=\"rounded-circle\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person-fill text-white\"></i></div>`);\n    }\n    $$payload2.out.push(`<!--]--></div> <div><a${attr(\"href\", toLocaleHref(`/users/${community.headUser.id}`))} class=\"fw-medium\" style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(community.headUser.name))}</a></div></div></div> <div class=\"col-sm-6\"><div class=\"small text-muted\">${escape_html(t.createdOn)}:</div> <div class=\"fw-medium\">${escape_html(formatDate(community.createdAt, locale))}</div></div></div></div></div> `);\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    if (data.canEdit) {\n      $$payload2.out.push(\"<!--[-->\");\n      Modal($$payload2, {\n        show: showEditModal,\n        title: t.editCommunityTitle,\n        onClose: closeEditModal,\n        onSubmit: handleUpdateCommunity,\n        submitText: isUpdating ? t.saving : t.save,\n        cancelText: t.cancel,\n        submitDisabled: isUpdating || !communityName.some((item) => item.value.trim().length > 0) || !communityDescription.some((item) => item.value.trim().length > 0),\n        isSubmitting: isUpdating,\n        children: ($$payload3) => {\n          if (updateError) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(updateError)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> `);\n          if (updateSuccess) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(updateSuccess)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> <form>`);\n          Localized_input($$payload3, {\n            locale,\n            id: \"community-name\",\n            label: t.communityName,\n            placeholder: t.communityNamePlaceholder,\n            required: true,\n            get value() {\n              return communityName;\n            },\n            set value($$value) {\n              communityName = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----> `);\n          Localized_textarea($$payload3, {\n            locale,\n            id: \"community-description\",\n            label: t.communityDescription,\n            placeholder: t.communityDescriptionPlaceholder,\n            rows: 4,\n            required: true,\n            get value() {\n              return communityDescription;\n            },\n            set value($$value) {\n              communityDescription = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----></form>`);\n        }\n      });\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (data.canEdit) {\n      $$payload2.out.push(\"<!--[-->\");\n      Modal($$payload2, {\n        show: showUploadModal,\n        title: t.uploadImageTitle,\n        onClose: closeUploadModal,\n        onSubmit: handleUploadImage,\n        submitText: isUploading ? t.uploading : t.upload,\n        cancelText: t.cancel,\n        submitDisabled: !selectedFile || isUploading,\n        isSubmitting: isUploading,\n        size: \"lg\",\n        children: ($$payload3) => {\n          if (uploadSuccess) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(uploadSuccess)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> `);\n          if (uploadError) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(uploadError)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> <form><div class=\"mb-3\"><label for=\"imageInput\" class=\"form-label\">${escape_html(t.pleaseSelectImage)}</label> <input id=\"imageInput\" type=\"file\" class=\"form-control\" accept=\".jpg,.jpeg,.png,.webp\"${attr(\"disabled\", isUploading, true)}/> <p class=\"form-text text-muted\">${escape_html(t.uploadImageMaxSize)}</p> `);\n          if (previewUrl) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"mt-3 text-center\"><img${attr(\"src\", previewUrl)} alt=\"Preview\" class=\"img-thumbnail\"${attr_style(\"\", { \"max-height\": \"200px\" })}/></div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--></div></form>`);\n        }\n      });\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (data.canEdit && community.image) {\n      $$payload2.out.push(\"<!--[-->\");\n      Modal($$payload2, {\n        show: showDeleteImageModal,\n        title: t.deleteImageTitle,\n        onClose: closeDeleteImageModal,\n        onSubmit: handleDeleteImage,\n        submitText: isDeleting ? t.deleting : t.delete,\n        cancelText: t.cancel,\n        submitDisabled: isDeleting,\n        isSubmitting: isDeleting,\n        children: ($$payload3) => {\n          if (deleteSuccess) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(deleteSuccess)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> `);\n          if (deleteError) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(deleteError)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> <p>${escape_html(t.confirmDeleteImage)}</p>`);\n        }\n      });\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]-->`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAaA;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,gCAAgC,EAAE;AACxD,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,kBAAkB,EAAE,gBAAgB;AAC1C,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,oBAAoB,EAAE,uBAAuB;AACnD,MAAM,wBAAwB,EAAE,sBAAsB;AACtD,MAAM,+BAA+B,EAAE,6BAA6B;AACpE,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,uBAAuB,EAAE,iCAAiC;AAChE,MAAM,sBAAsB,EAAE,4BAA4B;AAC1D,MAAM,QAAQ,EAAE,wBAAwB;AACxC,MAAM,gBAAgB,EAAE,wBAAwB;AAChD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,oBAAoB,EAAE,8BAA8B;AAC1D,MAAM,mBAAmB,EAAE,wBAAwB;AACnD,MAAM,iBAAiB,EAAE,kCAAkC;AAC3D,MAAM,eAAe,EAAE,6DAA6D;AACpF,MAAM,YAAY,EAAE,yCAAyC;AAC7D,MAAM,kBAAkB,EAAE,4CAA4C;AACtE,MAAM,kBAAkB,EAAE,6CAA6C;AACvE,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,mBAAmB,EAAE,6BAA6B;AACxD,MAAM,kBAAkB,EAAE;AAC1B,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,aAAa,EAAE,0BAA0B;AAC/C,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,WAAW,EAAE,qBAAqB;AACxC,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,kBAAkB,EAAE,0BAA0B;AACpD,MAAM,aAAa,EAAE,qBAAqB;AAC1C,MAAM,oBAAoB,EAAE,qBAAqB;AACjD,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,+BAA+B,EAAE,6BAA6B;AACpE,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,uBAAuB,EAAE,+BAA+B;AAC9D,MAAM,sBAAsB,EAAE,gCAAgC;AAC9D,MAAM,QAAQ,EAAE,sBAAsB;AACtC,MAAM,gBAAgB,EAAE,kCAAkC;AAC1D,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,oBAAoB,EAAE,gCAAgC;AAC5D,MAAM,mBAAmB,EAAE,kCAAkC;AAC7D,MAAM,iBAAiB,EAAE,+CAA+C;AACxE,MAAM,eAAe,EAAE,0EAA0E;AACjG,MAAM,YAAY,EAAE,kDAAkD;AACtE,MAAM,kBAAkB,EAAE,oEAAoE;AAC9F,MAAM,kBAAkB,EAAE,iDAAiD;AAC3E,MAAM,gBAAgB,EAAE,qBAAqB;AAC7C,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,mBAAmB,EAAE,8BAA8B;AACzD,MAAM,kBAAkB,EAAE;AAC1B;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;AAChC,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,IAAI;AAC1B,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,IAAI,oBAAoB,GAAG,EAAE;AAC/B,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,IAAI,WAAW,GAAG,KAAK;AACzB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,IAAI;AAC1B,EAAE,IAAI,YAAY,GAAG,IAAI;AACzB,EAAE,IAAI,UAAU,GAAG,IAAI;AACvB,EAAE,IAAI,oBAAoB,GAAG,KAAK;AAClC,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,IAAI;AAC1B,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,aAAa,GAAG,KAAK;AACzB,EAAE;AACF,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AACrE,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ;AAC9B,MAAM,OAAO,KAAK;AAClB,IAAI;AACJ,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AAC5E,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ;AAC9B,MAAM,OAAO,KAAK;AAClB,IAAI;AACJ,IAAI,OAAO,IAAI;AACf,EAAE;AACF,EAAE,eAAe,qBAAqB,GAAG;AACzC,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;AAC7B,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;AACxC,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;AACxB,QAAQ,IAAI,EAAE,aAAa;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,aAAa,GAAG,CAAC,CAAC,uBAAuB;AAC/C,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,OAAO,EAAE;AACnB,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,WAAW,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,sBAAsB;AACjF,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,UAAU,GAAG,KAAK;AACxB,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,UAAU,GAAG,IAAI;AACrB,EAAE;AACF,EAAE,eAAe,iBAAiB,GAAG;AACrC,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,WAAW,GAAG,CAAC,CAAC,iBAAiB;AACvC,MAAM;AACN,IAAI;AACJ,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE;AACrC,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC;AAC5C,MAAM,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC7H,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACxB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAC3E,MAAM;AACN,MAAM,aAAa,GAAG,CAAC,CAAC,oBAAoB;AAC5C,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AAClC,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,WAAW,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,mBAAmB;AAC9E,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,WAAW,GAAG,KAAK;AACzB,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,qBAAqB,GAAG;AACnC,IAAI,oBAAoB,GAAG,KAAK;AAChC,EAAE;AACF,EAAE,eAAe,iBAAiB,GAAG;AACrC,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;AAChH,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACxB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1E,MAAM;AACN,MAAM,aAAa,GAAG,CAAC,CAAC,mBAAmB;AAC3C,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AAClC,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,WAAW,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,kBAAkB;AAC7E,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,UAAU,GAAG,KAAK;AACxB,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,OAAO,GAAG;AACrB,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC5B,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACnJ,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iJAAiJ,CAAC,CAAC;AAC5K,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,yCAAyC,CAAC,CAAC;AAC/L,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0GAA0G,CAAC,CAAC;AACvI,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gHAAgH,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AACpL,MAAM,IAAI,SAAS,CAAC,KAAK,EAAE;AAC3B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;AACpJ,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kIAAkI,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC;AACpP,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kEAAkE,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,CAAC;AACvI,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC3K,IAAI,IAAI,SAAS,CAAC,GAAG,EAAE;AACvB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,CAAC,CAAC;AAC5G,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE;AAC/B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,wEAAwE,CAAC,CAAC;AACzN,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uKAAuK,CAAC,CAAC;AACtM,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC3P,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wFAAwF,CAAC,CAAC;AACnH,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE;AAClC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,+EAA+E,CAAC,CAAC;AACxO,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAC5M,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,2EAA2E,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,+BAA+B,EAAE,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACjc,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,IAAI,EAAE,aAAa;AAC3B,QAAQ,KAAK,EAAE,CAAC,CAAC,kBAAkB;AACnC,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,QAAQ,EAAE,qBAAqB;AACvC,QAAQ,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;AAClD,QAAQ,UAAU,EAAE,CAAC,CAAC,MAAM;AAC5B,QAAQ,cAAc,EAAE,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACvK,QAAQ,YAAY,EAAE,UAAU;AAChC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AACzG,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C,UAAU,IAAI,aAAa,EAAE;AAC7B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5G,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAChD,UAAU,eAAe,CAAC,UAAU,EAAE;AACtC,YAAY,MAAM;AAClB,YAAY,EAAE,EAAE,gBAAgB;AAChC,YAAY,KAAK,EAAE,CAAC,CAAC,aAAa;AAClC,YAAY,WAAW,EAAE,CAAC,CAAC,wBAAwB;AACnD,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,KAAK,GAAG;AACxB,cAAc,OAAO,aAAa;AAClC,YAAY,CAAC;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/B,cAAc,aAAa,GAAG,OAAO;AACrC,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACzC,UAAU,kBAAkB,CAAC,UAAU,EAAE;AACzC,YAAY,MAAM;AAClB,YAAY,EAAE,EAAE,uBAAuB;AACvC,YAAY,KAAK,EAAE,CAAC,CAAC,oBAAoB;AACzC,YAAY,WAAW,EAAE,CAAC,CAAC,+BAA+B;AAC1D,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,KAAK,GAAG;AACxB,cAAc,OAAO,oBAAoB;AACzC,YAAY,CAAC;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/B,cAAc,oBAAoB,GAAG,OAAO;AAC5C,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC/C,QAAQ;AACR,OAAO,CAAC;AACR,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,KAAK,EAAE,CAAC,CAAC,gBAAgB;AACjC,QAAQ,OAAO,EAAE,gBAAgB;AACjC,QAAQ,QAAQ,EAAE,iBAAiB;AACnC,QAAQ,UAAU,EAAE,WAAW,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;AACxD,QAAQ,UAAU,EAAE,CAAC,CAAC,MAAM;AAC5B,QAAQ,cAAc,EAAE,CAAC,YAAY,IAAI,WAAW;AACpD,QAAQ,YAAY,EAAE,WAAW;AACjC,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,aAAa,EAAE;AAC7B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5G,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AACzG,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4EAA4E,EAAE,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,+FAA+F,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,CAAC;AACjW,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,oCAAoC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvL,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,CAAC;AACtD,QAAQ;AACR,OAAO,CAAC;AACR,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE;AACzC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,IAAI,EAAE,oBAAoB;AAClC,QAAQ,KAAK,EAAE,CAAC,CAAC,gBAAgB;AACjC,QAAQ,OAAO,EAAE,qBAAqB;AACtC,QAAQ,QAAQ,EAAE,iBAAiB;AACnC,QAAQ,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM;AACtD,QAAQ,UAAU,EAAE,CAAC,CAAC,MAAM;AAC5B,QAAQ,cAAc,EAAE,UAAU;AAClC,QAAQ,YAAY,EAAE,UAAU;AAChC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,aAAa,EAAE;AAC7B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5G,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AACzG,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC;AACrF,QAAQ;AACR,OAAO,CAAC;AACR,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}