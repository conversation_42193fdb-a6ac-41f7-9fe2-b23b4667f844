{"version": 3, "file": "_server.ts-e-UuZ_Uf.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/_...slug_/_server.ts.js"], "sourcesContent": ["import { A as API_URL } from \"../../../../chunks/private.js\";\nasync function proxy(event) {\n  const { request, params, url } = event;\n  const slugPath = params.slug ?? \"\";\n  const externalUrl = new URL(`${API_URL}/${slugPath}`);\n  externalUrl.search = url.search;\n  const init = {\n    method: request.method,\n    headers: {},\n    redirect: \"manual\"\n  };\n  for (const [key, value] of request.headers) {\n    if (key.toLowerCase() === \"host\") continue;\n    init.headers[key] = value;\n  }\n  if (request.method !== \"GET\" && request.method !== \"HEAD\") {\n    init.body = await request.arrayBuffer();\n  }\n  const response = await fetch(externalUrl.toString(), init);\n  const headers = new Headers(response.headers);\n  headers.delete(\"connection\");\n  headers.delete(\"keep-alive\");\n  headers.delete(\"transfer-encoding\");\n  headers.delete(\"upgrade\");\n  return new Response(response.body, {\n    status: response.status,\n    headers\n  });\n}\nconst GET = proxy;\nconst POST = proxy;\nconst PUT = proxy;\nconst PATCH = proxy;\nconst DELETE = proxy;\nconst OPTIONS = proxy;\nexport {\n  DELETE,\n  GET,\n  OPTIONS,\n  PATCH,\n  POST,\n  PUT\n};\n"], "names": [], "mappings": ";;AACA,eAAe,KAAK,CAAC,KAAK,EAAE;AAC5B,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,KAAK;AACxC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE;AACpC,EAAE,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AACvD,EAAE,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;AACjC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,MAAM,EAAE,OAAO,CAAC,MAAM;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE;AAC9C,IAAI,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK;AAC7B,EAAE;AACF,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;AAC7D,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE;AAC3C,EAAE;AACF,EAAE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC;AAC5D,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC/C,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;AAC9B,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;AAC9B,EAAE,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;AACrC,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;AAC3B,EAAE,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,CAAC,MAAM;AAC3B,IAAI;AACJ,GAAG,CAAC;AACJ;AACK,MAAC,GAAG,GAAG;AACP,MAAC,IAAI,GAAG;AACR,MAAC,GAAG,GAAG;AACP,MAAC,KAAK,GAAG;AACT,MAAC,MAAM,GAAG;AACV,MAAC,OAAO,GAAG;;;;"}