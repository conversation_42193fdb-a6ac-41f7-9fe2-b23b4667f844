{"version": 3, "file": "_page.svelte-DCuryJZz.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/profile/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, z as escape_html, y as attr, F as attr_style, x as head, G as attr_class } from \"../../../../../chunks/index.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../chunks/exports.js\";\nimport \"../../../../../chunks/state.svelte.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nimport { a as consts_exports } from \"../../../../../chunks/current-user.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../chunks/format-date.js\";\nimport \"clsx\";\nimport { M as Modal } from \"../../../../../chunks/modal.js\";\nimport { L as Localized_input } from \"../../../../../chunks/localized-input.js\";\nimport { L as Localized_textarea } from \"../../../../../chunks/localized-textarea.js\";\n/* empty css                                                                           */\nimport { f as fetchWithAuth } from \"../../../../../chunks/fetch-with-auth.js\";\nimport \"../../../../../chunks/schema.js\";\nfunction Edit_profile_modal($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      editProfile: \"Edit Profile\",\n      name: { label: \"Name\", placeholder: \"Enter your name\" },\n      description: {\n        label: \"Description (optional)\",\n        placeholder: \"Tell us about yourself\"\n      },\n      saveChanges: \"Save Changes\",\n      cancel: \"Cancel\",\n      saving: \"Saving...\",\n      nameRequired: \"Name is required\",\n      failedToUpdateProfile: \"Failed to update profile\",\n      profileUpdatedSuccessfully: \"Profile updated successfully\",\n      errorOccurred: \"An error occurred while updating profile\"\n    },\n    ru: {\n      editProfile: \"Редактировать профиль\",\n      name: {\n        label: \"Имя\",\n        placeholder: \"Введите ваше имя\"\n      },\n      description: {\n        label: \"Описание (необязательно)\",\n        placeholder: \"Расскажите о себе\"\n      },\n      saveChanges: \"Сохранить изменения\",\n      cancel: \"Отменить\",\n      saving: \"Сохранение...\",\n      nameRequired: \"Имя обязательно\",\n      failedToUpdateProfile: \"Не удалось обновить профиль\",\n      profileUpdatedSuccessfully: \"Профиль обновлен успешно\",\n      errorOccurred: \"Произошла ошибка при обновлении профиля\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { locale, show, onHide, onProfileUpdated, userData } = $$props;\n  const t = i18n[locale];\n  let error = \"\";\n  let isSubmitting = false;\n  let submitSuccess = false;\n  let name = userData?.name || [];\n  let description = userData?.description || [];\n  const handleSubmit = async () => {\n    if (!name.some((item) => item.value.trim().length)) {\n      error = t.nameRequired;\n      return;\n    }\n    isSubmitting = true;\n    error = \"\";\n    try {\n      await api.user.patch({ id: userData.id, name, description });\n      submitSuccess = true;\n      setTimeout(\n        () => {\n          handleClose();\n          onProfileUpdated();\n        },\n        1500\n      );\n    } catch (err) {\n      error = err instanceof Error ? err.message : t.errorOccurred;\n      console.error(err);\n    } finally {\n      isSubmitting = false;\n    }\n  };\n  const handleClose = () => {\n    error = \"\";\n    submitSuccess = false;\n    onHide();\n  };\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    Modal($$payload2, {\n      show,\n      title: t.editProfile,\n      onClose: handleClose,\n      onSubmit: handleSubmit,\n      submitText: isSubmitting ? t.saving : t.saveChanges,\n      cancelText: t.cancel,\n      submitDisabled: !name.some((item) => item.value.trim().length) || isSubmitting,\n      cancelDisabled: isSubmitting,\n      isSubmitting,\n      children: ($$payload3) => {\n        if (submitSuccess) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(t.profileUpdatedSuccessfully)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> `);\n        if (error) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(error)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> <form>`);\n        Localized_input($$payload3, {\n          id: \"profileName\",\n          label: t.name.label,\n          placeholder: t.name.placeholder,\n          required: true,\n          locale,\n          get value() {\n            return name;\n          },\n          set value($$value) {\n            name = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----> `);\n        Localized_textarea($$payload3, {\n          id: \"profileDescription\",\n          label: t.description.label,\n          placeholder: t.description.placeholder,\n          rows: 4,\n          locale,\n          get value() {\n            return description;\n          },\n          set value($$value) {\n            description = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----></form>`);\n      }\n    });\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nfunction Upload_image_modal($$payload, $$props) {\n  push();\n  const MAX_FILE_SIZE_MB = consts_exports.MAX_IMAGE_FILE_SIZE / (1024 * 1024);\n  const i18n = {\n    en: {\n      uploadImage: \"Upload Profile Image\",\n      upload: \"Upload\",\n      cancel: \"Cancel\",\n      uploading: \"Uploading...\",\n      imageUploadedSuccessfully: \"Image uploaded successfully!\",\n      pleaseSelectImage: \"Please select an image to upload\",\n      invalidFileTypeError: \"Invalid file type. Please upload a JPG, PNG, or WebP image.\",\n      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,\n      failedToUploadImage: \"Failed to upload image\",\n      errorOccurred: \"An error occurred while uploading the image\",\n      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`\n    },\n    ru: {\n      uploadImage: \"Загрузить изображение профиля\",\n      upload: \"Загрузить\",\n      cancel: \"Отменить\",\n      uploading: \"Загрузка...\",\n      imageUploadedSuccessfully: \"Изображение загружено успешно!\",\n      pleaseSelectImage: \"Пожалуйста, выберите изображение для загрузки\",\n      invalidFileTypeError: \"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.\",\n      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,\n      failedToUploadImage: \"Не удалось загрузить изображение\",\n      errorOccurred: \"Произошла ошибка при загрузке изображения\",\n      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`\n    }\n  };\n  const { locale, show, onHide, userId, onImageUploaded } = $$props;\n  const t = i18n[locale];\n  let selectedFile = null;\n  let previewUrl = null;\n  let error = \"\";\n  let isSubmitting = false;\n  let submitSuccess = false;\n  const handleSubmit = async () => {\n    if (!selectedFile) {\n      error = t.pleaseSelectImage;\n      return;\n    }\n    isSubmitting = true;\n    error = \"\";\n    try {\n      const formData = new FormData();\n      formData.append(\"image\", selectedFile);\n      const response = await fetchWithAuth(`/api/user/${userId}/image`, {\n        method: \"PUT\",\n        body: formData\n        // Don't set Content-Type header, it will be set automatically with the boundary\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || t.failedToUploadImage);\n      }\n      submitSuccess = true;\n      onImageUploaded();\n      const imageInput = document.getElementById(\"imageInput\");\n      if (imageInput) {\n        imageInput.files = null;\n      }\n      setTimeout(\n        () => {\n          handleClose();\n        },\n        1500\n      );\n    } catch (err) {\n      error = err instanceof Error ? err.message : t.errorOccurred;\n      console.error(err);\n    } finally {\n      isSubmitting = false;\n    }\n  };\n  const handleClose = () => {\n    selectedFile = null;\n    previewUrl = null;\n    error = \"\";\n    submitSuccess = false;\n    onHide();\n  };\n  Modal($$payload, {\n    show,\n    title: t.uploadImage,\n    onClose: handleClose,\n    onSubmit: handleSubmit,\n    submitText: isSubmitting ? t.uploading : t.upload,\n    cancelText: t.cancel,\n    submitDisabled: !selectedFile || isSubmitting,\n    cancelDisabled: isSubmitting,\n    isSubmitting,\n    size: \"lg\",\n    children: ($$payload2) => {\n      if (submitSuccess) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(t.imageUploadedSuccessfully)}</div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--> `);\n      if (error) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(error)}</div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--> <form><div class=\"mb-3\"><label for=\"imageInput\" class=\"form-label\">${escape_html(t.pleaseSelectImage)}</label> <input id=\"imageInput\" type=\"file\" class=\"form-control\" accept=\".jpg,.jpeg,.png,.webp\"${attr(\"disabled\", isSubmitting, true)}/> <p class=\"form-text text-muted\">${escape_html(t.uploadImageMaxSize)}</p> `);\n      if (previewUrl) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"mt-3 text-center\"><img${attr(\"src\", previewUrl)} alt=\"Preview\" class=\"img-thumbnail\"${attr_style(\"\", { \"max-height\": \"200px\" })}/></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div></form>`);\n    }\n  });\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Profile — Commune\" },\n      loading: \"Loading...\",\n      loadingProfile: \"Loading your profile...\",\n      uploadImage: \"Upload profile image\",\n      admin: \"Administrator\",\n      user: \"User\",\n      editProfile: \"Edit Profile\",\n      signOut: \"Sign Out\",\n      joined: \"Joined\",\n      accountSummary: \"Account Summary\",\n      accountType: {\n        title: \"Account Type\",\n        values: { admin: \"Administrator\", moderator: \"Moderator\", user: \"User\" }\n      },\n      daysAsMember: \"Days as member\",\n      aboutMe: \"About Me\",\n      noDescription: \"No description available yet. Add one to tell others about yourself.\",\n      addDescription: \"Add Description\",\n      dateFormatLocale: \"en-US\"\n    },\n    ru: {\n      _page: {\n        title: \"Профиль — Коммуна\"\n      },\n      loading: \"Загрузка...\",\n      loadingProfile: \"Загрузка вашего профиля...\",\n      uploadImage: \"Загрузить изображение профиля\",\n      admin: \"Администратор\",\n      user: \"Пользователь\",\n      editProfile: \"Редактировать профиль\",\n      signOut: \"Выйти\",\n      joined: \"Присоединился\",\n      accountSummary: \"Информация об аккаунте\",\n      accountType: {\n        title: \"Тип аккаунта\",\n        values: {\n          admin: \"Администратор\",\n          moderator: \"Модератор\",\n          user: \"Пользователь\"\n        }\n      },\n      daysAsMember: \"Дней в качестве участника\",\n      aboutMe: \"Обо мне\",\n      noDescription: \"Нет описания. Добавьте описание, чтобы рассказать другим о себе.\",\n      addDescription: \"Добавить описание\",\n      dateFormatLocale: \"ru-RU\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let user = data.me;\n  let showEditModal = false;\n  let showUploadModal = false;\n  const name = getAppropriateLocalization(user.name);\n  const description = getAppropriateLocalization(user.description);\n  const joinDate = new Date(user.createdAt);\n  function refresh() {\n    window.location.reload();\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container-fluid py-4\"><div class=\"row g-4\"><div class=\"col-lg-3\"><div class=\"card border-0 shadow-sm h-100\"><div class=\"text-center p-4\"><div class=\"position-relative mx-auto mb-3\"${attr_style(\"\", { width: \"120px\", height: \"120px\" })}><div class=\"bg-light rounded-circle overflow-hidden border\" role=\"img\"${attr_style(\"\", { width: \"100%\", height: \"100%\" })}><div class=\"position-relative w-100 h-100\"><img${attr(\"src\", user.image ? `/images/${user.image}` : \"/images/default-avatar.png\")}${attr(\"alt\", `${name}'s avatar`)}${attr(\"width\", 120)}${attr(\"height\", 120)}${attr_style(\"\", { width: \"100%\", height: \"100%\", \"object-fit\": \"cover\" })}/></div></div> <button class=\"position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0\"${attr(\"title\", t.uploadImage)}${attr(\"aria-label\", t.uploadImage)}${attr_style(\"\", {\n    width: \"30px\",\n    height: \"30px\",\n    display: \"flex\",\n    \"align-items\": \"center\",\n    \"justify-content\": \"center\"\n  })}><i class=\"bi bi-plus\"></i></button></div> <h4 class=\"fw-bold mb-1\">${escape_html(name)}</h4> <span${attr_class(`badge bg-${user.role === \"admin\" ? \"danger\" : \"primary\"} mb-3`)}>${escape_html(user.role === \"admin\" ? t.admin : t.user)}</span> <div class=\"d-grid gap-2 mt-3\"><button class=\"btn btn-primary\"><i class=\"bi bi-gear me-2\"></i> ${escape_html(t.editProfile)}</button> <button class=\"btn btn-outline-danger\"><i class=\"bi bi-box-arrow-right me-2\"></i> ${escape_html(t.signOut)}</button></div></div> <div class=\"card-footer bg-light border-top p-3\"><div class=\"d-flex align-items-center mb-2\"><i class=\"bi bi-envelope text-muted me-2\"></i> <div class=\"text-truncate\"><small class=\"text-muted\">${escape_html(user.email)}</small></div></div> <div class=\"d-flex align-items-center\"><i class=\"bi bi-calendar3 text-muted me-2\"></i> <div><small class=\"text-muted\">${escape_html(t.joined)} ${escape_html(formatDate(joinDate, locale))}</small></div></div></div></div></div> <div class=\"col-lg-9\"><div class=\"card border-0 shadow-sm mb-4\"><div class=\"card-header bg-transparent border-bottom-0 pb-0\"><h5 class=\"fw-bold\">${escape_html(t.accountSummary)}</h5></div> <div class=\"card-body\"><div class=\"row g-4\"><div class=\"col-md-4\"><div class=\"border rounded p-3 text-center h-100\"><div class=\"mb-2\"><i class=\"bi bi-shield-check fs-3 text-primary\"></i></div> <h2 class=\"mb-0 fw-bold\">${escape_html(t.accountType.values[user.role])}</h2> <p class=\"text-muted mb-0\">${escape_html(t.accountType.title)}</p></div></div> <div class=\"col-md-4\"><div class=\"border rounded p-3 text-center h-100\"><div class=\"mb-2\"><i class=\"bi bi-calendar-check fs-3 text-primary\"></i></div> <h2 class=\"mb-0 fw-bold\">${escape_html(Math.floor(((/* @__PURE__ */ new Date()).getTime() - joinDate.getTime()) / (1e3 * 60 * 60 * 24)))}</h2> <p class=\"text-muted mb-0\">${escape_html(t.daysAsMember)}</p></div></div></div></div></div> <div class=\"card border-0 shadow-sm mb-4\"><div class=\"card-header d-flex justify-content-between align-items-center bg-transparent\"><h5 class=\"fw-bold mb-0\">${escape_html(t.aboutMe)}</h5></div> <div class=\"card-body\">`);\n  if (description) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<p class=\"mb-0\">${escape_html(description)}</p>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div class=\"text-center text-muted py-4\"><i class=\"bi bi-file-earmark-text fs-1 mb-2\"></i> <p>${escape_html(t.noDescription)}</p> <button class=\"btn btn-sm btn-primary\"${attr(\"aria-label\", t.addDescription)}><i class=\"bi bi-plus-circle me-1\"></i> ${escape_html(t.addDescription)}</button></div>`);\n  }\n  $$payload.out.push(`<!--]--></div></div></div></div> `);\n  Edit_profile_modal($$payload, {\n    locale,\n    show: showEditModal,\n    onHide: () => showEditModal = false,\n    userData: user || null,\n    onProfileUpdated: refresh\n  });\n  $$payload.out.push(`<!----> `);\n  Upload_image_modal($$payload, {\n    locale,\n    show: showUploadModal,\n    onHide: () => showUploadModal = false,\n    userId: user.id,\n    onImageUploaded: refresh\n  });\n  $$payload.out.push(`<!----></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE;AAC7D,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,wBAAwB;AACvC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,YAAY,EAAE,kBAAkB;AACtC,MAAM,qBAAqB,EAAE,0BAA0B;AACvD,MAAM,0BAA0B,EAAE,8BAA8B;AAChE,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,0BAA0B;AACzC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,WAAW,EAAE,qBAAqB;AACxC,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,YAAY,EAAE,iBAAiB;AACrC,MAAM,qBAAqB,EAAE,6BAA6B;AAC1D,MAAM,0BAA0B,EAAE,0BAA0B;AAC5D,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,OAAO;AACtE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,IAAI,GAAG,QAAQ,EAAE,IAAI,IAAI,EAAE;AACjC,EAAE,IAAI,WAAW,GAAG,QAAQ,EAAE,WAAW,IAAI,EAAE;AAC/C,EAAE,MAAM,YAAY,GAAG,YAAY;AACnC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE;AACxD,MAAM,KAAK,GAAG,CAAC,CAAC,YAAY;AAC5B,MAAM;AACN,IAAI;AACJ,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;AAClE,MAAM,aAAa,GAAG,IAAI;AAC1B,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,WAAW,EAAE;AACvB,UAAU,gBAAgB,EAAE;AAC5B,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,KAAK,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,aAAa;AAClE,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,YAAY,GAAG,KAAK;AAC1B,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,MAAM,EAAE;AACZ,EAAE,CAAC;AACH,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,CAAC,CAAC,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,WAAW;AACzD,MAAM,UAAU,EAAE,CAAC,CAAC,MAAM;AAC1B,MAAM,cAAc,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,YAAY;AACpF,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,YAAY;AAClB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC;AACzH,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACjG,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,QAAQ,eAAe,CAAC,UAAU,EAAE;AACpC,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC7B,UAAU,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW;AACzC,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,MAAM;AAChB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,IAAI;AACvB,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,IAAI,GAAG,OAAO;AAC1B,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,kBAAkB,CAAC,UAAU,EAAE;AACvC,UAAU,EAAE,EAAE,oBAAoB;AAClC,UAAU,KAAK,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK;AACpC,UAAU,WAAW,EAAE,CAAC,CAAC,WAAW,CAAC,WAAW;AAChD,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,MAAM;AAChB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,WAAW;AAC9B,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,WAAW,GAAG,OAAO;AACjC,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAM;AACN,KAAK,CAAC;AACN,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,gBAAgB,GAAG,cAAc,CAAC,mBAAmB,IAAI,IAAI,GAAG,IAAI,CAAC;AAC7E,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,yBAAyB,EAAE,8BAA8B;AAC/D,MAAM,iBAAiB,EAAE,kCAAkC;AAC3D,MAAM,oBAAoB,EAAE,6DAA6D;AACzF,MAAM,YAAY,EAAE,CAAC,mCAAmC,EAAE,gBAAgB,CAAC,GAAG,CAAC;AAC/E,MAAM,mBAAmB,EAAE,wBAAwB;AACnD,MAAM,aAAa,EAAE,6CAA6C;AAClE,MAAM,kBAAkB,EAAE,CAAC,sCAAsC,EAAE,gBAAgB,CAAC,GAAG;AACvF,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,+BAA+B;AAClD,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,yBAAyB,EAAE,gCAAgC;AACjE,MAAM,iBAAiB,EAAE,+CAA+C;AACxE,MAAM,oBAAoB,EAAE,0EAA0E;AACtG,MAAM,YAAY,EAAE,CAAC,4CAA4C,EAAE,gBAAgB,CAAC,GAAG,CAAC;AACxF,MAAM,mBAAmB,EAAE,kCAAkC;AAC7D,MAAM,aAAa,EAAE,2CAA2C;AAChE,MAAM,kBAAkB,EAAE,CAAC,8DAA8D,EAAE,gBAAgB,CAAC,GAAG;AAC/G;AACA,GAAG;AACH,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,OAAO;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,IAAI;AACzB,EAAE,IAAI,UAAU,GAAG,IAAI;AACvB,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,MAAM,YAAY,GAAG,YAAY;AACnC,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,KAAK,GAAG,CAAC,CAAC,iBAAiB;AACjC,MAAM;AACN,IAAI;AACJ,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE;AACrC,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC;AAC5C,MAAM,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE;AACxE,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,IAAI,EAAE;AACd;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACxB,QAAQ,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC/C,QAAQ,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC,mBAAmB,CAAC;AACnE,MAAM;AACN,MAAM,aAAa,GAAG,IAAI;AAC1B,MAAM,eAAe,EAAE;AACvB,MAAM,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC;AAC9D,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,UAAU,CAAC,KAAK,GAAG,IAAI;AAC/B,MAAM;AACN,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,WAAW,EAAE;AACvB,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,KAAK,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,aAAa;AAClE,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,YAAY,GAAG,KAAK;AAC1B,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,MAAM,EAAE;AACZ,EAAE,CAAC;AACH,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,IAAI;AACR,IAAI,KAAK,EAAE,CAAC,CAAC,WAAW;AACxB,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,QAAQ,EAAE,YAAY;AAC1B,IAAI,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;AACrD,IAAI,UAAU,EAAE,CAAC,CAAC,MAAM;AACxB,IAAI,cAAc,EAAE,CAAC,YAAY,IAAI,YAAY;AACjD,IAAI,cAAc,EAAE,YAAY;AAChC,IAAI,YAAY;AAChB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAAC,CAAC;AACtH,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACtC,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AAC/F,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4EAA4E,EAAE,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,+FAA+F,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9V,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,oCAAoC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnL,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAClD,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE;AAC3C,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,cAAc,EAAE,yBAAyB;AAC/C,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,MAAM,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM;AAC9E,OAAO;AACP,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,aAAa,EAAE,sEAAsE;AAC3F,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,gBAAgB,EAAE;AACxB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,cAAc,EAAE,4BAA4B;AAClD,MAAM,WAAW,EAAE,+BAA+B;AAClD,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,cAAc,EAAE,wBAAwB;AAC9C,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,MAAM,EAAE;AAChB,UAAU,KAAK,EAAE,eAAe;AAChC,UAAU,SAAS,EAAE,WAAW;AAChC,UAAU,IAAI,EAAE;AAChB;AACA,OAAO;AACP,MAAM,YAAY,EAAE,2BAA2B;AAC/C,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,aAAa,EAAE,kEAAkE;AACvF,MAAM,cAAc,EAAE,mBAAmB;AACzC,MAAM,gBAAgB,EAAE;AACxB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACrD,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE;AACpB,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,MAAM,IAAI,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,EAAE,MAAM,WAAW,GAAG,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,EAAE,SAAS,OAAO,GAAG;AACrB,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC5B,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gMAAgM,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,uEAAuE,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,gDAAgD,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,iHAAiH,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE;AACv2B,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,aAAa,EAAE,QAAQ;AAC3B,IAAI,iBAAiB,EAAE;AACvB,GAAG,CAAC,CAAC,oEAAoE,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,uGAAuG,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,4FAA4F,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,uNAAuN,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,2IAA2I,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,wLAAwL,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,sOAAsO,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,iMAAiM,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,iBAAiB,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,gMAAgM,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,mCAAmC,CAAC,CAAC;AACxkE,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;AACzE,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8FAA8F,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,2CAA2C,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC;AAChU,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iCAAiC,CAAC,CAAC;AACzD,EAAE,kBAAkB,CAAC,SAAS,EAAE;AAChC,IAAI,MAAM;AACV,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,MAAM,EAAE,MAAM,aAAa,GAAG,KAAK;AACvC,IAAI,QAAQ,EAAE,IAAI,IAAI,IAAI;AAC1B,IAAI,gBAAgB,EAAE;AACtB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,kBAAkB,CAAC,SAAS,EAAE;AAChC,IAAI,MAAM;AACV,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,MAAM,EAAE,MAAM,eAAe,GAAG,KAAK;AACzC,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI,eAAe,EAAE;AACrB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;;;;"}