{"version": 3, "file": "12-BEtKNiNX.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_id_/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/12.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, params, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    me,\n    [commune],\n    members\n  ] = await Promise.all([\n    api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null),\n    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),\n    api.commune.member.list.get({ communeId: params.id }, { fetch, ctx: { url } })\n  ]);\n  if (!commune) {\n    throw error(404, \"Commune not found\");\n  }\n  const isLoggedIn = !!me;\n  const isAdmin = me?.role === \"admin\";\n  const isHeadMember = isLoggedIn && commune.headMember.actorType === \"user\" && commune.headMember.actorId === me.id;\n  const isMember = isLoggedIn && members.some(\n    (member) => member.actorType === \"user\" && member.actorId === me.id && !member.deletedAt\n  );\n  let hasPendingJoinRequest = false;\n  if (isLoggedIn && !isMember) {\n    const joinRequests = await api.commune.joinRequest.list.get(\n      {},\n      { fetch, ctx: { url } }\n    );\n    hasPendingJoinRequest = joinRequests.some(\n      ({ communeId, status }) => communeId === params.id && status === \"pending\"\n    );\n  }\n  return {\n    commune,\n    members,\n    userPermissions: {\n      isLoggedIn,\n      isAdmin,\n      isHeadMember,\n      isMember,\n      canInvite: isAdmin || isHeadMember,\n      canRequestJoin: isLoggedIn && !isMember && !hasPendingJoinRequest,\n      hasPendingJoinRequest\n    }\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/communes/_id_/_page.ts.js';\n\nexport const index = 12;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/_id_/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/communes/[id]/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/12.NVieDlgI.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/CaC9IHEK.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/Cx19LsLk.js\",\"_app/immutable/chunks/DiZKRWcx.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/KKeKRB0S.js\",\"_app/immutable/chunks/CBe4EX5h.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/CR3e0W7L.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/BiLRrsV0.js\",\"_app/immutable/chunks/CL12WlkV.js\",\"_app/immutable/chunks/C_wziyCN.js\"];\nexport const stylesheets = [\"_app/immutable/assets/create-post-modal.BRelZfpq.css\",\"_app/immutable/assets/12.DBKDFuDx.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC/C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,EAAE;AACN,IAAI,CAAC,OAAO,CAAC;AACb,IAAI;AACJ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;AACvE,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AACvE,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACjF,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AACzC,EAAE;AACF,EAAE,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE;AACzB,EAAE,MAAM,OAAO,GAAG,EAAE,EAAE,IAAI,KAAK,OAAO;AACtC,EAAE,MAAM,YAAY,GAAG,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,KAAK,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE,CAAC,EAAE;AACpH,EAAE,MAAM,QAAQ,GAAG,UAAU,IAAI,OAAO,CAAC,IAAI;AAC7C,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,SAAS,KAAK,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACnF,GAAG;AACH,EAAE,IAAI,qBAAqB,GAAG,KAAK;AACnC,EAAE,IAAI,UAAU,IAAI,CAAC,QAAQ,EAAE;AAC/B,IAAI,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG;AAC/D,MAAM,EAAE;AACR,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AAC3B,KAAK;AACL,IAAI,qBAAqB,GAAG,YAAY,CAAC,IAAI;AAC7C,MAAM,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,SAAS,KAAK,MAAM,CAAC,EAAE,IAAI,MAAM,KAAK;AACvE,KAAK;AACL,EAAE;AACF,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,eAAe,EAAE;AACrB,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,QAAQ;AACd,MAAM,SAAS,EAAE,OAAO,IAAI,YAAY;AACxC,MAAM,cAAc,EAAE,UAAU,IAAI,CAAC,QAAQ,IAAI,CAAC,qBAAqB;AACvE,MAAM;AACN;AACA,GAAG;AACH,CAAC;;;;;;;AC3CW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAmE,CAAC,EAAE;AAEjI,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC72B,MAAC,WAAW,GAAG,CAAC,sDAAsD,CAAC,uCAAuC;AAC9G,MAAC,KAAK,GAAG;;;;"}