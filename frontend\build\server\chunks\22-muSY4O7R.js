import { e as error } from './index-CT944rr3.js';
import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './schema-CmMg_B_X.js';

const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    me,
    [user],
    feedbacks
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.rating.feedback.list.get({ userId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!user) {
    throw error(404, "User not found");
  }
  return {
    me,
    user,
    feedbacks,
    isHasMoreFeedbacks: feedbacks.length === consts_exports.PAGE_SIZE
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 22;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CV4mPmgu.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/users/[id]/feedback/+page.ts";
const imports = ["_app/immutable/nodes/22.xnr8Ake3.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/CBe4EX5h.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/DGxS2cwR.js"];
const stylesheets = ["_app/immutable/assets/create-post-modal.BRelZfpq.css","_app/immutable/assets/22.zWDzjYRs.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=22-muSY4O7R.js.map
