{"version": 3, "file": "user-picker-3qltaNhi.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/user-picker.js"], "sourcesContent": ["import { u as push, z as escape_html, V as bind_props, w as pop } from \"./index.js\";\nimport { g as getClient } from \"./acrpc.js\";\n/* empty css                                                       */\nfunction User_picker($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      user: \"User\",\n      selectUser: \"Select user\",\n      searchUsers: \"Search users...\",\n      noUsersFound: \"No users found\",\n      loading: \"Loading...\",\n      clearSelection: \"Clear selection\"\n    },\n    ru: {\n      user: \"Пользователь\",\n      selectUser: \"Выбрать пользователя\",\n      searchUsers: \"Поиск пользователей...\",\n      noUsersFound: \"Пользователи не найдены\",\n      loading: \"Загрузка...\",\n      clearSelection: \"Очистить выбор\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  let { selectedUserId = void 0, locale, label, placeholder } = $$props;\n  const t = i18n[locale];\n  let showUserInput = false;\n  $$payload.out.push(`<div class=\"mb-3\">`);\n  if (label && showUserInput) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<label for=\"user-search-input\" class=\"form-label\">${escape_html(label)}</label>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"user-picker svelte-fbacwr\"><div class=\"selected-user d-flex align-items-center gap-2 mb-2 svelte-fbacwr\">`);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n    {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<button type=\"button\" class=\"btn btn-outline-secondary\"><i class=\"bi bi-person-plus\"></i> ${escape_html(t.selectUser)}</button>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n  bind_props($$props, { selectedUserId });\n  pop();\n}\nexport {\n  User_picker as U\n};\n"], "names": [], "mappings": ";;;AAEA;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,cAAc,EAAE;AACtB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,WAAW,EAAE,wBAAwB;AAC3C,MAAM,YAAY,EAAE,yBAAyB;AAC7C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,cAAc,EAAE;AACtB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,IAAI,EAAE,cAAc,GAAG,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,OAAO;AACvE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAExB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAGS;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8HAA8H,CAAC,CAAC;AACtJ,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0FAA0F,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC;AAC3J,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,CAAC;AACzC,EAAE,GAAG,EAAE;AACP;;;;"}