{"version": 3, "file": "21-C_aHu0Q8.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/users/_id_/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/21.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, params, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    [user],\n    note,\n    summary\n  ] = await Promise.all([\n    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),\n    api.user.note.get({ userId: params.id }, { fetch, ctx: { url } }),\n    api.rating.summary.get({ userId: params.id }, { fetch, ctx: { url } })\n  ]);\n  if (!user) {\n    throw error(404, \"User not found\");\n  }\n  return {\n    user,\n    userNote: note.text,\n    ratingSummary: summary\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/users/_id_/_page.ts.js';\n\nexport const index = 21;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/users/_id_/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/users/[id]/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/21.CVybIU1L.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/CaC9IHEK.js\",\"_app/immutable/chunks/DiZKRWcx.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/DGxS2cwR.js\"];\nexport const stylesheets = [\"_app/immutable/assets/21.BU79Yo5H.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC/C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,CAAC,IAAI,CAAC;AACV,IAAI,IAAI;AACR,IAAI;AACJ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AACpE,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AACrE,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACzE,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC;AACtC,EAAE;AACF,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI;AACvB,IAAI,aAAa,EAAE;AACnB,GAAG;AACH,CAAC;;;;;;;ACnBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAgE,CAAC,EAAE;AAE9H,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7kB,MAAC,WAAW,GAAG,CAAC,uCAAuC;AACvD,MAAC,KAAK,GAAG;;;;"}