import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { Common, Reactor } from "@commune/api";
export declare class ReactorLensService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    private generateSql;
    getLenses(user: CurrentUser): Promise<{
        id: string;
        code: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: string;
        userId: string;
        sql: string;
    }[]>;
    private generateLensSql;
    createLens(dto: {
        name: string;
        code: string;
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateLens(input: Reactor.UpdateLensInput, user: CurrentUser): Promise<boolean>;
    deleteLens(input: Common.ObjectWithId, user: CurrentUser): Promise<void>;
}
