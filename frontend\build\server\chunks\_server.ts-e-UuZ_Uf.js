import { A as API_URL } from './private-DXTbUqrb.js';

async function proxy(event) {
  const { request, params, url } = event;
  const slugPath = params.slug ?? "";
  const externalUrl = new URL(`${API_URL}/${slugPath}`);
  externalUrl.search = url.search;
  const init = {
    method: request.method,
    headers: {},
    redirect: "manual"
  };
  for (const [key, value] of request.headers) {
    if (key.toLowerCase() === "host") continue;
    init.headers[key] = value;
  }
  if (request.method !== "GET" && request.method !== "HEAD") {
    init.body = await request.arrayBuffer();
  }
  const response = await fetch(externalUrl.toString(), init);
  const headers = new Headers(response.headers);
  headers.delete("connection");
  headers.delete("keep-alive");
  headers.delete("transfer-encoding");
  headers.delete("upgrade");
  return new Response(response.body, {
    status: response.status,
    headers
  });
}
const GET = proxy;
const POST = proxy;
const PUT = proxy;
const PATCH = proxy;
const DELETE = proxy;
const OPTIONS = proxy;

export { DELETE, GET, OPTIONS, PATCH, POST, PUT };
//# sourceMappingURL=_server.ts-e-UuZ_Uf.js.map
