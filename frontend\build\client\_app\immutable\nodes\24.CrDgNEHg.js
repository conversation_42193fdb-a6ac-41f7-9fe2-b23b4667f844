import"../chunks/Bzak7iHL.js";import{p as Ye,av as x,aw as le,o as He,g as e,ax as l,f as D,d as t,s as i,t as y,b as m,c as Me,u as X,r as u,ay as Ne,a as $e,az as de}from"../chunks/RHWQbow4.js";import{d as ze,e as Ve,s as o}from"../chunks/BlWcudmi.js";import{i as E}from"../chunks/CtoItwj4.js";import{s as Z,a as ve,r as pe}from"../chunks/BdpLTtcP.js";import{s as ce}from"../chunks/Cxg-bych.js";import{s as me}from"../chunks/CaC9IHEK.js";import{g as ge}from"../chunks/KKeKRB0S.js";import{p as be}from"../chunks/Cx19LsLk.js";import{L as _e,s as fe}from"../chunks/CVTn1FV4.js";import{g as Ge}from"../chunks/CSZ3sDel.js";const Je=async(g,d,c,q)=>{if(g.preventDefault(),!!e(d)){l(c,"pending");try{const{isSent:F}=await q.auth.otp.post({email:e(d)},{skipInterceptor:!0});l(c,F?"sent":"sending-disabled-by-server",!0)}catch(F){console.error("Failed to send OTP:",F),l(c,"error")}}};var Ke=(g,d)=>l(d,"login"),Qe=(g,d)=>l(d,"register"),We=(g,d)=>l(d,g.target.value,!0),Xe=D('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>'),Ze=D('<span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill"> </span>'),er=D('<span class="badge bg-warning-subtle text-warning px-2 py-1 rounded-pill"> </span>'),rr=(g,d)=>{const c=g.target.value.replace(/\D/g,"");c.length<=6&&l(d,c,!0)},tr=D('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>'),ur=D('<span class="text-danger"> <br/> </span>'),ar=D('<div class="mt-4 p-3 border rounded bg-light"><h6 class="text-warning mb-3"><i class="bi bi-exclamation-triangle-fill me-2"></i> </h6> <p class="mb-3 text-muted"> </p> <div class="d-flex gap-2"><a target="_blank" rel="noopener noreferrer" class="btn btn-outline-primary btn-sm"><i class="bi bi-telegram me-1"></i> </a></div></div>'),ir=D('<div class="container min-vh-100 d-flex align-items-center justify-content-center"><div class="card shadow-lg border-0"><div class="card-header bg-white border-0 pt-4 pb-0"><div class="position-relative"><ul class="nav nav-tabs border-0 card-header-tabs"><li class="nav-item flex-grow-1 text-center"><button> </button></li> <li class="nav-item flex-grow-1 text-center"><button> </button></li></ul> <div class="position-absolute bottom-0 bg-primary"></div></div></div> <div class="card-body p-4"><form><div class="mb-3"><label for="email" class="form-label"> </label> <input type="email" autoComplete="email" class="form-control" id="email" placeholder="<EMAIL>" required/></div> <div class="mb-3"><button type="button" class="btn btn-outline-primary w-100"><!> </button></div> <div class="mb-3"><div class="d-flex justify-content-between align-items-center mb-2"><label for="otp" class="form-label mb-0"> </label> <!></div> <input id="otp" class="form-control" type="text" aria-describedby="otpHelp"/> <div id="otpHelp" class="form-text"> </div></div> <div class="d-grid gap-2"><button type="submit" class="btn btn-primary"><!> <!></button> <!></div></form> <!></div></div></div>');function _r(g,d){Ye(d,!0);const{fetcher:c}=Ge(),q={en:{_page:{title:"Auth — Commune"},authType:{login:"Login",register:"Register"},login:"Log In",register:"Create Account",email:"Email",otp:{short:"OTP",long:"Email Verification Code",send:"Send OTP",enterThe6DigitCodeSentToYourEmail:"Enter the 6-digit code sent to your email.",sent:"OTP Sent",sendingDisabledByServer:"OTP sending is disabled by the server",placeholder:"6-digit code"},failedToSubmitPleaseTryAgain:"Failed to submit. Please try again.",invite:{required:"Invitation Required",notInvited:"You are not invited yet, write to one of our chats.",telegram:"Telegram",telegramLink:"https://t.me/ds_commune_en"}},ru:{_page:{title:"Вход — Коммуна"},authType:{login:"Вход",register:"Регистрация"},login:"Войти",register:"Создать аккаунт",email:"Email",otp:{short:"OTP",long:"Код проверки почты",send:"Отправить OTP",enterThe6DigitCodeSentToYourEmail:"Введите 6-значный код, отправленный на ваш email.",sent:"OTP отправлен",sendingDisabledByServer:"Отправка OTP отключена сервером",placeholder:"Шестизначный код"},failedToSubmitPleaseTryAgain:"Не удалось отправить. Пожалуйста, попробуйте снова.",invite:{required:"Требуется приглашение",notInvited:"Вы ещё не приглашены, напишите в один из наших чатов.",telegram:"Telegram",telegramLink:"https://t.me/ds_commune_ru"}}},F=X(()=>d.data.locale),s=X(()=>q[e(F)]);var B=(r=>(r.None="none",r.Pending="pending",r.Sent="sent",r.SendingDisabledByServer="sending-disabled-by-server",r.Error="error",r))(B||{}),O=(r=>(r.None="none",r.Pending="pending",r.Error="error",r))(O||{});let b=x("login"),_=x(le("")),h=x(le("")),P=x("none"),T=x("none"),U=x(null),j=x(!1);const ee=X(()=>e(P)==="pending"||e(T)==="pending");He(()=>{e(b),e(_),l(j,!1)});const he=async r=>{var a,v,f;if(r.preventDefault(),!(!e(_)||!e(h))){l(T,"pending");try{if(e(b)==="login"){const n=await c.auth.signIn.post({email:e(_),otp:e(h)},{skipInterceptor:!0}),p=_e.parse(n);fe(p),ge(be.url.searchParams.get("redirectFrom")??"/")}else{const n=await c.auth.signUp.post({referrerId:null,email:e(_),otp:e(h)},{skipInterceptor:!0}),p=_e.parse(n);fe(p),ge(be.url.searchParams.get("redirectFrom")??"/")}}catch(n){if(console.error(n),e(b)==="register"&&n.status===403&&((a=n.description)!=null&&a.includes("must_have_invite"))){l(j,!0),l(T,"none"),l(U,null);return}const p=((f=(v=n.response)==null?void 0:v.body)==null?void 0:f.error)||n.message;l(T,"error"),l(U,p,!0)}}};var R=ir(),Y=t(R);me(Y,"",{},{width:"100%","max-width":"400px"});var H=t(Y),re=t(H),M=t(re),N=t(M),k=t(N);k.__click=[Ke,b];var xe=t(k,!0);u(k),u(N);var te=i(N,2),C=t(te);C.__click=[Qe,b];var ye=t(C,!0);u(C),u(te),u(M);var Ee=i(M,2);let ue;u(re),u(H);var ae=i(H,2),A=t(ae),$=t(A),z=t($),De=t(z,!0);u(z);var V=i(z,2);pe(V),V.__input=[We,_],u($);var G=i($,2),S=t(G);S.__click=[Je,_,P,c];var ie=t(S);{var Te=r=>{var a=Xe();m(r,a)};E(ie,r=>{e(P)===B.Pending&&r(Te)})}var we=i(ie);u(S),u(G);var J=i(G,2),K=t(J),Q=t(K),Fe=t(Q,!0);u(Q);var Pe=i(Q,2);{var Be=r=>{var a=Ze(),v=t(a,!0);u(a),y(()=>o(v,e(s).otp.sent)),m(r,a)},ke=r=>{var a=Ne(),v=$e(a);{var f=n=>{var p=er(),L=t(p,!0);u(p),y(()=>o(L,e(s).otp.sendingDisabledByServer)),m(n,p)};E(v,n=>{e(P)===B.SendingDisabledByServer&&n(f)},!0)}m(r,a)};E(Pe,r=>{e(P)===B.Sent?r(Be):r(ke,!1)})}u(K);var w=i(K,2);pe(w),w.__input=[rr,h],Z(w,"maxlength",6);var ne=i(w,2),Ce=t(ne,!0);u(ne),u(J);var se=i(J,2),I=t(se),oe=t(I);{var Ae=r=>{var a=tr();m(r,a)};E(oe,r=>{e(T)===O.Pending&&e(h)&&r(Ae)})}var Se=i(oe,2);{var Ie=r=>{var a=de();y(()=>o(a,e(s).login)),m(r,a)},Le=r=>{var a=de();y(()=>o(a,e(s).register)),m(r,a)};E(Se,r=>{e(b)==="login"?r(Ie):r(Le,!1)})}u(I);var qe=i(I,2);{var Oe=r=>{var a=ur(),v=t(a),f=i(v,2);u(a),y(()=>{o(v,`${e(s).failedToSubmitPleaseTryAgain??""} `),o(f,` ${e(U)??""}`)}),m(r,a)};E(qe,r=>{e(T)===O.Error&&r(Oe)})}u(se),u(A);var Ue=i(A,2);{var je=r=>{var a=ar(),v=t(a),f=i(t(v));u(v);var n=i(v,2),p=t(n,!0);u(n);var L=i(n,2),W=t(L),Re=i(t(W));u(W),u(L),u(a),y(()=>{o(f,` ${e(s).invite.required??""}`),o(p,e(s).invite.notInvited),Z(W,"href",e(s).invite.telegramLink),o(Re,` ${e(s).invite.telegram??""}`)}),m(r,a)};E(Ue,r=>{e(j)&&r(je)})}u(ae),u(Y),u(R),y(r=>{ce(k,1,`nav-link border-0 w-100 ${e(b)==="login"?"active":""}`,"svelte-il0jyn"),o(xe,e(s).authType.login),ce(C,1,`nav-link border-0 w-100 ${e(b)==="register"?"active":""}`,"svelte-il0jyn"),o(ye,e(s).authType.register),ue=me(Ee,"",ue,r),o(De,e(s).email),ve(V,e(_)),S.disabled=!e(_)||e(ee),o(we,` ${e(s).otp.send??""}`),o(Fe,e(s).otp.long),ve(w,e(h)),Z(w,"placeholder",e(s).otp.placeholder),o(Ce,e(s).otp.enterThe6DigitCodeSentToYourEmail),I.disabled=!e(_)||!e(h)||e(ee)},[()=>({height:"3px",width:"50%",left:e(b)==="login"?"0":"50%",transition:"left 0.3s ease-in-out",borderRadius:"3px 3px 0 0"})]),Ve("submit",A,he),m(g,R),Me()}ze(["click","input"]);export{_r as component};
