import { Common } from "@commune/api";
import { Injectable, OnModuleInit } from "@nestjs/common";
import { createTransport, Transporter } from "nodemailer";
import SMTPTransport from "nodemailer/lib/smtp-transport";
import { ConfigService } from "src/config/config.service";
import { generateInviteText } from "./invite-text";

type SendEmailDto = {
    from: string;
    to: string[];
    subject: string;
    text: string;
};

@Injectable()
export class EmailService implements OnModuleInit {
    private transporter: Transporter;

    constructor(private readonly configService: ConfigService) {}

    onModuleInit() {
        const options = {
            host: this.configService.config.email.host,
            secure: false,
            port: this.configService.config.email.port,

            auth: {
                user: this.configService.config.email.user,
                pass: this.configService.config.email.pass,
            },

            tls: {
                rejectUnauthorized:
                    this.configService.config.email.rejectUnauthorized,
            },
        } satisfies SMTPTransport.Options;

        // console.log({ options });

        this.transporter = createTransport(options);
    }

    joinAddress(sender: string, domain: string) {
        return `${sender}@${domain}`;
    }

    async send(dto: SendEmailDto) {
        if (this.configService.config.email.disableAllEmails) {
            return false;
        }

        try {
            await this.transporter.verify();

            await this.transporter.sendMail({
                from: dto.from,
                to: dto.to,
                subject: dto.subject,
                text: dto.text,
            });

            return true;
        } catch (e) {
            if (!this.configService.config.email.ignoreErrors) {
                throw e;
            }

            return false;
        }
    }

    async sendOtp(dto: { to: string; otp: string }) {
        if (this.configService.config.email.disableOtpEmails) {
            return false;
        }

        return await this.send({
            from: this.joinAddress(
                this.configService.config.email.otpEmailSender,
                this.configService.config.instance.emailDomain,
            ),
            to: [dto.to],
            subject: `${this.configService.config.instance.name} - OTP`,
            text: `Your OTP is ${dto.otp}.`,
        });
    }

    async sendInvite(dto: {
        to: string;
        name: string | null;
        locale: Common.LocalizationLocale;
    }) {
        if (this.configService.config.email.disableInviteEmails) {
            return false;
        }

        const subject: Record<Common.LocalizationLocale, string> = {
            ru: "Коммуна — приглашение",
            en: "Commune — invite",
        };

        return await this.send({
            from: this.joinAddress(
                this.configService.config.email.otpEmailSender,
                this.configService.config.instance.emailDomain,
            ),
            to: [dto.to],
            subject: subject[dto.locale],
            text: generateInviteText(dto.name, dto.locale),
        });
    }
}
