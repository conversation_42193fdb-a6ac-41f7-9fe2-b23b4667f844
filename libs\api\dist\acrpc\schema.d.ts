import { Transformer } from './core.js';
import * as zod from 'zod';
import '@ocelotjungle/case-converters';

declare const CACHE_CONTROL_TEN_MINUTES = "no-cache";
declare const CACHE_CONTROL_HOUR = "no-cache";
declare const CACHE_CONTROL_IMMUTABLE = "no-cache";
declare const DEFAULT_CACHE_CONTROL = "no-cache";
declare const schema: {
    auth: {
        otp: {
            post: {
                input: zod.ZodObject<{
                    email: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    email: string;
                }, {
                    email: string;
                }>;
                output: zod.ZodObject<{
                    isSent: zod.ZodBoolean;
                }, "strip", zod.ZodTypeAny, {
                    isSent: boolean;
                }, {
                    isSent: boolean;
                }>;
                isMetadataUsed: false;
            };
        };
        signUp: {
            post: {
                input: zod.ZodObject<{
                    referrerId: zod.ZodNullable<zod.ZodString>;
                    email: zod.ZodString;
                    otp: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    email: string;
                    referrerId: string | null;
                    otp: string;
                }, {
                    email: string;
                    referrerId: string | null;
                    otp: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                    email: zod.ZodString;
                    role: zod.ZodEnum<["admin", "moderator", "user"]>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    email: string;
                    role: "admin" | "moderator" | "user";
                }, {
                    id: string;
                    email: string;
                    role: "admin" | "moderator" | "user";
                }>;
                isMetadataUsed: false;
                invalidate: string[];
            };
        };
        signIn: {
            post: {
                input: zod.ZodObject<{
                    email: zod.ZodString;
                    otp: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    email: string;
                    otp: string;
                }, {
                    email: string;
                    otp: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                    email: zod.ZodString;
                    role: zod.ZodEnum<["admin", "moderator", "user"]>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    email: string;
                    role: "admin" | "moderator" | "user";
                }, {
                    id: string;
                    email: string;
                    role: "admin" | "moderator" | "user";
                }>;
                isMetadataUsed: false;
                invalidate: string[];
            };
        };
        signOut: {
            get: {
                input: null;
                output: null;
                isMetadataUsed: false;
                invalidate: string[];
            };
        };
    };
    commune: {
        transferHeadStatus: {
            post: {
                input: zod.ZodObject<{
                    communeId: zod.ZodString;
                    newHeadUserId: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    communeId: string;
                    newHeadUserId: string;
                }, {
                    communeId: string;
                    newHeadUserId: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        list: {
            get: {
                input: zod.ZodObject<{
                    pagination: zod.ZodOptional<zod.ZodDefault<zod.ZodObject<{
                        page: zod.ZodDefault<zod.ZodNumber>;
                        size: zod.ZodDefault<zod.ZodNumber>;
                    }, "strip", zod.ZodTypeAny, {
                        page: number;
                        size: number;
                    }, {
                        page?: number | undefined;
                        size?: number | undefined;
                    }>>>;
                    ids: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                    query: zod.ZodOptional<zod.ZodString>;
                    userId: zod.ZodOptional<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    pagination?: {
                        page: number;
                        size: number;
                    } | undefined;
                    ids?: string[] | undefined;
                    query?: string | undefined;
                    userId?: string | undefined;
                }, {
                    pagination?: {
                        page?: number | undefined;
                        size?: number | undefined;
                    } | undefined;
                    ids?: string[] | undefined;
                    query?: string | undefined;
                    userId?: string | undefined;
                }>;
                output: zod.ZodArray<zod.ZodObject<{
                    id: zod.ZodString;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    headMember: zod.ZodObject<{
                        actorType: zod.ZodEnum<["user"]>;
                        actorId: zod.ZodString;
                        name: zod.ZodUnion<[zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">, zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">]>;
                        image: zod.ZodNullable<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    }, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    }>;
                    memberCount: zod.ZodNumber;
                    image: zod.ZodNullable<zod.ZodString>;
                    createdAt: zod.ZodDate;
                    updatedAt: zod.ZodDate;
                    deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                }, "strip", zod.ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    headMember: {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    };
                    memberCount: number;
                    deletedAt?: Date | null | undefined;
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    headMember: {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        image: string | null;
                        actorType: "user";
                        actorId: string;
                    };
                    memberCount: number;
                    deletedAt?: Date | null | undefined;
                }>, "many">;
                cacheControl: string;
                isMetadataRequired: false;
            };
        };
        post: {
            input: zod.ZodObject<{
                headUserId: zod.ZodOptional<zod.ZodString>;
                name: zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">;
                description: zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">;
            }, "strip", zod.ZodTypeAny, {
                description: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                headUserId?: string | undefined;
            }, {
                description: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                headUserId?: string | undefined;
            }>;
            output: zod.ZodObject<{
                id: zod.ZodString;
            }, "strip", zod.ZodTypeAny, {
                id: string;
            }, {
                id: string;
            }>;
            autoScopeInvalidationDepth: number;
        };
        patch: {
            input: zod.ZodObject<{
                id: zod.ZodString;
                name: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">>;
                description: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">>;
            }, "strip", zod.ZodTypeAny, {
                id: string;
                description?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
                name?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
            }, {
                id: string;
                description?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
                name?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
            }>;
            output: null;
            autoScopeInvalidationDepth: number;
        };
        delete: {
            input: zod.ZodObject<{
                id: zod.ZodString;
            }, "strip", zod.ZodTypeAny, {
                id: string;
            }, {
                id: string;
            }>;
            output: null;
            autoScopeInvalidationDepth: number;
        };
        member: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                        communeId: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                        communeId: string;
                    }, {
                        communeId: string;
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        actorType: zod.ZodEnum<["user"]>;
                        actorId: zod.ZodString;
                        name: zod.ZodUnion<[zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">, zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">]>;
                        image: zod.ZodNullable<zod.ZodString>;
                        createdAt: zod.ZodDate;
                        deletedAt: zod.ZodNullable<zod.ZodNullable<zod.ZodDate>>;
                    }, "strip", zod.ZodTypeAny, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        image: string | null;
                        deletedAt: Date | null;
                        actorType: "user";
                        actorId: string;
                    }, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        image: string | null;
                        deletedAt: Date | null;
                        actorType: "user";
                        actorId: string;
                    }>, "many">;
                    cacheControl: string;
                    isMetadataRequired: false;
                };
            };
            post: {
                input: zod.ZodObject<{
                    communeId: zod.ZodString;
                    userId: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    userId: string;
                    communeId: string;
                }, {
                    userId: string;
                    communeId: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        invitation: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                        communeId: zod.ZodOptional<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                        communeId?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        communeId?: string | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        communeId: zod.ZodString;
                        userId: zod.ZodString;
                        status: zod.ZodEnum<["pending", "accepted", "rejected", "expired"]>;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                    }, "strip", zod.ZodTypeAny, {
                        status: "pending" | "accepted" | "rejected" | "expired";
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        userId: string;
                        communeId: string;
                    }, {
                        status: "pending" | "accepted" | "rejected" | "expired";
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        userId: string;
                        communeId: string;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: zod.ZodObject<{
                    communeId: zod.ZodString;
                    userId: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    userId: string;
                    communeId: string;
                }, {
                    userId: string;
                    communeId: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            accept: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    invalidate: string[];
                };
            };
            reject: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    invalidate: string[];
                };
            };
        };
        joinRequest: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                        communeId: zod.ZodOptional<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                        communeId?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        communeId?: string | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        communeId: zod.ZodString;
                        userId: zod.ZodString;
                        status: zod.ZodEnum<["pending", "accepted", "rejected"]>;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                    }, "strip", zod.ZodTypeAny, {
                        status: "pending" | "accepted" | "rejected";
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        userId: string;
                        communeId: string;
                    }, {
                        status: "pending" | "accepted" | "rejected";
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        userId: string;
                        communeId: string;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: zod.ZodObject<{
                    communeId: zod.ZodString;
                    userId: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    userId: string;
                    communeId: string;
                }, {
                    userId: string;
                    communeId: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            accept: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    invalidate: string[];
                };
            };
            reject: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                    }, {
                        id: string;
                    }>;
                    output: null;
                    invalidate: string[];
                };
            };
        };
    };
    rating: {
        karma: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                        userId: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                        userId: string;
                    }, {
                        userId: string;
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        author: zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>;
                        quantity: zod.ZodNumber;
                        comment: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        comment: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        quantity: number;
                    }, {
                        id: string;
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        comment: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        quantity: number;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: zod.ZodObject<{
                    sourceUserId: zod.ZodString;
                    targetUserId: zod.ZodString;
                    quantity: zod.ZodNumber;
                    comment: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", zod.ZodTypeAny, {
                    comment: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    quantity: number;
                    sourceUserId: string;
                    targetUserId: string;
                }, {
                    comment: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    quantity: number;
                    sourceUserId: string;
                    targetUserId: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
                invalidate: string[];
            };
        };
        feedback: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                        userId: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                        userId: string;
                    }, {
                        userId: string;
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        author: zod.ZodNullable<zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>>;
                        isAnonymous: zod.ZodBoolean;
                        value: zod.ZodNumber;
                        text: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                    }, "strip", zod.ZodTypeAny, {
                        value: number;
                        id: string;
                        text: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        isAnonymous: boolean;
                    }, {
                        value: number;
                        id: string;
                        text: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        isAnonymous: boolean;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: zod.ZodObject<{
                    sourceUserId: zod.ZodString;
                    targetUserId: zod.ZodString;
                    value: zod.ZodNumber;
                    isAnonymous: zod.ZodBoolean;
                    text: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", zod.ZodTypeAny, {
                    value: number;
                    text: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    isAnonymous: boolean;
                    sourceUserId: string;
                    targetUserId: string;
                }, {
                    value: number;
                    text: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    isAnonymous: boolean;
                    sourceUserId: string;
                    targetUserId: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
                invalidate: string[];
            };
        };
        summary: {
            get: {
                input: zod.ZodObject<{
                    userId: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    userId: string;
                }, {
                    userId: string;
                }>;
                output: zod.ZodObject<{
                    rating: zod.ZodNumber;
                    karma: zod.ZodNumber;
                    rate: zod.ZodNullable<zod.ZodNumber>;
                }, "strip", zod.ZodTypeAny, {
                    rating: number;
                    karma: number;
                    rate: number | null;
                }, {
                    rating: number;
                    karma: number;
                    rate: number | null;
                }>;
                cacheControl: string;
            };
        };
    };
    reactor: {
        post: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                        id: zod.ZodOptional<zod.ZodString>;
                        lensId: zod.ZodNullable<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                        lensId: string | null;
                        id?: string | undefined;
                    }, {
                        lensId: string | null;
                        id?: string | undefined;
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        hub: zod.ZodNullable<zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>>;
                        community: zod.ZodNullable<zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>>;
                        author: zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>;
                        rating: zod.ZodObject<{
                            likes: zod.ZodNumber;
                            dislikes: zod.ZodNumber;
                            status: zod.ZodNullable<zod.ZodEnum<["like", "dislike"]>>;
                        }, "strip", zod.ZodTypeAny, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }>;
                        usefulness: zod.ZodObject<{
                            value: zod.ZodNullable<zod.ZodNumber>;
                            count: zod.ZodNumber;
                            totalValue: zod.ZodNullable<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            value: number | null;
                            count: number;
                            totalValue: number | null;
                        }, {
                            value: number | null;
                            count: number;
                            totalValue: number | null;
                        }>;
                        title: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        body: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        tags: zod.ZodArray<zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                        }>, "many">;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                        deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        hub: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        community: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        rating: {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        };
                        usefulness: {
                            value: number | null;
                            count: number;
                            totalValue: number | null;
                        };
                        title: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        tags: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                        }[];
                        deletedAt?: Date | null | undefined;
                    }, {
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        hub: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        community: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        rating: {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        };
                        usefulness: {
                            value: number | null;
                            count: number;
                            totalValue: number | null;
                        };
                        title: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        tags: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                        }[];
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                    isMetadataRequired: false;
                };
            };
            post: {
                input: zod.ZodObject<{
                    hubId: zod.ZodNullable<zod.ZodString>;
                    communityId: zod.ZodNullable<zod.ZodString>;
                    title: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    body: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    tagIds: zod.ZodArray<zod.ZodString, "many">;
                    imageIds: zod.ZodArray<zod.ZodString, "many">;
                }, "strip", zod.ZodTypeAny, {
                    title: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    body: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    hubId: string | null;
                    communityId: string | null;
                    tagIds: string[];
                    imageIds: string[];
                }, {
                    title: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    body: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    hubId: string | null;
                    communityId: string | null;
                    tagIds: string[];
                    imageIds: string[];
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    title: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    body: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    tagIds: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                    imageIds: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    title?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    body?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    tagIds?: string[] | undefined;
                    imageIds?: string[] | undefined;
                }, {
                    id: string;
                    title?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    body?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    tagIds?: string[] | undefined;
                    imageIds?: string[] | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    reason: zod.ZodNullable<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    reason: string | null;
                }, {
                    id: string;
                    reason: string | null;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            rating: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                        type: zod.ZodEnum<["like", "dislike"]>;
                    }, "strip", zod.ZodTypeAny, {
                        type: "like" | "dislike";
                        id: string;
                    }, {
                        type: "like" | "dislike";
                        id: string;
                    }>;
                    output: zod.ZodObject<{
                        likes: zod.ZodNumber;
                        dislikes: zod.ZodNumber;
                        status: zod.ZodNullable<zod.ZodEnum<["like", "dislike"]>>;
                    }, "strip", zod.ZodTypeAny, {
                        status: "like" | "dislike" | null;
                        likes: number;
                        dislikes: number;
                    }, {
                        status: "like" | "dislike" | null;
                        likes: number;
                        dislikes: number;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
            };
            usefulness: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                        value: zod.ZodNullable<zod.ZodNumber>;
                    }, "strip", zod.ZodTypeAny, {
                        value: number | null;
                        id: string;
                    }, {
                        value: number | null;
                        id: string;
                    }>;
                    output: zod.ZodObject<{
                        value: zod.ZodNullable<zod.ZodNumber>;
                        count: zod.ZodNumber;
                        totalValue: zod.ZodNullable<zod.ZodNumber>;
                    }, "strip", zod.ZodTypeAny, {
                        value: number | null;
                        count: number;
                        totalValue: number | null;
                    }, {
                        value: number | null;
                        count: number;
                        totalValue: number | null;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
            };
            image: {
                list: {
                    get: {
                        input: zod.ZodObject<{
                            id: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            id: string;
                        }, {
                            id: string;
                        }>;
                        output: zod.ZodArray<zod.ZodObject<{
                            id: zod.ZodString;
                            url: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            id: string;
                            url: string;
                        }, {
                            id: string;
                            url: string;
                        }>, "many">;
                        cacheControl: string;
                    };
                };
            };
        };
        comment: {
            list: {
                get: {
                    input: zod.ZodUnion<[zod.ZodObject<{
                        id: zod.ZodString;
                        entityType: zod.ZodOptional<zod.ZodNever>;
                        entityId: zod.ZodOptional<zod.ZodNever>;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                        entityType?: undefined;
                        entityId?: undefined;
                    }, {
                        id: string;
                        entityType?: undefined;
                        entityId?: undefined;
                    }>, zod.ZodObject<{
                        id: zod.ZodOptional<zod.ZodNever>;
                        entityType: zod.ZodEnum<["post", "comment"]>;
                        entityId: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        entityType: "post" | "comment";
                        entityId: string;
                        id?: undefined;
                    }, {
                        entityType: "post" | "comment";
                        entityId: string;
                        id?: undefined;
                    }>]>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        path: zod.ZodString;
                        author: zod.ZodNullable<zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>>;
                        isAnonymous: zod.ZodBoolean;
                        anonimityReason: zod.ZodNullable<zod.ZodString>;
                        rating: zod.ZodObject<{
                            likes: zod.ZodNumber;
                            dislikes: zod.ZodNumber;
                            status: zod.ZodNullable<zod.ZodEnum<["like", "dislike"]>>;
                        }, "strip", zod.ZodTypeAny, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }, {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        }>;
                        body: zod.ZodNullable<zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">>;
                        childrenCount: zod.ZodNumber;
                        deleteReason: zod.ZodNullable<zod.ZodString>;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                        deletedAt: zod.ZodNullable<zod.ZodDate>;
                    }, "strip", zod.ZodTypeAny, {
                        path: string;
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        deletedAt: Date | null;
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        rating: {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        };
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | null;
                        isAnonymous: boolean;
                        anonimityReason: string | null;
                        childrenCount: number;
                        deleteReason: string | null;
                    }, {
                        path: string;
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        deletedAt: Date | null;
                        author: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        rating: {
                            status: "like" | "dislike" | null;
                            likes: number;
                            dislikes: number;
                        };
                        body: {
                            value: string;
                            locale: "en" | "ru";
                        }[] | null;
                        isAnonymous: boolean;
                        anonimityReason: string | null;
                        childrenCount: number;
                        deleteReason: string | null;
                    }>, "many">;
                    cacheControl: string;
                    isMetadataRequired: false;
                };
            };
            post: {
                input: zod.ZodObject<{
                    entityType: zod.ZodEnum<["post", "comment"]>;
                    entityId: zod.ZodString;
                    body: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", zod.ZodTypeAny, {
                    body: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    entityType: "post" | "comment";
                    entityId: string;
                }, {
                    body: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    entityType: "post" | "comment";
                    entityId: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    body: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    body?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }, {
                    id: string;
                    body?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    reason: zod.ZodNullable<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    reason: string | null;
                }, {
                    id: string;
                    reason: string | null;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            rating: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                        type: zod.ZodEnum<["like", "dislike"]>;
                    }, "strip", zod.ZodTypeAny, {
                        type: "like" | "dislike";
                        id: string;
                    }, {
                        type: "like" | "dislike";
                        id: string;
                    }>;
                    output: zod.ZodObject<{
                        likes: zod.ZodNumber;
                        dislikes: zod.ZodNumber;
                        status: zod.ZodNullable<zod.ZodEnum<["like", "dislike"]>>;
                    }, "strip", zod.ZodTypeAny, {
                        status: "like" | "dislike" | null;
                        likes: number;
                        dislikes: number;
                    }, {
                        status: "like" | "dislike" | null;
                        likes: number;
                        dislikes: number;
                    }>;
                    autoScopeInvalidationDepth: number;
                };
            };
            anonimify: {
                post: {
                    input: zod.ZodObject<{
                        id: zod.ZodString;
                        reason: zod.ZodNullable<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        id: string;
                        reason: string | null;
                    }, {
                        id: string;
                        reason: string | null;
                    }>;
                    output: null;
                    autoScopeInvalidationDepth: number;
                };
            };
        };
        lens: {
            list: {
                get: {
                    input: null;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        name: zod.ZodString;
                        code: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        code: string;
                        name: string;
                        id: string;
                    }, {
                        code: string;
                        name: string;
                        id: string;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: zod.ZodObject<{
                    name: zod.ZodString;
                    code: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    code: string;
                    name: string;
                }, {
                    code: string;
                    name: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    name: zod.ZodOptional<zod.ZodString>;
                    code: zod.ZodOptional<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    code?: string | undefined;
                    name?: string | undefined;
                }, {
                    id: string;
                    code?: string | undefined;
                    name?: string | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        hub: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodOptional<zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>>;
                        ids: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                        query: zod.ZodOptional<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        pagination?: {
                            page: number;
                            size: number;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        headUser: zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>;
                        image: zod.ZodNullable<zod.ZodString>;
                        name: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                        deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                    }, "strip", zod.ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        image: string | null;
                        headUser: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        deletedAt?: Date | null | undefined;
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        image: string | null;
                        headUser: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                    isMetadataRequired: false;
                };
            };
            post: {
                input: zod.ZodObject<{
                    headUserId: zod.ZodNullable<zod.ZodString>;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", zod.ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    headUserId: string | null;
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    headUserId: string | null;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    name: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    description: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        community: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodOptional<zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>>;
                        ids: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                        query: zod.ZodOptional<zod.ZodString>;
                        hubId: zod.ZodOptional<zod.ZodString>;
                    }, "strip", zod.ZodTypeAny, {
                        pagination?: {
                            page: number;
                            size: number;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                        hubId?: string | undefined;
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                        ids?: string[] | undefined;
                        query?: string | undefined;
                        hubId?: string | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        hub: zod.ZodNullable<zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>>;
                        headUser: zod.ZodObject<{
                            id: zod.ZodString;
                            name: zod.ZodArray<zod.ZodObject<{
                                locale: zod.ZodEnum<["en", "ru"]>;
                                value: zod.ZodString;
                            }, "strip", zod.ZodTypeAny, {
                                value: string;
                                locale: "en" | "ru";
                            }, {
                                value: string;
                                locale: "en" | "ru";
                            }>, "many">;
                            image: zod.ZodNullable<zod.ZodString>;
                        }, "strip", zod.ZodTypeAny, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }, {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        }>;
                        image: zod.ZodNullable<zod.ZodString>;
                        name: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        description: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                        deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                    }, "strip", zod.ZodTypeAny, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        image: string | null;
                        hub: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        headUser: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        deletedAt?: Date | null | undefined;
                    }, {
                        description: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        image: string | null;
                        hub: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        } | null;
                        headUser: {
                            name: {
                                value: string;
                                locale: "en" | "ru";
                            }[];
                            id: string;
                            image: string | null;
                        };
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                    isMetadataRequired: false;
                };
            };
            post: {
                input: zod.ZodObject<{
                    hubId: zod.ZodNullable<zod.ZodString>;
                    headUserId: zod.ZodNullable<zod.ZodString>;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                }, "strip", zod.ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    headUserId: string | null;
                    hubId: string | null;
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    headUserId: string | null;
                    hubId: string | null;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    name: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    description: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }, {
                    id: string;
                    description?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
    };
    tag: {
        list: {
            get: {
                input: zod.ZodObject<{
                    pagination: zod.ZodOptional<zod.ZodDefault<zod.ZodObject<{
                        page: zod.ZodDefault<zod.ZodNumber>;
                        size: zod.ZodDefault<zod.ZodNumber>;
                    }, "strip", zod.ZodTypeAny, {
                        page: number;
                        size: number;
                    }, {
                        page?: number | undefined;
                        size?: number | undefined;
                    }>>>;
                    ids: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                    query: zod.ZodOptional<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    pagination?: {
                        page: number;
                        size: number;
                    } | undefined;
                    ids?: string[] | undefined;
                    query?: string | undefined;
                }, {
                    pagination?: {
                        page?: number | undefined;
                        size?: number | undefined;
                    } | undefined;
                    ids?: string[] | undefined;
                    query?: string | undefined;
                }>;
                output: zod.ZodArray<zod.ZodObject<{
                    id: zod.ZodString;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                }, "strip", zod.ZodTypeAny, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    deletedAt?: Date | null | undefined;
                }, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    deletedAt?: Date | null | undefined;
                }>, "many">;
                cacheControl: string;
            };
        };
        post: {
            input: zod.ZodObject<{
                name: zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">;
            }, "strip", zod.ZodTypeAny, {
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
            }, {
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
            }>;
            output: zod.ZodObject<{
                id: zod.ZodString;
            }, "strip", zod.ZodTypeAny, {
                id: string;
            }, {
                id: string;
            }>;
            autoScopeInvalidationDepth: number;
        };
        patch: {
            input: zod.ZodObject<{
                id: zod.ZodString;
                name: zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">;
            }, "strip", zod.ZodTypeAny, {
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                id: string;
            }, {
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                id: string;
            }>;
            output: null;
            autoScopeInvalidationDepth: number;
        };
        delete: {
            input: zod.ZodObject<{
                id: zod.ZodString;
            }, "strip", zod.ZodTypeAny, {
                id: string;
            }, {
                id: string;
            }>;
            output: null;
            autoScopeInvalidationDepth: number;
        };
    };
    user: {
        list: {
            get: {
                input: zod.ZodObject<{
                    pagination: zod.ZodOptional<zod.ZodDefault<zod.ZodObject<{
                        page: zod.ZodDefault<zod.ZodNumber>;
                        size: zod.ZodDefault<zod.ZodNumber>;
                    }, "strip", zod.ZodTypeAny, {
                        page: number;
                        size: number;
                    }, {
                        page?: number | undefined;
                        size?: number | undefined;
                    }>>>;
                    ids: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                    query: zod.ZodOptional<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    pagination?: {
                        page: number;
                        size: number;
                    } | undefined;
                    ids?: string[] | undefined;
                    query?: string | undefined;
                }, {
                    pagination?: {
                        page?: number | undefined;
                        size?: number | undefined;
                    } | undefined;
                    ids?: string[] | undefined;
                    query?: string | undefined;
                }>;
                output: zod.ZodArray<zod.ZodObject<{
                    id: zod.ZodString;
                    role: zod.ZodEnum<["admin", "moderator", "user"]>;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    image: zod.ZodNullable<zod.ZodString>;
                    createdAt: zod.ZodDate;
                    updatedAt: zod.ZodDate;
                    deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                }, "strip", zod.ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    role: "admin" | "moderator" | "user";
                    deletedAt?: Date | null | undefined;
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    role: "admin" | "moderator" | "user";
                    deletedAt?: Date | null | undefined;
                }>, "many">;
                cacheControl: string;
            };
        };
        me: {
            get: {
                input: null;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                    email: zod.ZodString;
                    role: zod.ZodEnum<["admin", "moderator", "user"]>;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    description: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    image: zod.ZodNullable<zod.ZodString>;
                    createdAt: zod.ZodDate;
                    updatedAt: zod.ZodDate;
                }, "strip", zod.ZodTypeAny, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    email: string;
                    role: "admin" | "moderator" | "user";
                }, {
                    description: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    image: string | null;
                    email: string;
                    role: "admin" | "moderator" | "user";
                }>;
                cacheControl: string;
            };
        };
        patch: {
            input: zod.ZodObject<{
                id: zod.ZodString;
                name: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">>;
                description: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                    locale: zod.ZodEnum<["en", "ru"]>;
                    value: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    value: string;
                    locale: "en" | "ru";
                }, {
                    value: string;
                    locale: "en" | "ru";
                }>, "many">>;
            }, "strip", zod.ZodTypeAny, {
                id: string;
                description?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
                name?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
            }, {
                id: string;
                description?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
                name?: {
                    value: string;
                    locale: "en" | "ru";
                }[] | undefined;
            }>;
            output: null;
            autoScopeInvalidationDepth: number;
        };
        title: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        userId: zod.ZodString;
                        ids: zod.ZodOptional<zod.ZodArray<zod.ZodString, "many">>;
                        isActive: zod.ZodOptional<zod.ZodBoolean>;
                    }, "strip", zod.ZodTypeAny, {
                        userId: string;
                        ids?: string[] | undefined;
                        isActive?: boolean | undefined;
                    }, {
                        userId: string;
                        ids?: string[] | undefined;
                        isActive?: boolean | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        userId: zod.ZodString;
                        name: zod.ZodArray<zod.ZodObject<{
                            locale: zod.ZodEnum<["en", "ru"]>;
                            value: zod.ZodString;
                        }, "strip", zod.ZodTypeAny, {
                            value: string;
                            locale: "en" | "ru";
                        }, {
                            value: string;
                            locale: "en" | "ru";
                        }>, "many">;
                        isActive: zod.ZodBoolean;
                        color: zod.ZodNullable<zod.ZodString>;
                        createdAt: zod.ZodDate;
                        updatedAt: zod.ZodDate;
                        deletedAt: zod.ZodOptional<zod.ZodNullable<zod.ZodDate>>;
                    }, "strip", zod.ZodTypeAny, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        userId: string;
                        isActive: boolean;
                        color: string | null;
                        deletedAt?: Date | null | undefined;
                    }, {
                        name: {
                            value: string;
                            locale: "en" | "ru";
                        }[];
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        userId: string;
                        isActive: boolean;
                        color: string | null;
                        deletedAt?: Date | null | undefined;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            post: {
                input: zod.ZodObject<{
                    userId: zod.ZodString;
                    name: zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">;
                    isActive: zod.ZodBoolean;
                    color: zod.ZodNullable<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    userId: string;
                    isActive: boolean;
                    color: string | null;
                }, {
                    name: {
                        value: string;
                        locale: "en" | "ru";
                    }[];
                    userId: string;
                    isActive: boolean;
                    color: string | null;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            patch: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                    name: zod.ZodOptional<zod.ZodArray<zod.ZodObject<{
                        locale: zod.ZodEnum<["en", "ru"]>;
                        value: zod.ZodString;
                    }, "strip", zod.ZodTypeAny, {
                        value: string;
                        locale: "en" | "ru";
                    }, {
                        value: string;
                        locale: "en" | "ru";
                    }>, "many">>;
                    isActive: zod.ZodOptional<zod.ZodBoolean>;
                    color: zod.ZodOptional<zod.ZodNullable<zod.ZodString>>;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    isActive?: boolean | undefined;
                    color?: string | null | undefined;
                }, {
                    id: string;
                    name?: {
                        value: string;
                        locale: "en" | "ru";
                    }[] | undefined;
                    isActive?: boolean | undefined;
                    color?: string | null | undefined;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        note: {
            get: {
                input: zod.ZodObject<{
                    userId: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    userId: string;
                }, {
                    userId: string;
                }>;
                output: zod.ZodObject<{
                    text: zod.ZodNullable<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    text: string | null;
                }, {
                    text: string | null;
                }>;
                cacheControl: string;
            };
            put: {
                input: zod.ZodObject<{
                    userId: zod.ZodString;
                    text: zod.ZodNullable<zod.ZodString>;
                }, "strip", zod.ZodTypeAny, {
                    userId: string;
                    text: string | null;
                }, {
                    userId: string;
                    text: string | null;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
        invite: {
            list: {
                get: {
                    input: zod.ZodObject<{
                        pagination: zod.ZodDefault<zod.ZodObject<{
                            page: zod.ZodDefault<zod.ZodNumber>;
                            size: zod.ZodDefault<zod.ZodNumber>;
                        }, "strip", zod.ZodTypeAny, {
                            page: number;
                            size: number;
                        }, {
                            page?: number | undefined;
                            size?: number | undefined;
                        }>>;
                    }, "strip", zod.ZodTypeAny, {
                        pagination: {
                            page: number;
                            size: number;
                        };
                    }, {
                        pagination?: {
                            page?: number | undefined;
                            size?: number | undefined;
                        } | undefined;
                    }>;
                    output: zod.ZodArray<zod.ZodObject<{
                        id: zod.ZodString;
                        email: zod.ZodString;
                        name: zod.ZodNullable<zod.ZodString>;
                        locale: zod.ZodEnum<["en", "ru"]>;
                        isUsed: zod.ZodBoolean;
                    }, "strip", zod.ZodTypeAny, {
                        name: string | null;
                        id: string;
                        locale: "en" | "ru";
                        email: string;
                        isUsed: boolean;
                    }, {
                        name: string | null;
                        id: string;
                        locale: "en" | "ru";
                        email: string;
                        isUsed: boolean;
                    }>, "many">;
                    cacheControl: string;
                };
            };
            put: {
                input: zod.ZodObject<{
                    email: zod.ZodString;
                    name: zod.ZodNullable<zod.ZodString>;
                    locale: zod.ZodEnum<["en", "ru"]>;
                }, "strip", zod.ZodTypeAny, {
                    name: string | null;
                    locale: "en" | "ru";
                    email: string;
                }, {
                    name: string | null;
                    locale: "en" | "ru";
                    email: string;
                }>;
                output: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                autoScopeInvalidationDepth: number;
            };
            delete: {
                input: zod.ZodObject<{
                    id: zod.ZodString;
                }, "strip", zod.ZodTypeAny, {
                    id: string;
                }, {
                    id: string;
                }>;
                output: null;
                autoScopeInvalidationDepth: number;
            };
        };
    };
};
declare const transformer: Transformer;

export { CACHE_CONTROL_HOUR, CACHE_CONTROL_IMMUTABLE, CACHE_CONTROL_TEN_MINUTES, DEFAULT_CACHE_CONTROL, schema, transformer };
