const index = 15;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CEW1-8cb.js')).default;
const imports = ["_app/immutable/nodes/15.D2qGS83K.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/q36Eg1F8.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=15-D5JOuB-k.js.map
