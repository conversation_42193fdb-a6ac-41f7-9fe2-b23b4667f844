import{e as Yu,c as Pu}from"../chunks/CVTn1FV4.js";import{g as Tu}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{p as Qu,av as A,aw as Cu,o as Ku,g as u,ax as i,f,h as Zu,b as _,c as Ju,u as J,t as p,s as o,d as a,$ as Ru,r as e,a as R,ay as Vu,az as Wu}from"../chunks/RHWQbow4.js";import{d as Xu,s as l}from"../chunks/BlWcudmi.js";import{i as w}from"../chunks/CtoItwj4.js";import{e as $u}from"../chunks/Dnfvvefi.js";import{s as ku,r as Bu}from"../chunks/BdpLTtcP.js";import{s as u4}from"../chunks/Cxg-bych.js";import{a as a4,d as Fu}from"../chunks/CBe4EX5h.js";import{b as e4}from"../chunks/B5DcI8qy.js";import"../chunks/BiLRrsV0.js";const t4=async({fetch:y,params:d,url:D})=>{const{fetcher:F}=Tu(),[I,[S],K]=await Promise.all([F.user.me.get({fetch:y,ctx:{url:D}}),F.user.list.get({ids:[d.id]},{fetch:y,ctx:{url:D}}),F.rating.karma.list.get({userId:d.id},{fetch:y,ctx:{url:D}})]);if(!S)throw Yu(404,"User not found");return{me:I,user:S,karmaPoints:K,isHasMoreKarma:K.length===Pu.PAGE_SIZE}},O4=Object.freeze(Object.defineProperty({__proto__:null,load:t4},Symbol.toStringTag,{value:"Module"})),r4=(y,d,D,F,I,S,K)=>{i(d,!0),i(D,1),i(F,[],!0),i(I,null),i(S,!1),i(K,!1)},s4=async(y,d,D,F,I,S,K,tu,V,n,U,q)=>{var H;if(u(d).length===0||!u(d).some(P=>P.value.trim())){i(D,"Comment is required");return}i(F,!0),i(D,null);try{await I.rating.karma.post({sourceUserId:u(S).id,targetUserId:u(K).id,quantity:u(tu),comment:u(d)}),i(V,!0),setTimeout(()=>{n()},1500)}catch(P){if(console.error(P),P.status===403&&((H=P.description)!=null&&H.includes("must_have_at_least_one_spendable_point"))){i(U,!0),i(D,null);return}i(D,P instanceof Error?P.message:u(q).errorSubmitting,!0)}finally{i(F,!1)}};var i4=f('<div class="alert alert-danger" role="alert"> </div>'),o4=f('<div><button class="btn btn-primary btn-sm"><i class="bi bi-arrow-up-down me-1"></i> </button></div>'),n4=f('<div class="text-center py-5"><p class="text-muted"> </p></div>'),l4=f('<img class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>'),v4=f('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white" style="width: 48px; height: 48px;"><i class="bi bi-person-fill"></i></div>'),c4=f('<div class="card mb-3 shadow-sm svelte-5uit7u"><div class="card-body"><div class="d-flex align-items-start"><div class="me-3"><!></div> <div class="flex-grow-1"><div class="d-flex justify-content-between align-items-start mb-2"><div><h6 class="mb-1"> </h6></div> <span> </span></div> <p class="mb-0 text-muted"> </p></div></div></div></div>'),d4=f('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),m4=f('<div class="text-center py-3"><!></div>'),_4=f('<div class="alert alert-danger" role="alert"> </div>'),b4=f('<div class="d-flex justify-content-between align-items-center mb-4"><div><h2 class="mb-1"> </h2> <p class="text-muted mb-0"> </p></div> <!></div> <!> <!> <!>',1),g4=(y,d)=>y.target===y.currentTarget&&d(),f4=(y,d)=>y.key==="Enter"&&y.target===y.currentTarget&&d(),p4=(y,d)=>i(d,!1),h4=f('<div class="text-center py-4"><div class="mb-3"><i class="bi bi-exclamation-triangle display-4 text-warning"></i></div> <h5 class="text-warning mb-3"> </h5> <p class="mb-3"> </p> <p class="text-muted mb-3"> </p> <div class="text-start mb-4"><h6 class="mb-2"> </h6></div> <button type="button" class="btn btn-outline-primary"><i class="bi bi-arrow-left me-1"></i> </button></div>'),x4=f('<div class="alert alert-danger" role="alert"> </div>'),E4=f('<div class="alert alert-success" role="alert"> </div>'),y4=f('<div class="mb-3"><div class="btn-group w-100" role="group"><input type="radio" class="btn-check" name="karmaType" id="giveKarma"/> <label class="btn btn-outline-success" for="giveKarma"><i class="bi bi-plus-lg me-1"></i> </label> <input type="radio" class="btn-check" name="karmaType" id="takeKarma"/> <label class="btn btn-outline-danger" for="takeKarma"><i class="bi bi-dash-lg me-1"></i> </label></div></div> <!> <!> <!>',1),C4=f('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),k4=f('<button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-primary"><!></button>',1),D4=f('<button type="button" class="btn btn-secondary"> </button>'),A4=f('<div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);" role="dialog" aria-modal="true"><div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!></div> <div class="modal-footer"><!></div></div></div></div>'),w4=f('<div class="container py-4"><div class="responsive-container"><!></div> <!></div>');function z4(y,d){Qu(d,!0);const D=[],F={en:{_page:{title:"Karma — Commune"},userNotFound:"User not found",karmaHistory:"Karma History",changeKarma:"Change Karma",karmaModalTitle:"Change Karma",giveKarma:"Give Karma (+1)",takeKarma:"Take Karma (-1)",comment:"Comment",commentPlaceholder:"Enter your comment...",cancel:"Cancel",submit:"Submit",submitting:"Submitting...",success:"Karma changed successfully",errorSubmitting:"Error submitting karma change",noKarmaChanges:"No karma changes found",loadingMore:"Loading more...",errorOccurred:"An error occurred",errorFetchingKarma:"Error fetching karma changes",insufficientPoints:{title:"Insufficient Karma Points",message:"You don't have enough spendable karma points to perform this action.",explanation:"You need at least one spendable karma point to give or take karma from other users.",howToEarn:"You get one karma point per week.",backToProfile:"Back to Profile"}},ru:{_page:{title:"Карма — Коммуна"},userNotFound:"Пользователь не найден",karmaHistory:"История кармы",changeKarma:"Изменить карму",karmaModalTitle:"Изменить карму",giveKarma:"Дать карму (+1)",takeKarma:"Забрать карму (-1)",comment:"Комментарий",commentPlaceholder:"Введите ваш комментарий...",cancel:"Отмена",submit:"Отправить",submitting:"Отправка...",success:"Карма успешно изменена",errorSubmitting:"Ошибка при изменении кармы",noKarmaChanges:"Изменения кармы не найдены",loadingMore:"Загрузка...",errorOccurred:"Произошла ошибка",errorFetchingKarma:"Ошибка загрузки изменений кармы",insufficientPoints:{title:"Недостаточно очков кармы",message:"У вас недостаточно доступных очков кармы для выполнения этого действия.",explanation:"Вам нужно как минимум одно доступное очко кармы, чтобы давать или забирать карму у других пользователей.",howToEarn:"Вы получаете одно очко кармы в неделю.",backToProfile:"Вернуться к профилю"}}},{fetcher:I}=Tu(),S=J(()=>d.data.me),K=J(()=>d.data.user),tu=J(()=>d.data.locale),V=J(()=>d.data.getAppropriateLocalization),n=J(()=>F[u(tu)]);let U=A(Cu(d.data.karmaPoints)),q=A(null),H=A(!1),P=A(1),ru=A(Cu(d.data.isHasMoreKarma)),su=A(null),iu=A(!1),G=A(1),N=A(Cu([])),ou=A(!1),W=A(null),nu=A(!1),Y=A(!1);async function Mu(){if(!(u(H)||!u(ru))){i(H,!0),i(q,null);try{const s=u(P)+1,g=await I.rating.karma.list.get({pagination:{page:s},userId:u(K).id});i(U,[...u(U),...g],!0),i(P,s),i(ru,g.length===Pu.PAGE_SIZE)}catch(s){i(q,s instanceof Error?s.message:u(n).errorOccurred,!0),console.error(s)}finally{i(H,!1)}}}Ku(()=>{if(!u(su))return;const s=new IntersectionObserver(g=>{g[0].isIntersecting&&u(ru)&&!u(H)&&Mu()},{threshold:.1});return s.observe(u(su)),()=>{s.disconnect()}}),Ku(()=>{if(!u(iu))return;const s=g=>{g.key==="Escape"&&Q()};return document.addEventListener("keydown",s),()=>{document.removeEventListener("keydown",s)}});const Du=J(()=>u(V)(u(K).name)),Q=()=>{i(iu,!1),i(N,[],!0),i(W,null),i(nu,!1),i(Y,!1)},Au=s=>u(V)(s.name),Su=s=>s>0?`+${s}`:`${s}`,Iu=s=>s>0?"text-success":"text-danger";function Hu(){window.location.reload()}var _u=w4();Zu(s=>{p(()=>Ru.title=`${u(Du)??""} ${u(n)._page.title??""}`)});var bu=a(_u),Lu=a(bu);{var ju=s=>{var g=i4(),L=a(g,!0);e(g),p(()=>l(L,u(n).userNotFound)),_(s,g)},Nu=s=>{var g=b4(),L=R(g),Z=a(L),O=a(Z),X=a(O,!0);e(O);var lu=o(O,2),gu=a(lu,!0);e(lu),e(Z);var $=o(Z,2);{var fu=r=>{var t=o4(),b=a(t);b.__click=[r4,iu,G,N,W,nu,Y];var v=o(a(b));e(b),e(t),p(()=>{ku(b,"aria-label",u(n).changeKarma),l(v,` ${u(n).changeKarma??""}`)}),_(r,t)};w($,r=>{u(S).id!==u(K).id&&r(fu)})}e(L);var vu=o(L,2);{var pu=r=>{var t=n4(),b=a(t),v=a(b,!0);e(b),e(t),p(()=>l(v,u(n).noKarmaChanges)),_(r,t)},cu=r=>{var t=Vu(),b=R(t);$u(b,17,()=>u(U),v=>v.id,(v,m)=>{var k=c4(),x=a(k),c=a(x),E=a(c),B=a(E);{var T=j=>{var z=l4();p(yu=>{ku(z,"src",`/images/${u(m).author.image}`),ku(z,"alt",yu)},[()=>Au(u(m).author)]),_(j,z)},uu=j=>{var z=v4();_(j,z)};w(B,j=>{u(m).author.image?j(T):j(uu,!1)})}e(E);var mu=o(E,2),au=a(mu),h=a(au),M=a(h),eu=a(M,!0);e(M),e(h);var Eu=o(h,2),Uu=a(Eu,!0);e(Eu),e(au);var wu=o(au,2),qu=a(wu,!0);e(wu),e(mu),e(c),e(x),e(k),p((j,z,yu,Gu)=>{l(eu,j),u4(Eu,1,`badge ${z??""} fs-6`,"svelte-5uit7u"),l(Uu,yu),l(qu,Gu)},[()=>Au(u(m).author),()=>Iu(u(m).quantity),()=>Su(u(m).quantity),()=>u(V)(u(m).comment)]),_(v,k)}),_(r,t)};w(vu,r=>{u(U).length===0?r(pu):r(cu,!1)})}var du=o(vu,2);{var hu=r=>{var t=m4(),b=a(t);{var v=m=>{var k=d4(),x=R(k),c=a(x),E=a(c,!0);e(c),e(x);var B=o(x,2),T=a(B,!0);e(B),p(()=>{l(E,u(n).loadingMore),l(T,u(n).loadingMore)}),_(m,k)};w(b,m=>{u(H)&&m(v)})}e(t),e4(t,m=>i(su,m),()=>u(su)),_(r,t)};w(du,r=>{u(ru)&&r(hu)})}var xu=o(du,2);{var C=r=>{var t=_4(),b=a(t,!0);e(t),p(()=>l(b,u(q))),_(r,t)};w(xu,r=>{u(q)&&r(C)})}p(()=>{l(X,u(Du)),l(gu,u(n).karmaHistory)}),_(s,g)};w(Lu,s=>{u(K)?s(Nu,!1):s(ju)})}e(bu);var Ou=o(bu,2);{var zu=s=>{var g=A4();g.__click=[g4,Q],g.__keydown=[f4,Q];var L=a(g),Z=a(L),O=a(Z),X=a(O),lu=a(X,!0);e(X);var gu=o(X,2);gu.__click=Q,e(O);var $=o(O,2),fu=a($);{var vu=C=>{var r=h4(),t=o(a(r),2),b=a(t,!0);e(t);var v=o(t,2),m=a(v,!0);e(v);var k=o(v,2),x=a(k,!0);e(k);var c=o(k,2),E=a(c),B=a(E,!0);e(E),e(c);var T=o(c,2);T.__click=[p4,Y];var uu=o(a(T));e(T),e(r),p(()=>{l(b,u(n).insufficientPoints.title),l(m,u(n).insufficientPoints.message),l(x,u(n).insufficientPoints.explanation),l(B,u(n).insufficientPoints.howToEarn),l(uu,` ${u(n).insufficientPoints.backToProfile??""}`)}),_(C,r)},pu=C=>{var r=y4(),t=R(r),b=a(t),v=a(b);Bu(v),v.value=v.__value=1;var m=o(v,2),k=o(a(m));e(m);var x=o(m,2);Bu(x),x.value=x.__value=-1;var c=o(x,2),E=o(a(c));e(c),e(b),e(t);var B=o(t,2);a4(B,{get locale(){return u(tu)},id:"karmaComment",get label(){return u(n).comment},get placeholder(){return u(n).commentPlaceholder},rows:3,required:!0,get value(){return u(N)},set value(h){i(N,h,!0)}});var T=o(B,2);{var uu=h=>{var M=x4(),eu=a(M,!0);e(M),p(()=>l(eu,u(W))),_(h,M)};w(T,h=>{u(W)&&h(uu)})}var mu=o(T,2);{var au=h=>{var M=E4(),eu=a(M,!0);e(M),p(()=>l(eu,u(n).success)),_(h,M)};w(mu,h=>{u(nu)&&h(au)})}p(()=>{l(k,` ${u(n).giveKarma??""}`),l(E,` ${u(n).takeKarma??""}`)}),Fu(D,[],v,()=>u(G),h=>i(G,h)),Fu(D,[],x,()=>u(G),h=>i(G,h)),_(C,r)};w(fu,C=>{u(Y)?C(vu):C(pu,!1)})}e($);var cu=o($,2),du=a(cu);{var hu=C=>{var r=k4(),t=R(r);t.__click=Q;var b=a(t,!0);e(t);var v=o(t,2);v.__click=[s4,N,W,ou,I,S,K,G,nu,Hu,Y,n];var m=a(v);{var k=c=>{var E=C4(),B=o(R(E));p(()=>l(B,` ${u(n).submitting??""}`)),_(c,E)},x=c=>{var E=Wu();p(()=>l(E,u(n).submit)),_(c,E)};w(m,c=>{u(ou)?c(k):c(x,!1)})}e(v),p(c=>{t.disabled=u(ou),l(b,u(n).cancel),v.disabled=c},[()=>u(ou)||u(N).length===0||!u(N).some(c=>c.value.trim())]),_(C,r)},xu=C=>{var r=D4();r.__click=Q;var t=a(r,!0);e(r),p(()=>l(t,u(n).cancel)),_(C,r)};w(du,C=>{u(Y)?C(xu,!1):C(hu)})}e(cu),e(Z),e(L),e(g),p(()=>l(lu,u(n).karmaModalTitle)),_(s,g)};w(Ou,s=>{u(iu)&&s(zu)})}e(_u),_(y,_u),Ju()}Xu(["click","keydown"]);export{z4 as component,O4 as universal};
