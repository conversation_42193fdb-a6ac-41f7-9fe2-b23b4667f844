import{e as Je,c as Pe}from"../chunks/CVTn1FV4.js";import{g as Le}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{o as Ke}from"../chunks/DeAm3Eed.js";import{p as Qe,av as R,aw as he,f as l,h as Ve,t as _,b as v,c as Xe,s as c,d as u,g as e,$ as $e,u as re,r as t,ax as C,a as ne,az as je}from"../chunks/RHWQbow4.js";import{d as et,s as d}from"../chunks/BlWcudmi.js";import{i as D}from"../chunks/CtoItwj4.js";import{e as ke}from"../chunks/Dnfvvefi.js";import{s as xe}from"../chunks/BdpLTtcP.js";import{s as Oe}from"../chunks/Cxg-bych.js";import{b as tt}from"../chunks/B5DcI8qy.js";import"../chunks/DiZKRWcx.js";import{f as Se}from"../chunks/CL12WlkV.js";const ut=async({fetch:B,params:m,url:y})=>{const{fetcher:F}=Le(),[I,[L]]=await Promise.all([F.user.me.get({fetch:B,ctx:{url:y}}),F.commune.list.get({ids:[m.id]},{fetch:B,ctx:{url:y}})]);if(!L)throw Je(404,"Commune not found");const M=(I==null?void 0:I.role)==="admin",s=I&&L.headMember.actorType==="user"&&L.headMember.actorId===I.id;if(!M&&!s)throw new Error("Access denied: You must be an admin or commune head to view invitations");const b=await F.commune.invitation.list.get({communeId:m.id},{fetch:B,ctx:{url:y}}),A=b.length?await F.user.list.get({ids:b.map(({userId:h})=>h)},{fetch:B,ctx:{url:y}}):[],j=new Map(A.map(h=>[h.id,h])),X=b.map(h=>({...h,user:j.get(h.userId)}));return{commune:L,invitations:X,isHasMoreInvitations:b.length===Pe.PAGE_SIZE,userPermissions:{isAdmin:M,isHeadMember:s,canManageInvitations:M||s}}},Pt=Object.freeze(Object.defineProperty({__proto__:null,load:ut},Symbol.toStringTag,{value:"Module"}));var at=l('<div class="text-center py-5"><p class="text-muted"> </p></div>'),rt=l('<img alt="User avatar" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;"/>'),nt=l('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;"><i class="bi bi-person text-white"></i></div>'),it=(B,m,y)=>m(e(y).id),st=l('<span class="spinner-border spinner-border-sm me-1" role="status"></span> ',1),ot=l('<button class="btn btn-sm btn-outline-danger"><!></button>'),ct=l('<span class="text-muted">—</span>'),dt=l('<tr><td><div class="d-flex align-items-center"><!> <div><div class="fw-medium"> </div></div></div></td><td><span> </span></td><td class="text-muted"> </td><td><!></td></tr>'),vt=l('<img alt="User avatar" class="rounded-circle me-3" style="width: 48px; height: 48px; object-fit: cover;"/>'),lt=l('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;"><i class="bi bi-person text-white"></i></div>'),mt=(B,m,y)=>m(e(y).id),_t=l('<span class="spinner-border spinner-border-sm me-1" role="status"></span> ',1),gt=l('<button class="btn btn-sm btn-outline-danger"><!></button>'),pt=l('<div class="col-12"><div class="card"><div class="card-body"><div class="d-flex align-items-start justify-content-between mb-3"><div class="d-flex align-items-center"><!> <div><div class="fw-medium"> </div></div></div> <span> </span></div> <div class="d-flex justify-content-between align-items-center"><small class="text-muted"> </small> <!></div></div></div></div>'),ft=l('<div class="d-none d-md-block"><div class="table-responsive"><table class="table table-hover"><thead><tr><th> </th><th> </th><th> </th><th> </th></tr></thead><tbody></tbody></table></div></div> <div class="d-md-none"><div class="row g-3"></div></div>',1),bt=l('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),ht=l('<div class="text-center py-3"><!></div>'),xt=l('<div class="alert alert-danger" role="alert"> </div>'),Ct=l('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><div><h1> </h1> <p class="text-muted mb-0"> </p></div> <a class="btn btn-outline-secondary"> </a></div> <!> <!> <!></div>');function Lt(B,m){Qe(m,!0);const y={en:{_page:{title:"Commune Invitations — Commune"},communeInvitations:"Commune Invitations",loading:"Loading...",noInvitations:"No invitations found",errorFetchingInvitations:"Failed to fetch invitations",errorOccurred:"An error occurred while fetching invitations",loadingMore:"Loading more invitations...",cancel:"Cancel Invitation",pending:"Pending",accepted:"Accepted",rejected:"Rejected",expired:"Expired",sentOn:"Sent on",cancelingInvitation:"Canceling...",errorCancelingInvitation:"Failed to cancel invitation",invitationCanceled:"Invitation canceled",backToCommune:"Back to Commune",invitedUser:"Invited User",status:"Status",actions:"Actions",confirmCancel:"Are you sure you want to cancel this invitation?"},ru:{_page:{title:"Приглашения коммуны — Коммуна"},communeInvitations:"Приглашения коммуны",loading:"Загрузка...",noInvitations:"Приглашения не найдены",errorFetchingInvitations:"Не удалось загрузить приглашения",errorOccurred:"Произошла ошибка при загрузке приглашений",loadingMore:"Загружаем больше приглашений...",cancel:"Отменить приглашение",pending:"Ожидает",accepted:"Принято",rejected:"Отклонено",expired:"Истекло",sentOn:"Отправлено",cancelingInvitation:"Отменяем...",errorCancelingInvitation:"Не удалось отменить приглашение",invitationCanceled:"Приглашение отменено",backToCommune:"Назад к коммуне",invitedUser:"Приглашенный пользователь",status:"Статус",actions:"Действия",confirmCancel:"Вы уверены, что хотите отменить это приглашение?"}},{fetcher:F}=Le(),I=re(()=>m.data.locale),L=re(()=>m.data.toLocaleHref),M=re(()=>m.data.getAppropriateLocalization),s=re(()=>y[e(I)]);let b=R(he(m.data.invitations)),A=R(null),j=R(!1),X=R(1),h=R(he(m.data.isHasMoreInvitations)),Y=R(null),T=he({});async function Te(){if(!(e(j)||!e(h))){C(j,!0),C(A,null);try{const a=e(X)+1,r=await F.commune.invitation.list.get({communeId:m.data.commune.id,pagination:{page:a}}),g=r.length?await F.user.list.get({ids:r.map(p=>p.userId)}):[],w=new Map(g.map(p=>[p.id,p])),E=r.map(p=>({...p,user:w.get(p.userId)}));C(b,[...e(b),...E],!0),C(X,a),C(h,r.length===Pe.PAGE_SIZE)}catch(a){C(A,a instanceof Error?a.message:e(s).errorOccurred,!0),console.error(a)}finally{C(j,!1)}}}async function Ce(a){if(confirm(e(s).confirmCancel)){T[a]="canceling",C(A,null);try{await F.commune.invitation.delete({id:a}),C(b,e(b).filter(r=>r.id!==a),!0)}catch(r){C(A,r instanceof Error?r.message:e(s).errorCancelingInvitation,!0),console.error(r)}finally{T[a]=null}}}Ke(()=>{let a;const r=()=>{e(Y)&&(a=new IntersectionObserver(g=>{g[0].isIntersecting&&e(h)&&!e(j)&&Te()},{rootMargin:"100px",threshold:.1}),a.observe(e(Y)))};return e(Y)?r():setTimeout(r,100),()=>{a&&a.disconnect()}});function ye(a){switch(a){case"pending":return"bg-warning text-dark";case"accepted":return"bg-success";case"rejected":return"bg-danger";case"expired":return"bg-secondary";default:return"bg-secondary"}}function Ee(a){switch(a){case"pending":return e(s).pending;case"accepted":return e(s).accepted;case"rejected":return e(s).rejected;case"expired":return e(s).expired;default:return a}}var ie=Ct();Ve(a=>{_(()=>$e.title=e(s)._page.title)});var se=u(ie),oe=u(se),ce=u(oe),Ue=u(ce,!0);t(ce);var De=c(ce,2),He=u(De,!0);t(De),t(oe);var de=c(oe,2),ze=u(de,!0);t(de),t(se);var Ie=c(se,2);{var Ge=a=>{var r=at(),g=u(r),w=u(g,!0);t(g),t(r),_(()=>d(w,e(s).noInvitations)),v(a,r)},We=a=>{var r=ft(),g=ne(r),w=u(g),E=u(w),p=u(E),U=u(p),H=u(U),ve=u(H,!0);t(H);var z=c(H),le=u(z,!0);t(z);var me=c(z),Ye=u(me,!0);t(me);var Be=c(me),qe=u(Be,!0);t(Be),t(U),t(p);var Fe=c(p);ke(Fe,21,()=>e(b),k=>k.id,(k,o)=>{var O=dt(),G=u(O),q=u(G),W=u(q);{var J=n=>{var i=rt();_(()=>xe(i,"src",`/images/${e(o).user.image}`)),v(n,i)},$=n=>{var i=nt();v(n,i)};D(W,n=>{e(o).user.image?n(J):n($,!1)})}var ee=c(W,2),te=u(ee),ue=u(te,!0);t(te),t(ee),t(q),t(G);var Z=c(G),K=u(Z),Q=u(K,!0);t(K),t(Z);var V=c(Z),ae=u(V,!0);t(V);var N=c(V),_e=u(N);{var ge=n=>{var i=ot();i.__click=[it,Ce,o];var S=u(i);{var P=f=>{var x=st(),be=c(ne(x));_(()=>d(be,` ${e(s).cancelingInvitation??""}`)),v(f,x)},fe=f=>{var x=je();_(()=>d(x,e(s).cancel)),v(f,x)};D(S,f=>{T[e(o).id]==="canceling"?f(P):f(fe,!1)})}t(i),_(()=>i.disabled=T[e(o).id]==="canceling"),v(n,i)},pe=n=>{var i=ct();v(n,i)};D(_e,n=>{e(o).status==="pending"?n(ge):n(pe,!1)})}t(N),t(O),_((n,i,S,P)=>{d(ue,n),Oe(K,1,i),d(Q,S),d(ae,P)},[()=>e(M)(e(o).user.name),()=>`badge ${ye(e(o).status)}`,()=>Ee(e(o).status),()=>Se(e(o).createdAt,e(I))]),v(k,O)}),t(Fe),t(E),t(w),t(g);var Ae=c(g,2),Me=u(Ae);ke(Me,21,()=>e(b),k=>k.id,(k,o)=>{var O=pt(),G=u(O),q=u(G),W=u(q),J=u(W),$=u(J);{var ee=n=>{var i=vt();_(()=>xe(i,"src",`/images/${e(o).user.image}`)),v(n,i)},te=n=>{var i=lt();v(n,i)};D($,n=>{e(o).user.image?n(ee):n(te,!1)})}var ue=c($,2),Z=u(ue),K=u(Z,!0);t(Z),t(ue),t(J);var Q=c(J,2),V=u(Q,!0);t(Q),t(W);var ae=c(W,2),N=u(ae),_e=u(N);t(N);var ge=c(N,2);{var pe=n=>{var i=gt();i.__click=[mt,Ce,o];var S=u(i);{var P=f=>{var x=_t(),be=c(ne(x));_(()=>d(be,` ${e(s).cancelingInvitation??""}`)),v(f,x)},fe=f=>{var x=je();_(()=>d(x,e(s).cancel)),v(f,x)};D(S,f=>{T[e(o).id]==="canceling"?f(P):f(fe,!1)})}t(i),_(()=>i.disabled=T[e(o).id]==="canceling"),v(n,i)};D(ge,n=>{e(o).status==="pending"&&n(pe)})}t(ae),t(q),t(G),t(O),_((n,i,S,P)=>{d(K,n),Oe(Q,1,i),d(V,S),d(_e,`${e(s).sentOn??""}
                    ${P??""}`)},[()=>e(M)(e(o).user.name),()=>`badge ${ye(e(o).status)}`,()=>Ee(e(o).status),()=>Se(e(o).createdAt,e(I))]),v(k,O)}),t(Me),t(Ae),_(()=>{d(ve,e(s).invitedUser),d(le,e(s).status),d(Ye,e(s).sentOn),d(qe,e(s).actions)}),v(a,r)};D(Ie,a=>{e(b).length===0?a(Ge):a(We,!1)})}var we=c(Ie,2);{var Ze=a=>{var r=ht(),g=u(r);{var w=E=>{var p=bt(),U=ne(p),H=u(U),ve=u(H,!0);t(H),t(U);var z=c(U,2),le=u(z,!0);t(z),_(()=>{d(ve,e(s).loadingMore),d(le,e(s).loadingMore)}),v(E,p)};D(g,E=>{e(j)&&E(w)})}t(r),tt(r,E=>C(Y,E),()=>e(Y)),v(a,r)};D(we,a=>{e(h)&&a(Ze)})}var Ne=c(we,2);{var Re=a=>{var r=xt(),g=u(r,!0);t(r),_(()=>d(g,e(A))),v(a,r)};D(Ne,a=>{e(A)&&a(Re)})}t(ie),_((a,r)=>{d(Ue,e(s).communeInvitations),d(He,a),xe(de,"href",r),d(ze,e(s).backToCommune)},[()=>e(M)(m.data.commune.name),()=>e(L)(`/communes/${m.data.commune.id}`)]),v(B,ie),Xe()}et(["click"]);export{Lt as component,Pt as universal};
