import"./Bzak7iHL.js";import{p as qt,av as H,aw as wt,f as n,d as u,s as _,t as b,b as l,c as At,g as i,u as k,ax as D,r as s,ay as Pt,a as Dt}from"./RHWQbow4.js";import{d as Et,e as xt,s as A}from"./BlWcudmi.js";import{i as w}from"./CtoItwj4.js";import{e as yt,i as kt}from"./Dnfvvefi.js";import{h as $t}from"./C_sRNQCS.js";import{s as v}from"./BdpLTtcP.js";import{s as z}from"./Cxg-bych.js";import{s as pt}from"./CaC9IHEK.js";import{g as te}from"./CSZ3sDel.js";import{p as ee,r as ae}from"./CR3e0W7L.js";function ie(h,e,m){if(!h.ctrlKey&&!h.shiftKey&&!h.altKey&&!h.metaKey){h.preventDefault();const p=`${window.location.origin}${e.toLocaleHref(`/reactor/${e.post.id}`)}`;navigator.clipboard.writeText(p),D(m,!0),setTimeout(()=>D(m,!1),2e3)}}async function se(h,e,m,p){D(e,await m.reactor.post.rating.post({id:p.post.id,type:"like"}),!0)}async function ue(h,e,m,p){D(e,await m.reactor.post.rating.post({id:p.post.id,type:"dislike"}),!0)}var oe=n('<img class="hub-image svelte-1oqwi5d"/>'),re=n('<div class="hub-image-placeholder svelte-1oqwi5d"><i class="bi bi-collection text-muted svelte-1oqwi5d"></i></div>'),le=n('<div class="hub-row d-flex align-items-center mb-2"><div class="hub-image-container me-2 svelte-1oqwi5d"><!></div> <a class="hub-link text-decoration-none fw-medium svelte-1oqwi5d"> </a></div>'),ne=n('<img class="community-image svelte-1oqwi5d"/>'),de=n('<div class="community-image-placeholder svelte-1oqwi5d"><i class="bi bi-people text-muted svelte-1oqwi5d"></i></div>'),ce=n('<div class="community-row d-flex align-items-center"><div class="community-image-container me-2 svelte-1oqwi5d"><!></div> <a class="community-link text-decoration-none fw-medium svelte-1oqwi5d"> </a></div>'),ve=n('<div class="hub-community-header mb-3 svelte-1oqwi5d"><!> <!></div>'),me=n('<span class="rating-value me-2 text-success svelte-1oqwi5d" data-bs-toggle="tooltip" data-bs-placement="top"> </span>'),fe=n('<span class="rating-value me-2 text-danger svelte-1oqwi5d" data-bs-toggle="tooltip" data-bs-placement="top"> </span>'),_e=n('<span class="rating-value me-2 svelte-1oqwi5d" data-bs-toggle="tooltip" data-bs-placement="top">0</span>'),be=n('<img class="avatar rounded-circle me-2" width="32" height="32"/>'),ge=n('<div class="avatar rounded-circle me-2"></div>'),he=n('<span class="usefulness-label mb-1 text-muted small px-1"> </span>'),we=n('<span class="usefulness-label mb-1 text-muted small px-1"> </span>'),xe=n('<button class="btn btn-sm p-0 px-1"><i></i></button>'),ye=(h,e)=>{var m;return(m=e.onEditPost)==null?void 0:m.call(e,e.post)},ke=n('<button type="button" class="btn btn-outline-secondary btn-sm ms-2"><i class="bi bi-pencil"></i></button>'),pe=n("<button> </button>"),qe=n('<i class="bi bi-check-circle svelte-1oqwi5d"></i>'),Ae=n('<i class="bi bi-link-45deg svelte-1oqwi5d"></i>'),Pe=n('<div class="post-card mb-4 svelte-1oqwi5d"><div class="card"><div class="card-header"><!> <div class="post-header d-flex justify-content-between align-items-center mb-3"><div class="d-flex align-items-center"><div class="rating-block d-flex align-items-center me-3"><!> <div class="rating-buttons"><button aria-label="Like"><i class="bi bi-hand-thumbs-up svelte-1oqwi5d"></i></button> <button aria-label="Dislike"><i class="bi bi-hand-thumbs-down svelte-1oqwi5d"></i></button></div></div> <div class="author-info d-flex align-items-center"><!> <div><a class="author-name fw-bold"> </a> <div class="post-time small text-muted"> </div></div></div></div> <div class="usefulness-block"><div class="d-flex flex-column align-items-start"><!> <div role="group" aria-label="Usefulness rating"></div></div></div></div></div> <div class="card-body"><div class="d-flex justify-content-between align-items-start mb-2"><a class="post-title-link-wrapper flex-grow-1 svelte-1oqwi5d"><h5 class="card-title mb-0 svelte-1oqwi5d"> </h5></a> <!></div> <div class="card-text"><!></div> <div class="tags mb-3 svelte-1oqwi5d"></div> <div class="card-actions d-flex"><a target="_blank"><button aria-label="Copy link"><!></button></a></div></div></div></div>');function Ke(h,e){qt(e,!0);const m={en:{usefulness:"Usefulness",editPost:"Edit Post",getPlural(t){return t===1?0:1},ratingTooltipText(t){const a=["like","likes"][this.getPlural(t.likes%10)],o=["dislike","dislikes"][this.getPlural(t.dislikes%10)];return`${t.likes} ${a}, ${t.dislikes} ${o}`},time:{days(t){return`${t} ${t===1?"day":"days"} ago`},hours(t){return`${t} ${t===1?"hour":"hours"} ago`},minutes(t){return`${t} ${t===1?"minute":"minutes"} ago`},seconds(t){return`${t} ${t===1?"second":"seconds"} ago`},rightNow:"right now"}},ru:{usefulness:"Полезность",editPost:"Редактировать пост",getPlural(t){return t===1?0:t>=2&&t<=4?1:2},ratingTooltipText(t){const a=["лайк","лайка","лайков"][this.getPlural(t.likes%10)],o=["дизлайк","дизлайка","дизлайков"][this.getPlural(t.dislikes%10)];return`${t.likes} ${a}, ${t.dislikes} ${o}`},time:{days(t){const a=["день","дня","дней"][m.ru.getPlural(t)];return`${t} ${a} назад`},hours(t){const a=["час","часа","часов"][m.ru.getPlural(t)];return`${t} ${a} назад`},minutes(t){const a=["минуту","минуты","минут"][m.ru.getPlural(t)];return`${t} ${a} назад`},seconds(t){const a=["секунду","секунды","секунд"][m.ru.getPlural(t)];return`${t} ${a} назад`},rightNow:"только что"}}},{fetcher:p}=te(),x=k(()=>m[e.locale]);let q=H(wt(e.post.rating)),E=H(wt(e.post.usefulness));const B=k(()=>i(q).likes-i(q).dislikes),Tt=k(()=>i(E).totalValue??0),S=k(()=>i(x).ratingTooltipText(i(q)));let M=H(null);const Lt=k(()=>e.getAppropriateLocalization(e.post.author.name)),Bt=k(()=>e.getAppropriateLocalization(e.post.title)),Ct=k(()=>e.getAppropriateLocalization(e.post.body)),at=k(()=>e.post.hub?e.getAppropriateLocalization(e.post.hub.name):null),it=k(()=>e.post.community?e.getAppropriateLocalization(e.post.community.name):null);let W=H(!1),N=H(null);const Ht=k(()=>e.currentUser&&(e.currentUser.role==="admin"||e.currentUser.id===e.post.author.id));function zt(t){const o=new Date().getTime()-t.getTime(),c=Math.floor(o/1e3),g=Math.floor(c/60),d=Math.floor(g/60),r=Math.floor(d/24);return r>0?i(x).time.days(r):d>0?i(x).time.hours(d):g>0?i(x).time.minutes(g):c>3?i(x).time.seconds(c):i(x).time.rightNow}async function Mt(t){var a;D(E,await p.reactor.post.usefulness.post({id:e.post.id,value:t===((a=i(E))==null?void 0:a.value)?null:t}),!0)}function Nt(t){navigator.clipboard.writeText(t),D(N,t,!0),setTimeout(()=>{i(N)===t&&D(N,null)},2e3)}var j=Pe(),st=u(j),F=u(st),ut=u(F);{var Ut=t=>{var a=ve(),o=u(a);{var c=r=>{var P=le(),T=u(P),Z=u(T);{var $=f=>{var y=oe();b(()=>{v(y,"src",`/images/${e.post.hub.image}`),v(y,"alt",i(at)||"Hub")}),l(f,y)},tt=f=>{var y=re();l(f,y)};w(Z,f=>{e.post.hub.image?f($):f(tt,!1)})}s(T);var L=_(T,2),et=u(L,!0);s(L),s(P),b(f=>{v(L,"href",f),A(et,i(at))},[()=>e.toLocaleHref(`/reactor/hubs/${e.post.hub.id}`)]),l(r,P)};w(o,r=>{e.post.hub&&r(c)})}var g=_(o,2);{var d=r=>{var P=ce(),T=u(P),Z=u(T);{var $=f=>{var y=ne();b(()=>{v(y,"src",`/images/${e.post.community.image}`),v(y,"alt",i(it)||"Community")}),l(f,y)},tt=f=>{var y=de();l(f,y)};w(Z,f=>{e.post.community.image?f($):f(tt,!1)})}s(T);var L=_(T,2),et=u(L,!0);s(L),s(P),b(f=>{v(L,"href",f),A(et,i(it))},[()=>e.toLocaleHref(`/reactor/communities/${e.post.community.id}`)]),l(r,P)};w(g,r=>{e.post.community&&r(d)})}s(a),l(t,a)};w(ut,t=>{(e.post.hub||e.post.community)&&t(Ut)})}var ot=_(ut,2),R=u(ot),V=u(R),rt=u(V);{var Kt=t=>{var a=me(),o=u(a,!0);s(a),b(()=>{v(a,"title",i(S)),A(o,i(B))}),l(t,a)},St=t=>{var a=Pt(),o=Dt(a);{var c=d=>{var r=fe(),P=u(r,!0);s(r),b(()=>{v(r,"title",i(S)),A(P,i(B))}),l(d,r)},g=d=>{var r=_e();b(()=>v(r,"title",i(S))),l(d,r)};w(o,d=>{i(B)<0?d(c):d(g,!1)},!0)}l(t,a)};w(rt,t=>{i(B)>0?t(Kt):t(St,!1)})}var lt=_(rt,2),I=u(lt);I.__click=[se,q,p,e];var nt=_(I,2);nt.__click=[ue,q,p,e],s(lt),s(V);var dt=_(V,2),ct=u(dt);{var Wt=t=>{var a=be();v(a,"alt","avatar"),pt(a,"",{},{"object-fit":"cover"}),b(()=>v(a,"src",`/images/${e.post.author.image}`)),l(t,a)},jt=t=>{var a=ge();l(t,a)};w(ct,t=>{e.post.author.image?t(Wt):t(jt,!1)})}var vt=_(ct,2),C=u(vt);pt(C,"",{},{"text-decoration":"none"});var Ft=u(C,!0);s(C);var O=_(C,2),Rt=u(O,!0);s(O),s(vt),s(dt),s(R);var mt=_(R,2),ft=u(mt),_t=u(ft);{var Vt=t=>{var a=he(),o=u(a);s(a),b(()=>A(o,`${i(x).usefulness??""} (${i(E).count??""})`)),l(t,a)},It=t=>{var a=we(),o=u(a,!0);s(a),b(()=>A(o,i(x).usefulness)),l(t,a)};w(_t,t=>{i(E)&&i(E).count>0?t(Vt):t(It,!1)})}var G=_(_t,2);yt(G,20,()=>Array(5),kt,(t,a,o)=>{var c=xe();v(c,"aria-label",`Rate usefulness ${o+1}`),c.__click=()=>Mt((o+1)*2);var g=u(c);s(c),b(()=>z(g,1,`bi bi-star${(i(M)!==null?o<=i(M):(o+1)*2<=i(Tt))?"-fill":""} text-warning rating-star`)),xt("mouseenter",c,()=>D(M,o,!0)),l(t,c)}),s(G),s(ft),s(mt),s(ot),s(F);var bt=_(F,2),J=u(bt),U=u(J),gt=u(U),Ot=u(gt,!0);s(gt),s(U);var Gt=_(U,2);{var Jt=t=>{var a=ke();a.__click=[ye,e],b(()=>{v(a,"title",i(x).editPost),v(a,"aria-label",i(x).editPost)}),l(t,a)};w(Gt,t=>{i(Ht)&&e.onEditPost&&t(Jt)})}s(J);var Q=_(J,2),Qt=u(Q);$t(Qt,()=>i(Ct)),s(Q);var X=_(Q,2);yt(X,21,()=>[...e.post.tags],kt,(t,a)=>{var o=pe(),c=k(()=>Nt.bind(null,i(a).id));o.__click=function(...d){var r;(r=i(c))==null||r.apply(this,d)};var g=u(o,!0);s(o),b(d=>{z(o,1,`badge me-1 ${i(N)===i(a).id?"bg-success text-white":"bg-light text-secondary"}`,"svelte-1oqwi5d"),A(g,d)},[()=>e.getAppropriateLocalization(i(a).name)]),l(t,o)}),s(X);var ht=_(X,2),Y=u(ht),K=u(Y);K.__click=[ie,e,W];var Xt=u(K);{var Yt=t=>{var a=qe();l(t,a)},Zt=t=>{var a=Ae();l(t,a)};w(Xt,t=>{i(W)?t(Yt):t(Zt,!1)})}s(K),s(Y),s(ht),s(bt),s(st),s(j),b((t,a,o,c,g)=>{var d,r;z(I,1,`btn btn-sm me-1 ${((d=i(q))==null?void 0:d.status)==="like"?"btn-success":"btn-outline-success"}`,"svelte-1oqwi5d"),z(nt,1,`btn btn-sm ${((r=i(q))==null?void 0:r.status)==="dislike"?"btn-danger":"btn-outline-danger"}`,"svelte-1oqwi5d"),v(C,"href",t),A(Ft,i(Lt)??"Anonymous"),v(O,"title",a),A(Rt,o),v(U,"href",c),A(Ot,i(Bt)),v(Y,"href",g),z(K,1,`btn btn-sm ${i(W)?"btn-success":"btn-outline-secondary"}`,"svelte-1oqwi5d")},[()=>e.toLocaleHref(`/users/${e.post.author.id}`),()=>e.post.createdAt.toISOString(),()=>zt(e.post.createdAt),()=>e.toLocaleHref(`/reactor/${e.post.id}`),()=>e.toLocaleHref(`/reactor/${e.post.id}`)]),xt("mouseleave",G,()=>D(M,null)),l(h,j),At()}Et(["click"]);function Se(h,e){qt(e,!0),ee(e,"isExpanded",15,!1);let m=ae(e,["$$slots","$$events","$$legacy","isExpanded"]);const{locale:p,toLocaleHref:x}=m;var q=Pt(),E=Dt(q);w(E,B=>{}),l(h,q),At()}Et(["click"]);export{Ke as P,Se as R};
