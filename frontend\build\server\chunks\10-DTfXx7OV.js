import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });
  const communes = invitations.length ? await api.commune.list.get(
    { ids: invitations.map(({ communeId }) => communeId) },
    { fetch, ctx: { url } }
  ) : [];
  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));
  const invitationsWithDetails = invitations.map((invitation) => ({
    ...invitation,
    commune: communeMap.get(invitation.communeId)
  }));
  return {
    invitations: invitationsWithDetails,
    isHasMoreInvitations: invitations.length === consts_exports.PAGE_SIZE
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 10;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-B2iFsfhh.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/communes/invitations/+page.ts";
const imports = ["_app/immutable/nodes/10.Qgo4_yo4.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/CL12WlkV.js"];
const stylesheets = ["_app/immutable/assets/10.DefDkanu.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=10-DTfXx7OV.js.map
