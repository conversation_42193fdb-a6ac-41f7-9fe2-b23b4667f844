import { z } from "zod";
import { ConfigService as NestConfigService } from "@nestjs/config";
import { Injectable, OnModuleInit } from "@nestjs/common";

function boolean(_default = false) {
    return z
        .string()
        .optional()
        .transform((str) =>
            str === undefined ? _default : !!str && str !== "0",
        );
}

export type Config = Normalize<z.infer<typeof ConfigSchema>>;
export const ConfigSchema = z.object({
    instance: z.object({
        name: z.string().nonempty(),
        // domain: z.string().nonempty(),

        emailDomain: z.string().nonempty(),
    }),

    auth: z.object({
        disableRegisterOtpCheck: boolean(),
        disableLoginOtpCheck: boolean(),

        otpExpirationTimeMs: z.coerce.number().int().positive(),
    }),

    minio: z.object({
        endpoint: z.string().nonempty(),
        port: z.coerce.number().int().positive(),
        accessKey: z.string().nonempty(),
        secretKey: z.string().nonempty(),
        useSSL: boolean(true),
    }),

    email: z.object({
        host: z.string().nonempty(),
        port: z.coerce.number().int().positive(),
        user: z.string().nonempty(),
        pass: z.string().nonempty(),

        disableAllEmails: boolean(),
        disableOtpEmails: boolean(),
        disableInviteEmails: boolean(),

        ignoreErrors: boolean(),
        rejectUnauthorized: z.boolean().optional(),

        otpEmailSender: z.string().nonempty(),
        inviteEmailSender: z.string().nonempty(),
    }),
});

@Injectable()
export class ConfigService implements OnModuleInit {
    config: DeepReadonly<Config>;

    constructor(private readonly configService: NestConfigService) {}

    onModuleInit() {
        const rawConfig: DeepPartialUnknown<z.input<typeof ConfigSchema>> = {
            instance: {
                name: this.configService.get("INSTANCE_NAME"),
                emailDomain: this.configService.get("INSTANCE_EMAIL_DOMAIN"),
            },

            auth: {
                disableRegisterOtpCheck: this.configService.get(
                    "DISABLE_REGISTER_OTP_CHECK",
                ),
                disableLoginOtpCheck: this.configService.get(
                    "DISABLE_LOGIN_OTP_CHECK",
                ),
                otpExpirationTimeMs: this.configService.get(
                    "OTP_EXPIRATION_TIME_MS",
                ),
            },

            email: {
                host: this.configService.get("EMAIL_HOST"),
                port: this.configService.get("EMAIL_PORT"),
                user: this.configService.get("EMAIL_USER"),
                pass: this.configService.get("EMAIL_PASSWORD"),

                disableAllEmails: this.configService.get("DISABLE_ALL_EMAILS"),
                disableOtpEmails: this.configService.get("DISABLE_OTP_EMAILS"),
                disableInviteEmails: this.configService.get(
                    "DISABLE_INVITE_EMAILS",
                ),

                ignoreErrors: this.configService.get("EMAIL_IGNORE_ERRORS"),
                rejectUnauthorized: this.configService.get(
                    "EMAIL_REJECT_UNAUTHORIZED",
                ),

                otpEmailSender: this.configService.get("OTP_EMAIL_SENDER"),
                inviteEmailSender: this.configService.get(
                    "INVITE_EMAIL_SENDER",
                ),
            },

            minio: {
                endpoint: this.configService.get("MINIO_ENDPOINT"),
                port: this.configService.get("MINIO_PORT"),
                accessKey: this.configService.get("MINIO_ACCESS_KEY"),
                secretKey: this.configService.get("MINIO_SECRET_KEY"),
                useSSL: this.configService.get("MINIO_USE_SSL"),
            },
        };

        const parsedConfig = ConfigSchema.safeParse(rawConfig);

        if (!parsedConfig.success) {
            throw new Error(
                `Invalid configuration: ${JSON.stringify(parsedConfig.error.issues, null, 2)}`,
            );
        }

        this.config = parsedConfig.data;
    }
}
