# Docker Setup for Commune Application

This repository contains Docker configurations for running the Commune application with both backend and frontend services.

## Prerequisites

- Docker
- Docker Compose
- A PostgreSQL database (external)
- MinIO instance (external)
- Redis instance (external, if used)

## Quick Start

1. **Copy the environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file with your actual configuration values:**
   - Database connection string
   - Session secret
   - MinIO credentials and configuration
   - Email configuration (if needed)
   - Redis URL (if used)

3. **Build and run the services:**
   ```bash
   docker-compose up --build
   ```

4. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:4000

## Services

### Backend (NestJS)
- **Port:** 4000
- **Health Check:** Available at `/` (returns "Hello World!")
- **Dependencies:** PostgreSQL database, MinIO, Redis (optional)
- **Features:**
  - Prisma ORM with automatic migrations
  - Session management with file store
  - CORS enabled for frontend communication
  - Health checks for container monitoring

### Frontend (SvelteKit)
- **Port:** 3000
- **Dependencies:** Backend service
- **Features:**
  - Server-side rendering with SvelteKit
  - Proxy configuration for API and image requests
  - Health checks for container monitoring

## Environment Variables

See `.env.example` for all required environment variables. Key variables include:

- `DATABASE_URL`: PostgreSQL connection string
- `SESSION_SECRET`: Secret key for session management
- `MINIO_*`: MinIO configuration for file storage
- `REDIS_URL`: Redis connection string (if used)
- `EMAIL_*`: Email service configuration

## Development vs Production

This Docker setup is configured for production use. For development:

1. Use the existing development commands:
   ```bash
   # Backend
   cd backend && npm run dev
   
   # Frontend
   cd frontend && npm run dev
   ```

2. Or modify the docker-compose.yml to mount source code volumes for hot reloading.

## Troubleshooting

1. **Backend fails to start:**
   - Check database connectivity
   - Verify all environment variables are set
   - Check Prisma migrations are applied

2. **Frontend can't connect to backend:**
   - Ensure backend is healthy
   - Check network configuration
   - Verify CORS settings

3. **Health checks failing:**
   - Check if services are actually running on expected ports
   - Verify curl is available in containers
   - Check service startup time vs health check timing

## Commands

```bash
# Build and start services
docker-compose up --build

# Start services in background
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f backend
docker-compose logs -f frontend

# Rebuild specific service
docker-compose build backend
docker-compose build frontend

# Scale services (if needed)
docker-compose up --scale frontend=2

# Execute commands in running containers
docker-compose exec backend npm run generate  # Regenerate Prisma client
docker-compose exec backend npx prisma migrate deploy  # Run migrations
```

## Notes

- The backend includes automatic Prisma migration deployment on startup
- Sessions are persisted using a Docker volume
- Both services include health checks for monitoring
- The setup uses multi-stage builds for optimized image sizes
- Non-root users are used for security
- The shared `@commune/api` library is built automatically during the Docker build process
- Both frontend and backend depend on the libs/api package which contains shared types and utilities
- Dockerfiles explicitly copy only necessary files (no .dockerignore used) for better control and transparency
