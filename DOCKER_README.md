# Docker Setup for Commune Application

This repository contains Docker configurations for running the Commune application with both backend and frontend services.

## Prerequisites

- Docker
- Docker Compose
- A PostgreSQL database (external)
- MinIO instance (external)
- Redis instance (external, if used)

## Quick Start

1. **Copy the environment files:**
   ```bash
   # Backend environment
   cp backend/.env.example backend/.env

   # Frontend environment
   cp frontend/.env.example frontend/.env
   ```

2. **Edit the environment files with your actual configuration values:**

   **Backend (`backend/.env`):**
   - Database connection string
   - Session secret
   - MinIO credentials and configuration
   - Email configuration
   - Redis URL (if used)
   - CORS frontend URL

   **Frontend (`frontend/.env`):**
   - Server port configuration
   - MinIO host for image proxy
   - Public domain and contact information

3. **Build and run the services:**
   ```bash
   docker-compose up --build
   ```

4. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:4000

## Services

### Backend (NestJS)
- **Port:** 4000
- **Health Check:** Available at `/` (returns "Hello World!")
- **Dependencies:** PostgreSQL database, MinIO, Redis (optional)
- **Features:**
  - Prisma ORM with automatic migrations
  - Session management with file store
  - CORS enabled for frontend communication
  - Health checks for container monitoring

### Frontend (SvelteKit)
- **Port:** 3000
- **Dependencies:** Backend service
- **Features:**
  - Server-side rendering with SvelteKit
  - Proxy configuration for API and image requests
  - Health checks for container monitoring

## Environment Variables

Environment variables are separated into backend and frontend specific files:

### Backend Environment (`backend/.env`)
- `DATABASE_URL`: PostgreSQL connection string
- `SESSION_SECRET`: Secret key for session management
- `MINIO_HOST`, `MINIO_PORT`, `MINIO_ACCESS_KEY`, `MINIO_SECRET_KEY`, `MINIO_BUCKET_NAME`: MinIO configuration for file storage
- `REDIS_URL`: Redis connection string (if used)
- `EMAIL_HOST`, `EMAIL_PORT`, `EMAIL_USER`, `EMAIL_PASSWORD`: Email service configuration
- `FRONTEND_URL`: Frontend URL for CORS configuration
- `NODE_ENV`: Node environment (production/development)

### Frontend Environment (`frontend/.env`)
- `PORT`: Server port (default: 3000)
- `MINIO_HOST`: MinIO host for image proxy
- `PUBLIC_DOMAIN`: Public domain for the application
- `PUBLIC_FOOTER_CONTACT_EMAIL`: Contact email displayed in footer
- `NODE_ENV`: Node environment (production/development)

**Note:** Some frontend environment variables (`API_URL`, `VITE_*`, `PUBLIC_*`) are needed at build time and are configured as build arguments in docker-compose.yml. To customize these for your environment, modify the `args` section in the frontend service of docker-compose.yml.

## Development vs Production

This Docker setup is configured for production use. For development:

1. Use the existing development commands:
   ```bash
   # Backend
   cd backend && npm run dev
   
   # Frontend
   cd frontend && npm run dev
   ```

2. Or modify the docker-compose.yml to mount source code volumes for hot reloading.

## Troubleshooting

1. **Backend fails to start:**
   - Check database connectivity
   - Verify all environment variables are set
   - Check Prisma migrations are applied

2. **Frontend can't connect to backend:**
   - Ensure backend is healthy
   - Check network configuration
   - Verify CORS settings

3. **Health checks failing:**
   - Check if services are actually running on expected ports
   - Verify curl is available in containers
   - Check service startup time vs health check timing

## Commands

```bash
# Build and start services
docker-compose up --build

# Start services in background
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f backend
docker-compose logs -f frontend

# Rebuild specific service
docker-compose build backend
docker-compose build frontend

# Scale services (if needed)
docker-compose up --scale frontend=2

# Execute commands in running containers
docker-compose exec backend npm run generate  # Regenerate Prisma client
docker-compose exec backend npx prisma migrate deploy  # Run migrations
```

## Notes

- The backend includes automatic Prisma migration deployment on startup
- Sessions are persisted using a Docker volume
- Both services include health checks for monitoring
- The setup uses multi-stage builds for optimized image sizes
- Non-root users are used for security
- The shared `@commune/api` library is built automatically during the Docker build process
- Both frontend and backend depend on the libs/api package which contains shared types and utilities
- Dockerfiles explicitly copy only necessary files (no .dockerignore used) for better control and transparency
