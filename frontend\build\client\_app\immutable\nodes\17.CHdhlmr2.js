import{g as iu}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{p as lu,c as ou,f as S,a as Au,s as l,ax as r,g as u,d as e,b as I,u as f,av as B,r as a,t as P,aw as qu,h as Ju,$ as Hu}from"../chunks/RHWQbow4.js";import{e as Xu,s as n,d as Bu}from"../chunks/BlWcudmi.js";import{i as J}from"../chunks/CtoItwj4.js";import{s as G}from"../chunks/BdpLTtcP.js";import{s as Zu}from"../chunks/Cxg-bych.js";import{s as X}from"../chunks/CaC9IHEK.js";import{g as Yu}from"../chunks/KKeKRB0S.js";import{c as ru,r as Ku}from"../chunks/CVTn1FV4.js";import{f as Qu}from"../chunks/CL12WlkV.js";import{M as Iu,e as Vu,a as $u,f as u4}from"../chunks/CBe4EX5h.js";import"../chunks/BiLRrsV0.js";import"../chunks/DiZKRWcx.js";const e4=async({fetch:b,url:t})=>{const{fetcher:g}=iu();return{me:await g.user.me.get({fetch:b,ctx:{url:t}})}},M4=Object.freeze(Object.defineProperty({__proto__:null,load:e4},Symbol.toStringTag,{value:"Module"}));var a4=S('<div class="alert alert-success mb-3"> </div>'),t4=S('<div class="alert alert-danger mb-3"> </div>'),r4=S("<!> <!> <form><!> <!></form>",1);function i4(b,t){lu(t,!0);const g={en:{editProfile:"Edit Profile",name:{label:"Name",placeholder:"Enter your name"},description:{label:"Description (optional)",placeholder:"Tell us about yourself"},saveChanges:"Save Changes",cancel:"Cancel",saving:"Saving...",nameRequired:"Name is required",failedToUpdateProfile:"Failed to update profile",profileUpdatedSuccessfully:"Profile updated successfully",errorOccurred:"An error occurred while updating profile"},ru:{editProfile:"Редактировать профиль",name:{label:"Имя",placeholder:"Введите ваше имя"},description:{label:"Описание (необязательно)",placeholder:"Расскажите о себе"},saveChanges:"Сохранить изменения",cancel:"Отменить",saving:"Сохранение...",nameRequired:"Имя обязательно",failedToUpdateProfile:"Не удалось обновить профиль",profileUpdatedSuccessfully:"Профиль обновлен успешно",errorOccurred:"Произошла ошибка при обновлении профиля"}},{fetcher:x}=iu(),o=f(()=>g[t.locale]);let c=B(""),i=B(!1),s=B(!1),m=f(()=>{var d;return((d=t.userData)==null?void 0:d.name)||[]}),C=f(()=>{var d;return((d=t.userData)==null?void 0:d.description)||[]});const N=async()=>{if(!u(m).some(d=>d.value.trim().length)){r(c,u(o).nameRequired,!0);return}r(i,!0),r(c,"");try{await x.user.patch({id:t.userData.id,name:u(m),description:u(C)}),r(s,!0),setTimeout(()=>{O(),t.onProfileUpdated()},1500)}catch(d){r(c,d instanceof Error?d.message:u(o).errorOccurred,!0),console.error(d)}finally{r(i,!1)}},O=()=>{r(c,""),r(s,!1),t.onHide()};{let d=f(()=>u(i)?u(o).saving:u(o).saveChanges),U=f(()=>!u(m).some(E=>E.value.trim().length)||u(i));Iu(b,{get show(){return t.show},get title(){return u(o).editProfile},onClose:O,onSubmit:N,get submitText(){return u(d)},get cancelText(){return u(o).cancel},get submitDisabled(){return u(U)},get cancelDisabled(){return u(i)},get isSubmitting(){return u(i)},children:(E,R)=>{var M=r4(),j=Au(M);{var W=v=>{var _=a4(),D=e(_,!0);a(_),P(()=>n(D,u(o).profileUpdatedSuccessfully)),I(v,_)};J(j,v=>{u(s)&&v(W)})}var w=l(j,2);{var k=v=>{var _=t4(),D=e(_,!0);a(_),P(()=>n(D,u(c))),I(v,_)};J(w,v=>{u(c)&&v(k)})}var T=l(w,2),y=e(T);Vu(y,{id:"profileName",get label(){return u(o).name.label},get placeholder(){return u(o).name.placeholder},required:!0,get locale(){return t.locale},get value(){return u(m)},set value(v){r(m,v)}});var A=l(y,2);$u(A,{id:"profileDescription",get label(){return u(o).description.label},get placeholder(){return u(o).description.placeholder},rows:4,get locale(){return t.locale},get value(){return u(C)},set value(v){r(C,v)}}),a(T),Xu("submit",T,v=>{v.preventDefault(),N()}),I(E,M)},$$slots:{default:!0}})}ou()}const l4=(b,t,g,x,o)=>{const c=b.target.files;if(r(t,""),!c||c.length===0){r(g,null),r(x,null);return}const i=c[0];if(!ru.ALLOWED_IMAGE_FILE_TYPES.includes(i.type)){r(t,u(o).invalidFileTypeError,!0),r(g,null),r(x,null);return}if(i.size>ru.MAX_IMAGE_FILE_SIZE){r(t,u(o).fileTooLarge,!0),r(g,null),r(x,null);return}r(g,i,!0);const s=URL.createObjectURL(i);return r(x,s,!0),()=>{URL.revokeObjectURL(s)}};var o4=S('<div class="alert alert-success mb-3"> </div>'),s4=S('<div class="alert alert-danger mb-3"> </div>'),d4=S('<div class="mt-3 text-center"><img alt="Preview" class="img-thumbnail"/></div>'),n4=S('<!> <!> <form><div class="mb-3"><label for="imageInput" class="form-label"> </label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"/> <p class="form-text text-muted"> </p> <!></div></form>',1);function c4(b,t){lu(t,!0);const g=ru.MAX_IMAGE_FILE_SIZE/(1024*1024),x={en:{uploadImage:"Upload Profile Image",upload:"Upload",cancel:"Cancel",uploading:"Uploading...",imageUploadedSuccessfully:"Image uploaded successfully!",pleaseSelectImage:"Please select an image to upload",invalidFileTypeError:"Invalid file type. Please upload a JPG, PNG, or WebP image.",fileTooLarge:`File is too large. Maximum size is ${g}MB.`,failedToUploadImage:"Failed to upload image",errorOccurred:"An error occurred while uploading the image",uploadImageMaxSize:`Upload an image (JPG, PNG, WebP), max ${g}MB.`},ru:{uploadImage:"Загрузить изображение профиля",upload:"Загрузить",cancel:"Отменить",uploading:"Загрузка...",imageUploadedSuccessfully:"Изображение загружено успешно!",pleaseSelectImage:"Пожалуйста, выберите изображение для загрузки",invalidFileTypeError:"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",fileTooLarge:`Файл слишком большой. Максимальный размер - ${g}MB.`,failedToUploadImage:"Не удалось загрузить изображение",errorOccurred:"Произошла ошибка при загрузке изображения",uploadImageMaxSize:`Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${g}MB.`}},o=f(()=>x[t.locale]);let c=B(null),i=B(null),s=B(""),m=B(!1),C=B(!1);const N=async()=>{if(!u(c)){r(s,u(o).pleaseSelectImage,!0);return}r(m,!0),r(s,"");try{const d=new FormData;d.append("image",u(c));const U=await u4(`/api/user/${t.userId}/image`,{method:"PUT",body:d});if(!U.ok){const R=await U.json();throw new Error(R.message||u(o).failedToUploadImage)}r(C,!0),t.onImageUploaded();const E=document.getElementById("imageInput");E&&(E.files=null),setTimeout(()=>{O()},1500)}catch(d){r(s,d instanceof Error?d.message:u(o).errorOccurred,!0),console.error(d)}finally{r(m,!1)}},O=()=>{r(c,null),r(i,null),r(s,""),r(C,!1),t.onHide()};{let d=f(()=>u(m)?u(o).uploading:u(o).upload),U=f(()=>!u(c)||u(m));Iu(b,{get show(){return t.show},get title(){return u(o).uploadImage},onClose:O,onSubmit:N,get submitText(){return u(d)},get cancelText(){return u(o).cancel},get submitDisabled(){return u(U)},get cancelDisabled(){return u(m)},get isSubmitting(){return u(m)},size:"lg",children:(E,R)=>{var M=n4(),j=Au(M);{var W=h=>{var p=o4(),z=e(p,!0);a(p),P(()=>n(z,u(o).imageUploadedSuccessfully)),I(h,p)};J(j,h=>{u(C)&&h(W)})}var w=l(j,2);{var k=h=>{var p=s4(),z=e(p,!0);a(p),P(()=>n(z,u(s))),I(h,p)};J(w,h=>{u(s)&&h(k)})}var T=l(w,2),y=e(T),A=e(y),v=e(A,!0);a(A);var _=l(A,2);_.__change=[l4,s,c,i,o];var D=l(_,2),K=e(D,!0);a(D);var Z=l(D,2);{var q=h=>{var p=d4(),z=e(p);X(z,"",{},{"max-height":"200px"}),a(p),P(()=>G(z,"src",u(i))),I(h,p)};J(Z,h=>{u(i)&&h(q)})}a(y),a(T),P(()=>{n(v,u(o).pleaseSelectImage),_.disabled=u(m),n(K,u(o).uploadImageMaxSize)}),I(E,M)},$$slots:{default:!0}})}ou()}Bu(["change"]);const v4=async(b,t)=>{try{await t.auth.signOut.get()}catch(g){console.error("Error during logout:",g)}finally{Ku(),Yu("/")}};var m4=(b,t)=>r(t,!0),g4=(b,t)=>r(t,!0),p4=S('<p class="mb-0"> </p>'),f4=(b,t)=>r(t,!0),b4=S('<div class="text-center text-muted py-4"><i class="bi bi-file-earmark-text fs-1 mb-2"></i> <p> </p> <button class="btn btn-sm btn-primary"><i class="bi bi-plus-circle me-1"></i> </button></div>'),_4=S('<div class="container-fluid py-4"><div class="row g-4"><div class="col-lg-3"><div class="card border-0 shadow-sm h-100"><div class="text-center p-4"><div class="position-relative mx-auto mb-3"><div class="bg-light rounded-circle overflow-hidden border" role="img"><div class="position-relative w-100 h-100"><img/></div></div> <button class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0"><i class="bi bi-plus"></i></button></div> <h4 class="fw-bold mb-1"> </h4> <span> </span> <div class="d-grid gap-2 mt-3"><button class="btn btn-primary"><i class="bi bi-gear me-2"></i> </button> <button class="btn btn-outline-danger"><i class="bi bi-box-arrow-right me-2"></i> </button></div></div> <div class="card-footer bg-light border-top p-3"><div class="d-flex align-items-center mb-2"><i class="bi bi-envelope text-muted me-2"></i> <div class="text-truncate"><small class="text-muted"> </small></div></div> <div class="d-flex align-items-center"><i class="bi bi-calendar3 text-muted me-2"></i> <div><small class="text-muted"> </small></div></div></div></div></div> <div class="col-lg-9"><div class="card border-0 shadow-sm mb-4"><div class="card-header bg-transparent border-bottom-0 pb-0"><h5 class="fw-bold"> </h5></div> <div class="card-body"><div class="row g-4"><div class="col-md-4"><div class="border rounded p-3 text-center h-100"><div class="mb-2"><i class="bi bi-shield-check fs-3 text-primary"></i></div> <h2 class="mb-0 fw-bold"> </h2> <p class="text-muted mb-0"> </p></div></div> <div class="col-md-4"><div class="border rounded p-3 text-center h-100"><div class="mb-2"><i class="bi bi-calendar-check fs-3 text-primary"></i></div> <h2 class="mb-0 fw-bold"> </h2> <p class="text-muted mb-0"> </p></div></div></div></div></div> <div class="card border-0 shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center bg-transparent"><h5 class="fw-bold mb-0"> </h5></div> <div class="card-body"><!></div></div></div></div> <!> <!></div>');function T4(b,t){lu(t,!0);const g={en:{_page:{title:"Profile — Commune"},loading:"Loading...",loadingProfile:"Loading your profile...",uploadImage:"Upload profile image",admin:"Administrator",user:"User",editProfile:"Edit Profile",signOut:"Sign Out",joined:"Joined",accountSummary:"Account Summary",accountType:{title:"Account Type",values:{admin:"Administrator",moderator:"Moderator",user:"User"}},daysAsMember:"Days as member",aboutMe:"About Me",noDescription:"No description available yet. Add one to tell others about yourself.",addDescription:"Add Description",dateFormatLocale:"en-US"},ru:{_page:{title:"Профиль — Коммуна"},loading:"Загрузка...",loadingProfile:"Загрузка вашего профиля...",uploadImage:"Загрузить изображение профиля",admin:"Администратор",user:"Пользователь",editProfile:"Редактировать профиль",signOut:"Выйти",joined:"Присоединился",accountSummary:"Информация об аккаунте",accountType:{title:"Тип аккаунта",values:{admin:"Администратор",moderator:"Модератор",user:"Пользователь"}},daysAsMember:"Дней в качестве участника",aboutMe:"Обо мне",noDescription:"Нет описания. Добавьте описание, чтобы рассказать другим о себе.",addDescription:"Добавить описание",dateFormatLocale:"ru-RU"}},{fetcher:x}=iu(),o=f(()=>t.data.locale),c=f(()=>t.data.getAppropriateLocalization),i=f(()=>g[u(o)]);let s=qu(t.data.me),m=B(!1),C=B(!1);const N=f(()=>u(c)(s.name)),O=f(()=>u(c)(s.description)),d=f(()=>new Date(s.createdAt));function U(){window.location.reload()}var E=_4();Ju(F=>{P(()=>Hu.title=u(i)._page.title)});var R=e(E),M=e(R),j=e(M),W=e(j),w=e(W);X(w,"",{},{width:"120px",height:"120px"});var k=e(w);X(k,"",{},{width:"100%",height:"100%"});var T=e(k),y=e(T);G(y,"width",120),G(y,"height",120),X(y,"",{},{width:"100%",height:"100%","object-fit":"cover"}),a(T),a(k);var A=l(k,2);A.__click=[m4,C],X(A,"",{},{width:"30px",height:"30px",display:"flex","align-items":"center","justify-content":"center"}),a(w);var v=l(w,2),_=e(v,!0);a(v);var D=l(v,2),K=e(D,!0);a(D);var Z=l(D,2),q=e(Z);q.__click=[g4,m];var h=l(e(q));a(q);var p=l(q,2);p.__click=[v4,x];var z=l(e(p));a(p),a(Z),a(W);var su=l(W,2),Q=e(su),du=l(e(Q),2),nu=e(du),Pu=e(nu,!0);a(nu),a(du),a(Q);var cu=l(Q,2),vu=l(e(cu),2),mu=e(vu),Su=e(mu);a(mu),a(vu),a(cu),a(su),a(j),a(M);var gu=l(M,2),V=e(gu),$=e(V),pu=e($),Uu=e(pu,!0);a(pu),a($);var fu=l($,2),bu=e(fu),uu=e(bu),_u=e(uu),eu=l(e(_u),2),Mu=e(eu,!0);a(eu);var hu=l(eu,2),Tu=e(hu,!0);a(hu),a(_u),a(uu);var Eu=l(uu,2),Du=e(Eu),au=l(e(Du),2),Lu=e(au,!0);a(au);var xu=l(au,2),Ou=e(xu,!0);a(xu),a(Du),a(Eu),a(bu),a(fu),a(V);var Cu=l(V,2),tu=e(Cu),yu=e(tu),ju=e(yu,!0);a(yu),a(tu);var Fu=l(tu,2),ku=e(Fu);{var zu=F=>{var L=p4(),H=e(L,!0);a(L),P(()=>n(H,u(O))),I(F,L)},Gu=F=>{var L=b4(),H=l(e(L),2),Ru=e(H,!0);a(H);var Y=l(H,2);Y.__click=[f4,m];var Wu=l(e(Y));a(Y),a(L),P(()=>{n(Ru,u(i).noDescription),G(Y,"aria-label",u(i).addDescription),n(Wu,` ${u(i).addDescription??""}`)}),I(F,L)};J(ku,F=>{u(O)?F(zu):F(Gu,!1)})}a(Fu),a(Cu),a(gu),a(R);var wu=l(R,2);{let F=f(()=>s||null);i4(wu,{get locale(){return u(o)},get show(){return u(m)},onHide:()=>r(m,!1),get userData(){return u(F)},onProfileUpdated:U})}var Nu=l(wu,2);c4(Nu,{get locale(){return u(o)},get show(){return u(C)},onHide:()=>r(C,!1),get userId(){return s.id},onImageUploaded:U}),a(E),P((F,L)=>{G(y,"src",s.image?`/images/${s.image}`:"/images/default-avatar.png"),G(y,"alt",`${u(N)}'s avatar`),G(A,"title",u(i).uploadImage),G(A,"aria-label",u(i).uploadImage),n(_,u(N)),Zu(D,1,`badge bg-${s.role==="admin"?"danger":"primary"} mb-3`),n(K,s.role==="admin"?u(i).admin:u(i).user),n(h,` ${u(i).editProfile??""}`),n(z,` ${u(i).signOut??""}`),n(Pu,s.email),n(Su,`${u(i).joined??""} ${F??""}`),n(Uu,u(i).accountSummary),n(Mu,u(i).accountType.values[s.role]),n(Tu,u(i).accountType.title),n(Lu,L),n(Ou,u(i).daysAsMember),n(ju,u(i).aboutMe)},[()=>Qu(u(d),u(o)),()=>Math.floor((new Date().getTime()-u(d).getTime())/(1e3*60*60*24))]),I(b,E),ou()}Bu(["click"]);export{T4 as component,M4 as universal};
