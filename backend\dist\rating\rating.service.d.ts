import type { Rating } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, UserRatingEntityType } from "@prisma/client";
export declare class RatingService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getKarmaPoints(input: Rating.GetKarmaPointsInput): Promise<({
        comment: {
            id: string;
            value: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            locale: import("@prisma/client").$Enums.Locale;
            key: string;
        }[];
        sourceUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                id: string;
                value: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                locale: import("@prisma/client").$Enums.Locale;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            imageId: string | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        sourceUserId: string;
        targetUserId: string;
        quantity: number;
    })[]>;
    spendKarmaPoint(data: Rating.SpendKarmaPointInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserFeedbacks(input: Rating.GetUserFeedbacksInput): Promise<({
        sourceUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                id: string;
                value: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                locale: import("@prisma/client").$Enums.Locale;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            imageId: string | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
        text: {
            id: string;
            value: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            locale: import("@prisma/client").$Enums.Locale;
            key: string;
        }[];
    } & {
        id: string;
        value: number;
        createdAt: Date;
        updatedAt: Date;
        isAnonymous: boolean;
        sourceUserId: string;
        targetUserId: string;
    })[]>;
    createUserFeedback(data: Rating.CreateUserFeedbackInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserSummary(input: Rating.GetUserSummaryInput): Promise<{
        rating: number;
        karma: number;
        rate: number | null;
    }>;
    upsertRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
        value: number;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
    deleteRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
}
