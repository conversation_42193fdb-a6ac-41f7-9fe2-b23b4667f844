import * as universal from '../entries/pages/__locale__/reactor/_page.ts.js';

export const index = 25;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/__locale__/reactor/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/[[locale]]/reactor/+page.ts";
export const imports = ["_app/immutable/nodes/25.CZ_w9bXd.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/CBe4EX5h.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/Dp-ac-BK.js","_app/immutable/chunks/C_sRNQCS.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/Np2weedy.js"];
export const stylesheets = ["_app/immutable/assets/create-post-modal.BRelZfpq.css","_app/immutable/assets/right-menu.BCyxSBRm.css","_app/immutable/assets/25.D23-cVSR.css"];
export const fonts = [];
