import { Reactor } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class ReactorCommunityService {
    private readonly prisma;
    private readonly minioService;
    private readonly logger;
    constructor(prisma: PrismaService, minioService: MinioService);
    getCommunityIds(): Promise<string[]>;
    getCommunities(input: Reactor.GetCommunitiesInput, user: CurrentUser | null): Promise<{
        id: string;
        description: {
            id: string;
            value: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            locale: import("@prisma/client").$Enums.Locale;
            key: string;
        }[];
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        hub: ({
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                id: string;
                value: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                locale: import("@prisma/client").$Enums.Locale;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            headUserId: string;
            imageId: string | null;
        }) | null;
        headUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                id: string;
                value: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                locale: import("@prisma/client").$Enums.Locale;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            imageId: string | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        name: {
            id: string;
            value: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            locale: import("@prisma/client").$Enums.Locale;
            key: string;
        }[];
    }[]>;
    createCommunity(input: Reactor.CreateCommunityInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        hubId: string | null;
        deletedAt: Date | null;
        headUserId: string;
        imageId: string | null;
    }>;
    updateCommunity(input: Reactor.UpdateCommunityInput, user: CurrentUser): Promise<void>;
    updateCommunityImage(id: string, file: Express.Multer.File, user: CurrentUser): Promise<void>;
    deleteCommunityImage(id: string, user: CurrentUser): Promise<void>;
}
