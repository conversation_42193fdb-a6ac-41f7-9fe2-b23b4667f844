const index = 32;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-HhgUYHlU.js')).default;
const imports = ["_app/immutable/nodes/32.FwxhMujG.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=32-BqMdZfkA.js.map
