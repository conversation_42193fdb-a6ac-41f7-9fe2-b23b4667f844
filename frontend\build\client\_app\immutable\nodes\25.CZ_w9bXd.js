import{c as W4}from"../chunks/CVTn1FV4.js";import{g as g4}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{o as _0}from"../chunks/DeAm3Eed.js";import{p as Y4,o as wu,ax as i,ay as $4,a as s4,b,c as uu,av as m,f as O,s as a,d as e,r as t,g as u,t as T,u as X,aQ as m0,aR as g0,aw as K4,h as E0,$ as p0}from"../chunks/RHWQbow4.js";import{d as eu,s}from"../chunks/BlWcudmi.js";import{i as J}from"../chunks/CtoItwj4.js";import{e as ku,i as f0}from"../chunks/Dnfvvefi.js";import{s as G,r as b0}from"../chunks/BdpLTtcP.js";import{s as X4}from"../chunks/Cxg-bych.js";import{b as h0}from"../chunks/B5DcI8qy.js";import{b as Fu,C as D0}from"../chunks/CBe4EX5h.js";import"../chunks/BiLRrsV0.js";import{R as x0,P as B0}from"../chunks/Dp-ac-BK.js";import{i as y0,s as C0}from"../chunks/Np2weedy.js";const F0=async({fetch:g,url:o})=>{const{fetcher:c}=g4();await c.user.me.get({fetch:g,skipInterceptor:!0}).catch(()=>null);const[h,r]=await Promise.all([c.reactor.post.list.get({lensId:null},{fetch:g,ctx:{url:o}}),c.reactor.lens.list.get({fetch:g,ctx:{url:o}})]);return{posts:h,lenses:r,isHasMorePosts:h.length===W4.PAGE_SIZE}},_e=Object.freeze(Object.defineProperty({__proto__:null,load:F0},Symbol.toStringTag,{value:"Module"}));function Lu(g,o){o.onClose()}function L0(g,o){i(o,!u(o))}async function w0(g,o,c,h,r,D,L,x,_,q){if(i(o,null),i(c,null),!u(h).trim()){i(o,u(r).nameRequired,!0);return}if(!u(D).trim()){i(o,u(r).codeRequired,!0);return}i(L,!0);try{u(x)&&_.lens?(await q.reactor.lens.patch({id:_.lens.id,name:u(h).trim(),code:u(D).trim()}),i(c,u(r).updateSuccess,!0),_.onLensUpdated(_.lens.id)):(await q.reactor.lens.post({name:u(h).trim(),code:u(D).trim()}),i(c,u(r).createSuccess,!0),_.onLensUpdated()),setTimeout(()=>{_.onClose()},1500)}catch(w){console.error("Error saving lens:",w),i(o,w instanceof Error?w.message:u(x)?u(r).updateError:u(r).createError,!0)}finally{i(L,!1)}}var k0=O(`<div class="guide-content svelte-k8cnxu"><h6 class="mb-3"> </h6> <div class="mb-4"><h6 class="text-primary"> </h6> <div class="table-responsive"><table class="table table-sm"><thead><tr><th> </th><th> </th><th> </th><th> </th></tr></thead><tbody><tr><td><code>hub</code></td><td><code>=</code>, <code>!=</code></td><td> </td><td> </td></tr><tr><td><code>community</code></td><td><code>=</code>, <code>!=</code></td><td> </td><td> </td></tr><tr><td><code>author</code></td><td><code>=</code>, <code>!=</code></td><td> </td><td> </td></tr><tr><td><code>rating</code></td><td><code>=</code>, <code>!=</code>, <code>&gt;</code>, <code>&gt;=</code>, <code>&lt;</code>, <code>&lt;=</code></td><td> </td><td> </td></tr><tr><td><code>usefulness</code></td><td><code>=</code>, <code>!=</code>, <code>&gt;</code>, <code>&gt;=</code>, <code>&lt;</code>, <code>&lt;=</code></td><td> </td><td> </td></tr><tr><td><code>tag</code></td><td><code>=</code>, <code>!=</code></td><td> </td><td> </td></tr><tr><td><code>title</code></td><td><code>~</code></td><td> </td><td> </td></tr><tr><td><code>body</code></td><td><code>~</code></td><td> </td><td> </td></tr><tr><td><code>age</code></td><td><code>&gt;</code>, <code>&gt;=</code>, <code>&lt;</code>, <code>&lt;=</code></td><td> </td><td> </td></tr></tbody></table></div></div> <div class="mb-4"><h6 class="text-primary"> </h6> <ul class="list-unstyled"><li><code>&&</code> </li> <li><code>||</code> </li> <li><code>()</code> </li></ul></div> <div class="mb-4"><h6 class="text-primary"> </h6> <ul class="list-unstyled"><li><code>m</code> </li> <li><code>h</code> </li> <li><code>d</code> </li> <li><code>w</code> </li> <li><code>mo</code> </li> <li><code>y</code> </li></ul></div> <div class="mb-4"><h6 class="text-primary"> </h6> <div class="bg-light p-3 rounded"><div class="mb-2"><strong> </strong></div> <code>rating >= 100</code> <div class="mb-2 mt-3"><strong> </strong></div> <code>age &lt; 7d</code> <div class="mb-2 mt-3"><strong> </strong></div> <code>title ~ "python"</code> <div class="mb-2 mt-3"><strong> </strong></div> <code>usefulness >= 8 && age &lt; 3d</code></div></div> <div class="mb-4"><h6 class="text-primary"> </h6> <div class="bg-light p-3 rounded"><div class="mb-2"><strong> </strong></div> <pre class="mb-0"><code>(
  (hub = ["hub-id-1", "hub-id-2"] && rating >= 100)
  || (community = ["comm-id-1"] && usefulness >= 7)
)
&& age &lt; 14d
&& (title ~ "tutorial" || body ~ "guide")</code></pre> <small class="text-muted mt-2 d-block"> </small></div></div></div>`),A0=O('<div class="alert alert-danger" role="alert"> </div>'),P0=O('<div class="alert alert-success" role="alert"> </div>'),T0=O('<form><div class="mb-3"><label for="lens-name" class="form-label"> </label> <input id="lens-name" type="text" class="form-control svelte-k8cnxu" required/></div> <div class="mb-3"><label for="lens-code" class="form-label"> </label> <textarea id="lens-code" class="form-control svelte-k8cnxu" rows="8" required></textarea></div> <!> <!></form>'),O0=O('<div class="modal-footer svelte-k8cnxu"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-primary"> </button></div>'),S0=O('<div class="modal fade show" style="display: block;" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-backdrop fade show svelte-k8cnxu"></div> <div class="modal-dialog modal-lg modal-dialog-centered svelte-k8cnxu"><div class="modal-content svelte-k8cnxu"><div class="modal-header d-flex justify-content-between align-items-center svelte-k8cnxu"><h5 class="modal-title svelte-k8cnxu"> </h5> <div class="d-flex gap-4"><button type="button" class="btn-close guide svelte-k8cnxu"></button> <button type="button" class="btn-close" aria-label="Close"></button></div></div> <div class="modal-body svelte-k8cnxu"><!></div> <!></div></div></div>');function U0(g,o){Y4(o,!0);const c={en:{createLens:"Create Lens",editLens:"Edit Lens",lensGuide:"Lens Guide",cancel:"Cancel",create:"Create",update:"Update",backToForm:"Back to Form",showGuide:"Show Guide",name:"Name",namePlaceholder:"Enter lens name...",code:"Code",nameRequired:"Name is required",codeRequired:"Code is required",createSuccess:"Lens created successfully!",updateSuccess:"Lens updated successfully!",createError:"Failed to create lens",updateError:"Failed to update lens",guideTitle:"Lens Code Guide",fieldsOperatorsTitle:"Available Fields & Operators",fieldColumn:"Field",operatorsColumn:"Operators",valueTypeColumn:"Value Type",descriptionColumn:"Description",hubDescription:"Filter by hub",communityDescription:"Filter by community",authorDescription:"Filter by author",ratingDescription:"Post rating",usefulnessDescription:"Post usefulness",tagDescription:"Filter by tags",titleDescription:"Search in title (like operator)",bodyDescription:"Search in body (like operator)",ageDescription:'Post age (e.g., "3d", "2w", "1mo")',logicalOperatorsTitle:"Logical Operators",andOperator:"AND operator (both conditions must be true)",orOperator:"OR operator (either condition can be true)",parenthesesOperator:"Parentheses for grouping expressions",ageDurationTitle:"Age Duration Units",minutesUnit:"minutes",hoursUnit:"hours",daysUnit:"days",weeksUnit:"weeks",monthsUnit:"months",yearsUnit:"years",simpleExamplesTitle:"Simple Examples",highRatedExample:"High rated posts:",recentPostsExample:"Recent posts:",titleSearchExample:"Search in title:",usefulRecentExample:"Useful and recent:",complexExampleTitle:"Complex Example",complexExampleSubtitle:"High-quality recent posts with educational content:",complexExampleDescription:'This filters for posts that meet one of two criteria: either posts from specific hubs that have a rating of 100 or higher, OR posts from a specific community that have a usefulness score of 7 or higher. Additionally, all results must be posted within the last 2 weeks and contain either "tutorial" in the title or "guide" in the body content.',arrayOfIds:"Array of IDs",integer:"Integer",integerRange:"Integer (0-10)",string:"String",durationString:"Duration string"},ru:{createLens:"Создать линзу",editLens:"Редактировать линзу",lensGuide:"Руководство по линзам",cancel:"Отмена",create:"Создать",update:"Обновить",backToForm:"Вернуться к форме",showGuide:"Показать руководство",name:"Название",namePlaceholder:"Введите название линзы...",code:"Код",nameRequired:"Название обязательно",codeRequired:"Код обязателен",createSuccess:"Линза успешно создана!",updateSuccess:"Линза успешно обновлена!",createError:"Не удалось создать линзу",updateError:"Не удалось обновить линзу",guideTitle:"Руководство по коду линз",fieldsOperatorsTitle:"Доступные поля и операторы",fieldColumn:"Поле",operatorsColumn:"Операторы",valueTypeColumn:"Тип значения",descriptionColumn:"Описание",hubDescription:"Фильтр по хабу",communityDescription:"Фильтр по сообществу",authorDescription:"Фильтр по автору",ratingDescription:"Рейтинг поста",usefulnessDescription:"Полезность поста",tagDescription:"Фильтр по тегам",titleDescription:"Поиск в заголовке (оператор подобия)",bodyDescription:"Поиск в тексте (оператор подобия)",ageDescription:'Возраст поста (например, "3d", "2w", "1mo")',logicalOperatorsTitle:"Логические операторы",andOperator:"Оператор И (оба условия должны быть истинными)",orOperator:"Оператор ИЛИ (любое из условий может быть истинным)",parenthesesOperator:"Скобки для группировки выражений",ageDurationTitle:"Единицы времени для возраста",minutesUnit:"минуты",hoursUnit:"часы",daysUnit:"дни",weeksUnit:"недели",monthsUnit:"месяцы",yearsUnit:"годы",simpleExamplesTitle:"Простые примеры",highRatedExample:"Посты с высоким рейтингом:",recentPostsExample:"Недавние посты:",titleSearchExample:"Поиск в заголовке:",usefulRecentExample:"Полезные и недавние:",complexExampleTitle:"Сложный пример",complexExampleSubtitle:"Качественные недавние посты с образовательным контентом:",complexExampleDescription:'Этот фильтр отбирает посты, которые соответствуют одному из двух критериев: либо посты из определенных хабов с рейтингом 100 или выше, ЛИБО посты из определенного сообщества с оценкой полезности 7 или выше. Дополнительно все результаты должны быть опубликованы в течение последних 2 недель и содержать либо "tutorial" в заголовке, либо "guide" в тексте.',arrayOfIds:"Массив ID",integer:"Целое число",integerRange:"Целое число (0-10)",string:"Строка",durationString:"Строка времени"}},{fetcher:h}=g4(),r=X(()=>c[o.locale]),D=X(()=>!!o.lens);let L=m(!1);const x=X(()=>u(L)?u(r).lensGuide:u(D)?u(r).editLens:u(r).createLens),_=X(()=>u(D)?u(r).update:u(r).create);let q=m(""),w=m(""),k=m(!1),S=m(null),U=m(null);wu(()=>{var I,R;o.show&&(i(q,((I=o.lens)==null?void 0:I.name)||"",!0),i(w,((R=o.lens)==null?void 0:R.code)||"",!0),i(S,null),i(U,null),i(k,!1),i(L,!1))});var H=$4(),Y=s4(H);{var $=I=>{var R=S0(),n4=a(e(R),2),i4=e(n4),e4=e(i4),r4=e(e4),A=e(r4,!0);t(r4);var j=a(r4,2),K=e(j);K.__click=[L0,L];var W=a(K,2);W.__click=[Lu,o],t(j),t(e4);var z=a(e4,2),c4=e(z);{var a4=M=>{var B=k0(),f=e(B),P=e(f,!0);t(f);var E=a(f,2),d=e(E),y=e(d,!0);t(d);var C=a(d,2),Z=e(C),Q=e(Z),V=e(Q),n=e(V),l=e(n,!0);t(n);var F=a(n),v=e(F,!0);t(F);var p=a(F),t4=e(p,!0);t(p);var l4=a(p),_4=e(l4,!0);t(l4),t(V),t(Q);var m4=a(Q),d4=e(m4),v4=a(e(d4),2),Au=e(v4,!0);t(v4);var tu=a(v4),Pu=e(tu,!0);t(tu),t(d4);var E4=a(d4),p4=a(e(E4),2),Tu=e(p4,!0);t(p4);var ru=a(p4),Ou=e(ru,!0);t(ru),t(E4);var f4=a(E4),b4=a(e(f4),2),Su=e(b4,!0);t(b4);var au=a(b4),Uu=e(au,!0);t(au),t(f4);var h4=a(f4),D4=a(e(h4),2),Iu=e(D4,!0);t(D4);var ou=a(D4),Ru=e(ou,!0);t(ou),t(h4);var x4=a(h4),B4=a(e(x4),2),Mu=e(B4,!0);t(B4);var su=a(B4),qu=e(su,!0);t(su),t(x4);var y4=a(x4),C4=a(e(y4),2),Gu=e(C4,!0);t(C4);var nu=a(C4),Hu=e(nu,!0);t(nu),t(y4);var F4=a(y4),L4=a(e(F4),2),ju=e(L4,!0);t(L4);var iu=a(L4),zu=e(iu,!0);t(iu),t(F4);var w4=a(F4),k4=a(e(w4),2),Nu=e(k4,!0);t(k4);var lu=a(k4),Zu=e(lu,!0);t(lu),t(w4);var du=a(w4),A4=a(e(du),2),Qu=e(A4,!0);t(A4);var cu=a(A4),Vu=e(cu,!0);t(cu),t(du),t(m4),t(Z),t(C),t(E);var P4=a(E,2),T4=e(P4),Ju=e(T4,!0);t(T4);var vu=a(T4,2),O4=e(vu),Ku=a(e(O4));t(O4);var S4=a(O4,2),Wu=a(e(S4));t(S4);var _u=a(S4,2),Xu=a(e(_u));t(_u),t(vu),t(P4);var U4=a(P4,2),I4=e(U4),Yu=e(I4,!0);t(I4);var mu=a(I4,2),R4=e(mu),$u=a(e(R4));t(R4);var M4=a(R4,2),u0=a(e(M4));t(M4);var q4=a(M4,2),e0=a(e(q4));t(q4);var G4=a(q4,2),t0=a(e(G4));t(G4);var H4=a(G4,2),r0=a(e(H4));t(H4);var gu=a(H4,2),a0=a(e(gu));t(gu),t(mu),t(U4);var j4=a(U4,2),z4=e(j4),o0=e(z4,!0);t(z4);var Eu=a(z4,2),N4=e(Eu),pu=e(N4),s0=e(pu,!0);t(pu),t(N4);var Z4=a(N4,4),fu=e(Z4),n0=e(fu,!0);t(fu),t(Z4);var Q4=a(Z4,4),bu=e(Q4),i0=e(bu,!0);t(bu),t(Q4);var hu=a(Q4,4),Du=e(hu),l0=e(Du,!0);t(Du),t(hu),m0(2),t(Eu),t(j4);var xu=a(j4,2),V4=e(xu),d0=e(V4,!0);t(V4);var Bu=a(V4,2),J4=e(Bu),yu=e(J4),c0=e(yu,!0);t(yu),t(J4);var Cu=a(J4,4),v0=e(Cu,!0);t(Cu),t(Bu),t(xu),t(B),T(()=>{s(P,u(r).guideTitle),s(y,u(r).fieldsOperatorsTitle),s(l,u(r).fieldColumn),s(v,u(r).operatorsColumn),s(t4,u(r).valueTypeColumn),s(_4,u(r).descriptionColumn),s(Au,u(r).arrayOfIds),s(Pu,u(r).hubDescription),s(Tu,u(r).arrayOfIds),s(Ou,u(r).communityDescription),s(Su,u(r).arrayOfIds),s(Uu,u(r).authorDescription),s(Iu,u(r).integer),s(Ru,u(r).ratingDescription),s(Mu,u(r).integerRange),s(qu,u(r).usefulnessDescription),s(Gu,u(r).arrayOfIds),s(Hu,u(r).tagDescription),s(ju,u(r).string),s(zu,u(r).titleDescription),s(Nu,u(r).string),s(Zu,u(r).bodyDescription),s(Qu,u(r).durationString),s(Vu,u(r).ageDescription),s(Ju,u(r).logicalOperatorsTitle),s(Ku,` - ${u(r).andOperator??""}`),s(Wu,` - ${u(r).orOperator??""}`),s(Xu,` - ${u(r).parenthesesOperator??""}`),s(Yu,u(r).ageDurationTitle),s($u,` - ${u(r).minutesUnit??""}`),s(u0,` - ${u(r).hoursUnit??""}`),s(e0,` - ${u(r).daysUnit??""}`),s(t0,` - ${u(r).weeksUnit??""}`),s(r0,` - ${u(r).monthsUnit??""}`),s(a0,` - ${u(r).yearsUnit??""}`),s(o0,u(r).simpleExamplesTitle),s(s0,u(r).highRatedExample),s(n0,u(r).recentPostsExample),s(i0,u(r).titleSearchExample),s(l0,u(r).usefulRecentExample),s(d0,u(r).complexExampleTitle),s(c0,u(r).complexExampleSubtitle),s(v0,u(r).complexExampleDescription)}),b(M,B)},o4=M=>{var B=T0(),f=e(B),P=e(f),E=e(P,!0);t(P);var d=a(P,2);b0(d),t(f);var y=a(f,2),C=e(y),Z=e(C,!0);t(C);var Q=a(C,2);g0(Q),t(y);var V=a(y,2);{var n=v=>{var p=A0(),t4=e(p,!0);t(p),T(()=>s(t4,u(S))),b(v,p)};J(V,v=>{u(S)&&v(n)})}var l=a(V,2);{var F=v=>{var p=P0(),t4=e(p,!0);t(p),T(()=>s(t4,u(U))),b(v,p)};J(l,v=>{u(U)&&v(F)})}t(B),T(()=>{s(E,u(r).name),G(d,"placeholder",u(r).namePlaceholder),d.disabled=u(k),s(Z,u(r).code),Q.disabled=u(k)}),Fu(d,()=>u(q),v=>i(q,v)),Fu(Q,()=>u(w),v=>i(w,v)),b(M,B)};J(c4,M=>{u(L)?M(a4):M(o4,!1)})}t(z);var N=a(z,2);{var u4=M=>{var B=O0(),f=e(B);f.__click=[Lu,o];var P=e(f,!0);t(f);var E=a(f,2);E.__click=[w0,S,U,q,r,w,k,D,o,h];var d=e(E,!0);t(E),t(B),T(y=>{f.disabled=u(k),s(P,u(r).cancel),E.disabled=y,s(d,u(k)?`${u(_)}...`:u(_))},[()=>u(k)||!u(q).trim()||!u(w).trim()]),b(M,B)};J(N,M=>{u(L)||M(u4)})}t(i4),t(n4),t(R),T(()=>{s(A,u(x)),G(K,"title",u(L)?u(r).backToForm:u(r).showGuide),G(K,"aria-label",u(L)?u(r).backToForm:u(r).showGuide),G(W,"title",u(r).cancel)}),b(I,R)};J(Y,I=>{o.show&&I($)})}b(g,H),uu()}eu(["click"]);function I0(g,o){i(o,!u(o))}function R0(g,o){const c=g.target,h=c.value===""?null:c.value;o.onLensChange(h)}function M0(g,o,c){i(o,null),i(c,!0)}var q0=(g,o,c)=>o(u(c)),G0=(g,o,c)=>o(u(c).id),H0=O('<button class="btn btn-sm btn-outline-secondary lens-action-btn svelte-12keyqc"><i class="bi bi-pencil svelte-12keyqc"></i></button> <button class="btn btn-sm btn-outline-danger lens-action-btn svelte-12keyqc"><i class="bi bi-trash svelte-12keyqc"></i></button>',1),j0=O("<option> </option>"),z0=O('<div class="left-menu-content p-3"><div class="mb-4"><div class="d-flex gap-2 mb-2"><button class="btn btn-sm btn-outline-primary lens-action-btn svelte-12keyqc"><i class="bi bi-plus svelte-12keyqc"></i></button> <!></div> <div class="mb-2"><select id="lensSelect" class="form-select form-select-sm"><option> </option><!></select></div></div></div>'),N0=O('<div><div class="left-menu-header d-flex justify-content-between align-items-center p-3 bg-light svelte-12keyqc"><h6 class="mb-0"> </h6> <button class="btn btn-sm btn-link p-0"><i></i></button></div> <!></div> <!>',1);function Z0(g,o){Y4(o,!0);const c={en:{lens:"Lens",lensing:"Lensing",createLens:"Create lens",editLens:"Edit lens",deleteLens:"Delete lens",noLens:"No lens",before:"Before",from:"From",now:"now",showRead:"Show read",toggleLensing:"Toggle lensing",toggleDateRange:"Toggle date range",confirmDelete:"Are you sure you want to delete this lens?",deleteSuccess:"Lens deleted successfully!",deleteError:"Failed to delete lens"},ru:{lens:"Линза",lensing:"Линзирование",createLens:"Создать линзу",editLens:"Редактировать линзу",deleteLens:"Удалить линзу",noLens:"Без линзы",before:"До",from:"От",now:"сейчас",showRead:"Прочитанное",toggleLensing:"Переключить линзирование",toggleDateRange:"Переключить диапазон дат",confirmDelete:"Вы уверены, что хотите удалить эту линзу?",deleteSuccess:"Линза успешно удалена!",deleteError:"Не удалось удалить линзу"}},{fetcher:h}=g4(),r=X(()=>c[o.locale]);let D=m(!0),L=m(""),x=m(!1),_=m(null);wu(()=>{u(L)||i(L,u(r).now,!0)});function q(A){i(_,A,!0),i(x,!0)}function w(){i(x,!1),i(_,null)}async function k(A){if(confirm(u(r).confirmDelete))try{await h.reactor.lens.delete({id:A}),o.selectedLensId===A&&o.onLensChange(null),o.onLensesUpdated()}catch(j){console.error("Error deleting lens:",j),alert(j instanceof Error?j.message:u(r).deleteError)}}function S(A){o.onLensesUpdated(),A&&o.selectedLensId===A&&o.onLensChange(o.selectedLensId)}var U=N0(),H=s4(U),Y=e(H),$=e(Y),I=e($,!0);t($);var R=a($,2);R.__click=[I0,D];var n4=e(R);t(R),t(Y);var i4=a(Y,2);{var e4=A=>{var j=z0(),K=e(j),W=e(K),z=e(W);z.__click=[M0,_,x];var c4=a(z,2);{var a4=P=>{var E=$4();const d=X(()=>o.lenses.find(Z=>Z.id===o.selectedLensId));var y=s4(E);{var C=Z=>{var Q=H0(),V=s4(Q);V.__click=[q0,q,d];var n=a(V,2);n.__click=[G0,k,d],T(()=>{G(V,"title",u(r).editLens),G(V,"aria-label",u(r).editLens),G(n,"title",u(r).deleteLens),G(n,"aria-label",u(r).deleteLens)}),b(Z,Q)};J(y,Z=>{u(d)&&Z(C)})}b(P,E)};J(c4,P=>{o.selectedLensId&&P(a4)})}t(W);var o4=a(W,2),N=e(o4);N.__change=[R0,o];var u4=e(N),M=e(u4,!0);t(u4),u4.value=u4.__value="";var B=a(u4);ku(B,17,()=>o.lenses,f0,(P,E)=>{var d=j0(),y=e(d,!0);t(d);var C={};T(()=>{s(y,u(E).name),C!==(C=u(E).id)&&(d.value=(d.__value=u(E).id)??"")}),b(P,d)}),t(N);var f;y0(N),t(o4),t(K),t(j),T(()=>{G(z,"title",u(r).createLens),G(z,"aria-label",u(r).createLens),s(M,u(r).noLens),f!==(f=o.selectedLensId||"")&&(N.value=(N.__value=o.selectedLensId||"")??"",C0(N,o.selectedLensId||""))}),b(A,j)};J(i4,A=>{u(D)||A(e4)})}t(H);var r4=a(H,2);U0(r4,{get show(){return u(x)},onClose:w,get locale(){return o.locale},get lens(){return u(_)},onLensUpdated:S}),T(()=>{X4(H,1,`left-menu ${u(D)?"collapsed":""}`,"svelte-12keyqc"),s(I,u(r).lensing),G(R,"aria-label",u(r).toggleLensing),G(R,"title",u(r).toggleLensing),X4(n4,1,`bi bi-chevron-${u(D)?"down":"up"}`)}),b(g,U),uu()}eu(["click","change"]);function Q0(g,o){i(o,!0)}var V0=O('<div class="text-center py-5"><p class="text-muted"> </p></div>'),J0=O('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),K0=O('<div class="text-center py-3"><!></div>'),W0=O('<div class="alert alert-danger" role="alert"> </div>'),X0=O('<div class="row g-4 mt-3"><div class="col-1"></div> <div class="col-2"><!></div> <div class="col-6"><div class="feed svelte-wnb4vd"><!> <!> <!></div></div> <div class="col-2"><div><button class="btn btn-outline-secondary w-100 create-post-btn svelte-wnb4vd"><i class="bi bi-plus-circle me-2"></i> </button></div> <!></div></div> <!>',1);function me(g,o){Y4(o,!0);const c={en:{_page:{title:"Feed — Reactor"},createPost:"Create Post",noPosts:"No posts found",loadingMore:"Loading more posts..."},ru:{_page:{title:"Лента — Реактор"},createPost:"Создать пост",noPosts:"Посты не найдены",loadingMore:"Загружаем больше постов..."}},{fetcher:h}=g4(),r=X(()=>o.data.locale),D=X(()=>o.data.toLocaleHref),L=X(()=>o.data.getAppropriateLocalization),x=X(()=>c[u(r)]);let _=m(K4(o.data.posts)),q=m(K4(o.data.lenses)),w=m(1),k=m(K4(o.data.isHasMorePosts)),S=m(!1),U=m(null),H=m(null),Y=m(!1),$=m(!1),I=m(null);function R(){i($,!1)}function n4(){e4()}async function i4(){if(!(u(S)||!u(k))){i(S,!0),i(U,null);try{const n=u(w)+1,l=await h.reactor.post.list.get({pagination:{page:n},lensId:u(H)});i(_,[...u(_),...l],!0),i(w,n),i(k,l.length===W4.PAGE_SIZE)}catch(n){i(U,n instanceof Error?n.message:"An error occurred while fetching posts",!0),console.error(n)}finally{i(S,!1)}}}async function e4(){i(S,!0),i(U,null),i(w,1);try{const n=await h.reactor.post.list.get({pagination:{page:1},lensId:u(H)});i(_,n,!0),i(k,n.length===W4.PAGE_SIZE)}catch(n){i(U,n instanceof Error?n.message:"An error occurred while fetching posts",!0),console.error(n)}finally{i(S,!1)}}async function r4(){try{i(q,await h.reactor.lens.list.get(),!0)}catch(n){console.error("Error fetching lenses:",n)}}function A(n){i(H,n,!0),e4()}function j(){r4()}_0(()=>{let n;const l=()=>{u(I)&&(n=new IntersectionObserver(F=>{F[0].isIntersecting&&u(k)&&!u(S)&&i4()},{rootMargin:"100px",threshold:.1}),n.observe(u(I)))};return u(I)?l():setTimeout(l,100),()=>{n&&n.disconnect()}});var K=X0();E0(n=>{T(()=>p0.title=u(x)._page.title)});var W=s4(K),z=a(e(W),2),c4=e(z);Z0(c4,{get locale(){return u(r)},get lenses(){return u(q)},get selectedLensId(){return u(H)},onLensChange:A,onLensesUpdated:j}),t(z);var a4=a(z,2),o4=e(a4),N=e(o4);{var u4=n=>{var l=V0(),F=e(l),v=e(F,!0);t(F),t(l),T(()=>s(v,u(x).noPosts)),b(n,l)},M=n=>{var l=$4(),F=s4(l);ku(F,17,()=>u(_),v=>v.id,(v,p)=>{B0(v,{get post(){return u(p)},get locale(){return u(r)},get toLocaleHref(){return u(D)},get getAppropriateLocalization(){return u(L)}})}),b(n,l)};J(N,n=>{u(_).length===0?n(u4):n(M,!1)})}var B=a(N,2);{var f=n=>{var l=K0(),F=e(l);{var v=p=>{var t4=J0(),l4=s4(t4),_4=e(l4),m4=e(_4,!0);t(_4),t(l4);var d4=a(l4,2),v4=e(d4,!0);t(d4),T(()=>{s(m4,u(x).loadingMore),s(v4,u(x).loadingMore)}),b(p,t4)};J(F,p=>{u(S)&&p(v)})}t(l),h0(l,p=>i(I,p),()=>u(I)),b(n,l)};J(B,n=>{u(k)&&n(f)})}var P=a(B,2);{var E=n=>{var l=W0(),F=e(l,!0);t(l),T(()=>s(F,u(U))),b(n,l)};J(P,n=>{u(U)&&n(E)})}t(o4),t(a4);var d=a(a4,2),y=e(d),C=e(y);C.__click=[Q0,$];var Z=a(e(C));t(C),t(y);var Q=a(y,2);x0(Q,{get locale(){return u(r)},get toLocaleHref(){return u(D)},get isExpanded(){return u(Y)},set isExpanded(n){i(Y,n,!0)}}),t(d),t(W);var V=a(W,2);D0(V,{get show(){return u($)},get locale(){return u(r)},get toLocaleHref(){return u(D)},onClose:R,onPostCreated:n4}),T(()=>{X4(y,1,`mb-3 create-post-btn-container ${u(Y)?"with-right-menu-expanded":""}`,"svelte-wnb4vd"),G(C,"aria-label",u(x).createPost),s(Z,` ${u(x).createPost??""}`)}),b(g,K),uu()}eu(["click"]);export{me as component,_e as universal};
