{"version": 3, "file": "_page.svelte-CV4mPmgu.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/users/_id_/feedback/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, x as head, z as escape_html, y as attr, K as ensure_array_like, F as attr_style, J as stringify } from \"../../../../../../../chunks/index.js\";\nimport \"../../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../../chunks/acrpc.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../../chunks/exports.js\";\nimport \"../../../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { g as getUserRateColor } from \"../../../../../../../chunks/get-user-rate-color.js\";\n/* empty css                                                                                 */\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Feedback — Commune\" },\n      userNotFound: \"User not found\",\n      feedbackHistory: \"Feedback History\",\n      sendFeedback: \"Send Feedback\",\n      feedbackModalTitle: \"Send Feedback\",\n      rating: \"Rating\",\n      anonymous: \"Anonymous\",\n      sendAnonymously: \"Send anonymously\",\n      feedback: \"Feedback\",\n      feedbackPlaceholder: \"Enter your feedback...\",\n      cancel: \"Cancel\",\n      submit: \"Submit\",\n      submitting: \"Submitting...\",\n      success: \"Feedback sent successfully\",\n      errorSubmitting: \"Error submitting feedback\",\n      noFeedbacks: \"No feedback found\",\n      loadingMore: \"Loading more...\",\n      errorOccurred: \"An error occurred\",\n      errorFetchingFeedbacks: \"Error fetching feedbacks\",\n      ratingRequired: \"Rating is required\",\n      feedbackRequired: \"Feedback text is required\"\n    },\n    ru: {\n      _page: {\n        title: \"Отзывы — Коммуна\"\n      },\n      userNotFound: \"Пользователь не найден\",\n      feedbackHistory: \"История отзывов\",\n      sendFeedback: \"Отправить отзыв\",\n      feedbackModalTitle: \"Отправить отзыв\",\n      rating: \"Оценка\",\n      anonymous: \"Анонимно\",\n      sendAnonymously: \"Отправить анонимно\",\n      feedback: \"Отзыв\",\n      feedbackPlaceholder: \"Введите ваш отзыв...\",\n      cancel: \"Отмена\",\n      submit: \"Отправить\",\n      submitting: \"Отправка...\",\n      success: \"Отзыв успешно отправлен\",\n      errorSubmitting: \"Ошибка при отправке отзыва\",\n      noFeedbacks: \"Отзывы не найдены\",\n      loadingMore: \"Загрузка...\",\n      errorOccurred: \"Произошла ошибка\",\n      errorFetchingFeedbacks: \"Ошибка загрузки отзывов\",\n      ratingRequired: \"Оценка обязательна\",\n      feedbackRequired: \"Текст отзыва обязателен\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { me, user, locale, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let feedbacks = data.feedbacks;\n  let isHasMoreFeedbacks = data.isHasMoreFeedbacks;\n  const userName = getAppropriateLocalization(user.name);\n  const getAuthorDisplayName = (author) => {\n    if (!author) return t.anonymous;\n    return getAppropriateLocalization(author.name);\n  };\n  const getStarDisplay = (value) => {\n    return \"★\".repeat(Math.floor(value / 2)) + (value % 2 === 1 ? \"☆\" : \"\");\n  };\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(userName)} ${escape_html(t._page.title)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"container py-4\"><div class=\"responsive-container\">`);\n    if (!user) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"alert alert-danger\" role=\"alert\">${escape_html(t.userNotFound)}</div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<div class=\"d-flex justify-content-between align-items-center mb-4\"><div><h2 class=\"mb-1\">${escape_html(userName)}</h2> <p class=\"text-muted mb-0\">${escape_html(t.feedbackHistory)}</p></div> `);\n      if (me.id !== user.id) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div><button class=\"btn btn-primary btn-sm\"${attr(\"aria-label\", t.sendFeedback)}><i class=\"bi bi-chat-dots me-1\"></i> ${escape_html(t.sendFeedback)}</button></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div> `);\n      if (feedbacks.length === 0) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noFeedbacks)}</p></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n        const each_array = ensure_array_like(feedbacks);\n        $$payload2.out.push(`<!--[-->`);\n        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n          let feedback = each_array[$$index];\n          $$payload2.out.push(`<div class=\"card mb-3 shadow-sm svelte-s9if29\"><div class=\"card-body\"><div class=\"d-flex align-items-start\"><div class=\"me-3\">`);\n          if (feedback.author?.image) {\n            $$payload2.out.push(\"<!--[-->\");\n            $$payload2.out.push(`<img${attr(\"src\", `/images/${feedback.author.image}`)}${attr(\"alt\", getAuthorDisplayName(feedback.author))} class=\"rounded-circle\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n          } else {\n            $$payload2.out.push(\"<!--[!-->\");\n            $$payload2.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person-fill\"></i></div>`);\n          }\n          $$payload2.out.push(`<!--]--></div> <div class=\"flex-grow-1\"><div class=\"d-flex justify-content-between align-items-start mb-2\"><div><h6 class=\"mb-1\">${escape_html(getAuthorDisplayName(feedback.author))}</h6></div> <div class=\"d-flex align-items-center gap-2\"><span class=\"badge fs-6\"${attr_style(`color: ${stringify(getUserRateColor(feedback.value))}; border: 1px solid ${stringify(getUserRateColor(feedback.value))};`)}>${escape_html(feedback.value)}/10</span> <span${attr_style(`font-size: 1.2rem; color: ${stringify(getUserRateColor(feedback.value))};`)}>${escape_html(getStarDisplay(feedback.value))}</span></div></div> <p class=\"mb-0 text-muted\">${escape_html(getAppropriateLocalization(feedback.text))}</p></div></div></div></div>`);\n        }\n        $$payload2.out.push(`<!--]-->`);\n      }\n      $$payload2.out.push(`<!--]--> `);\n      if (isHasMoreFeedbacks) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"text-center py-3\">`);\n        {\n          $$payload2.out.push(\"<!--[!-->\");\n        }\n        $$payload2.out.push(`<!--]--></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--> `);\n      {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div>`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;AAC5C,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,kBAAkB,EAAE,eAAe;AACzC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,mBAAmB,EAAE,wBAAwB;AACnD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,eAAe,EAAE,2BAA2B;AAClD,MAAM,WAAW,EAAE,mBAAmB;AACtC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,sBAAsB,EAAE,0BAA0B;AACxD,MAAM,cAAc,EAAE,oBAAoB;AAC1C,MAAM,gBAAgB,EAAE;AACxB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,YAAY,EAAE,wBAAwB;AAC5C,MAAM,eAAe,EAAE,iBAAiB;AACxC,MAAM,YAAY,EAAE,iBAAiB;AACrC,MAAM,kBAAkB,EAAE,iBAAiB;AAC3C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,mBAAmB,EAAE,sBAAsB;AACjD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,eAAe,EAAE,4BAA4B;AACnD,MAAM,WAAW,EAAE,mBAAmB;AACtC,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,aAAa,EAAE,kBAAkB;AACvC,MAAM,sBAAsB,EAAE,yBAAyB;AACvD,MAAM,cAAc,EAAE,oBAAoB;AAC1C,MAAM,gBAAgB,EAAE;AACxB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,0BAA0B,EAAE,GAAG,IAAI;AAC/D,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;AAChC,EAAE,IAAI,kBAAkB,GAAG,IAAI,CAAC,kBAAkB;AAClD,EAAE,MAAM,QAAQ,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD,EAAE,MAAM,oBAAoB,GAAG,CAAC,MAAM,KAAK;AAC3C,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,SAAS;AACnC,IAAI,OAAO,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC;AAClD,EAAE,CAAC;AACH,EAAE,MAAM,cAAc,GAAG,CAAC,KAAK,KAAK;AACpC,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3E,EAAE,CAAC;AACH,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAChG,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,CAAC,CAAC;AACzF,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9G,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0FAA0F,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,CAAC;AAC5N,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;AAC7B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2CAA2C,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC;AAClM,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC5C,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AAC1H,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAQ,MAAM,UAAU,GAAG,iBAAiB,CAAC,SAAS,CAAC;AACvD,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC3F,UAAU,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;AAC5C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8HAA8H,CAAC,CAAC;AAC/J,UAAU,IAAI,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE;AACtC,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,+EAA+E,CAAC,CAAC;AAC7N,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAClN,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iIAAiI,EAAE,WAAW,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iFAAiF,EAAE,UAAU,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB,EAAE,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,0BAA0B,EAAE,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,+CAA+C,EAAE,WAAW,CAAC,0BAA0B,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC;AAC7uB,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACtC,MAAM,IAAI,kBAAkB,EAAE;AAC9B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC7D,QAAQ;AACR,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACtC,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACzC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}