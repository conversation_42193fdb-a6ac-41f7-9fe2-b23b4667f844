import{e as f}from"../chunks/CVTn1FV4.js";import{g as h}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{p as y,f as _,t as g,b as x,c as k,d as a,s as p,r as e,aQ as w}from"../chunks/RHWQbow4.js";import{s as A}from"../chunks/BlWcudmi.js";import{s as P}from"../chunks/CkTdM00m.js";const j=async({fetch:t,url:s})=>{const{fetcher:i}=h(),[l]=await Promise.all([i.user.me.get({fetch:t,ctx:{url:s}})]);if(l.role!=="admin")throw f(403,"Access denied: Admin privileges required");return{me:l}},M=Object.freeze(Object.defineProperty({__proto__:null,load:j},Symbol.toStringTag,{value:"Module"}));var O=_('<div class="admin-layout svelte-e2baey"><div class="container-fluid"><div class="row"><nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar svelte-e2baey"><div class="position-sticky pt-3"><div class="sidebar-header mb-3 svelte-e2baey"><h5 class="text-primary"><i class="bi bi-gear-fill me-2"></i> Admin Panel</h5> <small class="text-muted"> </small></div> <ul class="nav flex-column"><li class="nav-item"><a class="nav-link svelte-e2baey" href="/admin" data-sveltekit-preload-data="hover"><i class="bi bi-house-door me-2"></i> Dashboard</a></li> <li class="nav-item"><a class="nav-link svelte-e2baey" href="/admin/invites" data-sveltekit-preload-data="hover"><i class="bi bi-envelope me-2"></i> User Invites</a></li></ul></div></nav> <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 svelte-e2baey"><div class="pt-3"><!></div></main></div></div></div>');function Q(t,s){y(s,!0);var i=O(),l=a(i),o=a(l),r=a(o),d=a(r),v=a(d),m=p(a(v),2),b=a(m);e(m),e(v),w(2),e(d),e(r);var n=p(r,2),c=a(n),u=a(c);P(u,()=>s.children),e(c),e(n),e(o),e(l),e(i),g(()=>A(b,`Welcome, ${s.data.me.email??""}`)),x(t,i),k()}export{Q as component,M as universal};
