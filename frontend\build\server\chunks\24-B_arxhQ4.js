const index = 24;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-_RlYfjYS.js')).default;
const imports = ["_app/immutable/nodes/24.CrDgNEHg.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/Cx19LsLk.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CSZ3sDel.js"];
const stylesheets = ["_app/immutable/assets/24.B75xpDc4.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=24-B_arxhQ4.js.map
