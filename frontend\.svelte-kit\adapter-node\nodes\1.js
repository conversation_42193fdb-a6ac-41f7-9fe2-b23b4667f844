

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.CST5FILT.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/Cx19LsLk.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/CYgJF_JY.js"];
export const stylesheets = [];
export const fonts = [];
