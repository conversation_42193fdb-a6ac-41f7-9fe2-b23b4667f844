import { PrismaService } from "src/prisma/prisma.service";
import { Common } from "@commune/api";
type CreateDto = {
    votesRequired: number;
    endsAt: Date;
    title: Common.Localization[];
    description: Common.Localization[];
    options: {
        title: Common.Localization[];
    }[];
};
export declare class VotingService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: CreateDto): Promise<{
        title: {
            id: string;
            value: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            locale: import("@prisma/client").$Enums.Locale;
            key: string;
        }[];
        description: {
            id: string;
            value: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            locale: import("@prisma/client").$Enums.Locale;
            key: string;
        }[];
        options: ({
            title: {
                id: string;
                value: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                locale: import("@prisma/client").$Enums.Locale;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            votingId: string;
            deletedAt: Date | null;
        })[];
    } & {
        id: string;
        votesRequired: number;
        endsAt: Date;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
}
export {};
