{"version": 3, "file": "_page.svelte-_RlYfjYS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/auth/_page.svelte.js"], "sourcesContent": ["import { F as attr_style, G as attr_class, z as escape_html, y as attr, w as pop, u as push } from \"../../../../chunks/index.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../chunks/exports.js\";\nimport \"../../../../chunks/state.svelte.js\";\nimport \"../../../../chunks/client2.js\";\nimport \"../../../../chunks/current-user.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { g as getClient } from \"../../../../chunks/acrpc.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const { fetcher: api } = getClient();\n  const i18n = {\n    en: {\n      _page: { title: \"Auth — Commune\" },\n      authType: { login: \"Login\", register: \"Register\" },\n      login: \"Log In\",\n      register: \"Create Account\",\n      email: \"Email\",\n      otp: {\n        short: \"OTP\",\n        long: \"Email Verification Code\",\n        send: \"Send OTP\",\n        enterThe6DigitCodeSentToYourEmail: \"Enter the 6-digit code sent to your email.\",\n        sent: \"OTP Sent\",\n        sendingDisabledByServer: \"OTP sending is disabled by the server\",\n        placeholder: \"6-digit code\"\n      },\n      failedToSubmitPleaseTryAgain: \"Failed to submit. Please try again.\",\n      invite: {\n        required: \"Invitation Required\",\n        notInvited: \"You are not invited yet, write to one of our chats.\",\n        telegram: \"Telegram\",\n        telegramLink: \"https://t.me/ds_commune_en\"\n      }\n    },\n    ru: {\n      _page: {\n        title: \"Вход — Коммуна\"\n      },\n      authType: {\n        login: \"Вход\",\n        register: \"Регистрация\"\n      },\n      login: \"Войти\",\n      register: \"Создать аккаунт\",\n      email: \"Email\",\n      otp: {\n        short: \"OTP\",\n        long: \"Код проверки почты\",\n        send: \"Отправить OTP\",\n        enterThe6DigitCodeSentToYourEmail: \"Введите 6-значный код, отправленный на ваш email.\",\n        sent: \"OTP отправлен\",\n        sendingDisabledByServer: \"Отправка OTP отключена сервером\",\n        placeholder: \"Шестизначный код\"\n      },\n      failedToSubmitPleaseTryAgain: \"Не удалось отправить. Пожалуйста, попробуйте снова.\",\n      invite: {\n        required: \"Требуется приглашение\",\n        notInvited: \"Вы ещё не приглашены, напишите в один из наших чатов.\",\n        telegram: \"Telegram\",\n        telegramLink: \"https://t.me/ds_commune_ru\"\n      }\n    }\n  };\n  const { data } = $$props;\n  const { locale } = data;\n  const t = i18n[locale];\n  var OtpStatus = /* @__PURE__ */ ((OtpStatus2) => {\n    OtpStatus2[\"None\"] = \"none\";\n    OtpStatus2[\"Pending\"] = \"pending\";\n    OtpStatus2[\"Sent\"] = \"sent\";\n    OtpStatus2[\"SendingDisabledByServer\"] = \"sending-disabled-by-server\";\n    OtpStatus2[\"Error\"] = \"error\";\n    return OtpStatus2;\n  })(OtpStatus || {});\n  var SubmitStatus = /* @__PURE__ */ ((SubmitStatus2) => {\n    SubmitStatus2[\"None\"] = \"none\";\n    SubmitStatus2[\"Pending\"] = \"pending\";\n    SubmitStatus2[\"Error\"] = \"error\";\n    return SubmitStatus2;\n  })(SubmitStatus || {});\n  let email = \"\";\n  let otp = \"\";\n  let otpStatus = \"none\";\n  let submitStatus = \"none\";\n  let submitErrorMessage = null;\n  $$payload.out.push(`<div class=\"container min-vh-100 d-flex align-items-center justify-content-center\"><div class=\"card shadow-lg border-0\"${attr_style(\"\", { width: \"100%\", \"max-width\": \"400px\" })}><div class=\"card-header bg-white border-0 pt-4 pb-0\"><div class=\"position-relative\"><ul class=\"nav nav-tabs border-0 card-header-tabs\"><li class=\"nav-item flex-grow-1 text-center\"><button${attr_class(`nav-link border-0 w-100 ${\"active\"}`, \"svelte-il0jyn\")}>${escape_html(t.authType.login)}</button></li> <li class=\"nav-item flex-grow-1 text-center\"><button${attr_class(`nav-link border-0 w-100 ${\"\"}`, \"svelte-il0jyn\")}>${escape_html(t.authType.register)}</button></li></ul> <div class=\"position-absolute bottom-0 bg-primary\"${attr_style(\"\", {\n    height: \"3px\",\n    width: \"50%\",\n    left: \"0\",\n    transition: \"left 0.3s ease-in-out\",\n    borderRadius: \"3px 3px 0 0\"\n  })}></div></div></div> <div class=\"card-body p-4\"><form><div class=\"mb-3\"><label for=\"email\" class=\"form-label\">${escape_html(t.email)}</label> <input type=\"email\" autoComplete=\"email\" class=\"form-control\" id=\"email\"${attr(\"value\", email)} placeholder=\"<EMAIL>\" required/></div> <div class=\"mb-3\"><button type=\"button\" class=\"btn btn-outline-primary w-100\"${attr(\"disabled\", !email, true)}>`);\n  if (otpStatus === OtpStatus.Pending) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> ${escape_html(t.otp.send)}</button></div> <div class=\"mb-3\"><div class=\"d-flex justify-content-between align-items-center mb-2\"><label for=\"otp\" class=\"form-label mb-0\">${escape_html(t.otp.long)}</label> `);\n  if (otpStatus === OtpStatus.Sent) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span class=\"badge bg-success-subtle text-success px-2 py-1 rounded-pill\">${escape_html(t.otp.sent)}</span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    if (otpStatus === OtpStatus.SendingDisabledByServer) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"badge bg-warning-subtle text-warning px-2 py-1 rounded-pill\">${escape_html(t.otp.sendingDisabledByServer)}</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></div> <input id=\"otp\" class=\"form-control\" type=\"text\"${attr(\"value\", otp)}${attr(\"placeholder\", t.otp.placeholder)}${attr(\"maxlength\", 6)} aria-describedby=\"otpHelp\"/> <div id=\"otpHelp\" class=\"form-text\">${escape_html(t.otp.enterThe6DigitCodeSentToYourEmail)}</div></div> <div class=\"d-grid gap-2\"><button type=\"submit\" class=\"btn btn-primary\"${attr(\"disabled\", !email, true)}>`);\n  if (submitStatus === SubmitStatus.Pending && otp) ;\n  else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`${escape_html(t.login)}`);\n  }\n  $$payload.out.push(`<!--]--></button> `);\n  if (submitStatus === SubmitStatus.Error) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span class=\"text-danger\">${escape_html(t.failedToSubmitPleaseTryAgain)} <br/> ${escape_html(submitErrorMessage)}</span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></form> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE;AACxC,MAAM,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE;AACxD,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE,yBAAyB;AACvC,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,iCAAiC,EAAE,4CAA4C;AACvF,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,uBAAuB,EAAE,uCAAuC;AACxE,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,4BAA4B,EAAE,qCAAqC;AACzE,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,qBAAqB;AACvC,QAAQ,UAAU,EAAE,qDAAqD;AACzE,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,YAAY,EAAE;AACtB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE,oBAAoB;AAClC,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,iCAAiC,EAAE,mDAAmD;AAC9F,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,uBAAuB,EAAE,iCAAiC;AAClE,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,4BAA4B,EAAE,qDAAqD;AACzF,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,uBAAuB;AACzC,QAAQ,UAAU,EAAE,uDAAuD;AAC3E,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,YAAY,EAAE;AACtB;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;AACzB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,SAAS,mBAAmB,CAAC,CAAC,UAAU,KAAK;AACnD,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;AAC/B,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;AACrC,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;AAC/B,IAAI,UAAU,CAAC,yBAAyB,CAAC,GAAG,4BAA4B;AACxE,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;AACjC,IAAI,OAAO,UAAU;AACrB,EAAE,CAAC,EAAE,SAAS,IAAI,EAAE,CAAC;AACrB,EAAE,IAAI,YAAY,mBAAmB,CAAC,CAAC,aAAa,KAAK;AACzD,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;AAClC,IAAI,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS;AACxC,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO;AACpC,IAAI,OAAO,aAAa;AACxB,EAAE,CAAC,EAAE,YAAY,IAAI,EAAE,CAAC;AACxB,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,GAAG,GAAG,EAAE;AACd,EAAE,IAAI,SAAS,GAAG,MAAM;AACxB,EAAE,IAAI,YAAY,GAAG,MAAM;AAC3B,EAAE,IAAI,kBAAkB,GAAG,IAAI;AAC/B,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uHAAuH,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC,4LAA4L,EAAE,UAAU,CAAC,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,mEAAmE,EAAE,UAAU,CAAC,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,sEAAsE,EAAE,UAAU,CAAC,EAAE,EAAE;AACtuB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,IAAI,EAAE,GAAG;AACb,IAAI,UAAU,EAAE,uBAAuB;AACvC,IAAI,YAAY,EAAE;AAClB,GAAG,CAAC,CAAC,6GAA6G,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,iFAAiF,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,8HAA8H,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnZ,EAAE,IAAI,SAAS,KAAK,SAAS,CAAC,OAAO,EAAE;AACvC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4FAA4F,CAAC,CAAC;AACtH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+IAA+I,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC7O,EAAE,IAAI,SAAS,KAAK,SAAS,CAAC,IAAI,EAAE;AACpC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AACrI,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,IAAI,SAAS,KAAK,SAAS,CAAC,uBAAuB,EAAE;AACzD,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,CAAC;AAC1J,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+DAA+D,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,kEAAkE,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC,oFAAoF,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3Z,EACO;AACP,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjD,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAAE,IAAI,YAAY,KAAK,YAAY,CAAC,KAAK,EAAE;AAC3C,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC;AAClJ,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC9C,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAClD,EAAE,GAAG,EAAE;AACP;;;;"}