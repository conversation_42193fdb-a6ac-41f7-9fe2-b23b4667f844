import"../chunks/Bzak7iHL.js";import{o as z}from"../chunks/DeAm3Eed.js";import{p as A,av as B,aw as E,f as k,s as a,a as w,b as d,c as F,ax as G,d as e,g as r,r as t,ay as H,t as J}from"../chunks/RHWQbow4.js";import{d as K,s as u}from"../chunks/BlWcudmi.js";import{e as T,i as D}from"../chunks/Dnfvvefi.js";import{g as C}from"../chunks/CSZ3sDel.js";async function N(v,s){await C().fetcher.tag.post({name:[{locale:"en",value:"test "+Date.now()},{locale:"ru",value:"тест "+Date.now()}]}),s()}var O=k("<tr><td> </td><td> </td><td> </td></tr>"),P=k("<div>Test!!</div> <button>Create new tag</button> <h2>Tags:</h2> <table><thead><tr><th>ID</th><th>Locale</th><th>Value</th></tr></thead><tbody></tbody></table>",1);function Y(v,s){A(s,!0);let n=B(E([]));async function m(){const o=await C().fetcher.tag.list.get({});G(n,o,!0)}z(()=>{m()});var f=P(),h=a(w(f),2);h.__click=[N,m];var p=a(h,4),g=a(e(p));T(g,21,()=>r(n),D,(_,o)=>{var b=H(),I=w(b);T(I,17,()=>r(o).name,D,(L,x,M)=>{var c=O(),i=e(c),V=e(i,!0);t(i);var l=a(i),j=e(l,!0);t(l);var y=a(l),q=e(y,!0);t(y),t(c),J(()=>{u(V,M===0?r(o).id:""),u(j,r(x).locale),u(q,r(x).value)}),d(L,c)}),d(_,b)}),t(g),t(p),d(v,f),F()}K(["click"]);export{Y as component};
