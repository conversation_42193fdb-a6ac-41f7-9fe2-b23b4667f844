import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const users = await api.user.list.get({}, { fetch, ctx: { url } });
  return {
    users,
    isHasMoreUsers: users.length === consts_exports.PAGE_SIZE
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 20;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-tnQKWWfX.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/users/+page.ts";
const imports = ["_app/immutable/nodes/20.Cngeoin8.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/B5DcI8qy.js"];
const stylesheets = ["_app/immutable/assets/20.CPoQ0XrN.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=20-CVr22--2.js.map
