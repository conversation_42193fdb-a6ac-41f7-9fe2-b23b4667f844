import { z } from "zod";
import { ConfigService as NestConfigService } from "@nestjs/config";
import { OnModuleInit } from "@nestjs/common";
export type Config = Normalize<z.infer<typeof ConfigSchema>>;
export declare const ConfigSchema: z.ZodObject<{
    instance: z.ZodObject<{
        name: z.ZodString;
        emailDomain: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
        emailDomain: string;
    }, {
        name: string;
        emailDomain: string;
    }>;
    auth: z.ZodObject<{
        disableRegisterOtpCheck: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableLoginOtpCheck: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        otpExpirationTimeMs: z.ZodNumber;
    }, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
        disableRegisterOtpCheck: boolean;
        disableLoginOtpCheck: boolean;
        otpExpirationTimeMs: number;
    }, {
        otpExpirationTimeMs: number;
        disableRegisterOtpCheck?: string | undefined;
        disableLoginOtpCheck?: string | undefined;
    }>;
    minio: z.ZodObject<{
        endpoint: z.ZodString;
        port: z.ZodNumber;
        accessKey: z.ZodString;
        secretKey: z.ZodString;
        useSSL: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
    }, "strip", z.ZodTypeAny, {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL: boolean;
    }, {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL?: string | undefined;
    }>;
    email: z.ZodObject<{
        host: z.ZodString;
        port: z.ZodNumber;
        user: z.ZodString;
        pass: z.ZodString;
        disableAllEmails: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableOtpEmails: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableInviteEmails: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        ignoreErrors: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        rejectUnauthorized: z.ZodOptional<z.ZodBoolean>;
        otpEmailSender: z.ZodString;
        inviteEmailSender: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        user: string;
        port: number;
        host: string;
        pass: string;
        disableAllEmails: boolean;
        disableOtpEmails: boolean;
        disableInviteEmails: boolean;
        ignoreErrors: boolean;
        otpEmailSender: string;
        inviteEmailSender: string;
        rejectUnauthorized?: boolean | undefined;
    }, {
        user: string;
        port: number;
        host: string;
        pass: string;
        otpEmailSender: string;
        inviteEmailSender: string;
        disableAllEmails?: string | undefined;
        disableOtpEmails?: string | undefined;
        disableInviteEmails?: string | undefined;
        ignoreErrors?: string | undefined;
        rejectUnauthorized?: boolean | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    auth: {
        disableRegisterOtpCheck: boolean;
        disableLoginOtpCheck: boolean;
        otpExpirationTimeMs: number;
    };
    instance: {
        name: string;
        emailDomain: string;
    };
    minio: {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL: boolean;
    };
    email: {
        user: string;
        port: number;
        host: string;
        pass: string;
        disableAllEmails: boolean;
        disableOtpEmails: boolean;
        disableInviteEmails: boolean;
        ignoreErrors: boolean;
        otpEmailSender: string;
        inviteEmailSender: string;
        rejectUnauthorized?: boolean | undefined;
    };
}, {
    auth: {
        otpExpirationTimeMs: number;
        disableRegisterOtpCheck?: string | undefined;
        disableLoginOtpCheck?: string | undefined;
    };
    instance: {
        name: string;
        emailDomain: string;
    };
    minio: {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL?: string | undefined;
    };
    email: {
        user: string;
        port: number;
        host: string;
        pass: string;
        otpEmailSender: string;
        inviteEmailSender: string;
        disableAllEmails?: string | undefined;
        disableOtpEmails?: string | undefined;
        disableInviteEmails?: string | undefined;
        ignoreErrors?: string | undefined;
        rejectUnauthorized?: boolean | undefined;
    };
}>;
export declare class ConfigService implements OnModuleInit {
    private readonly configService;
    config: DeepReadonly<Config>;
    constructor(configService: NestConfigService);
    onModuleInit(): void;
}
