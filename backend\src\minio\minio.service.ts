import path from "node:path";
import { Client } from "minio";
import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { ConfigService } from "src/config/config.service";

export interface FileInfo {
    originalname: string;
    buffer: Buffer;
    mimetype: string;
    size: number;
}

export const minioImageEntities = [
    "commune",
    "user",
    "reactor-hub",
    "reactor-community",
] as const;

export type MinioImageEntity = (typeof minioImageEntities)[number];

@Injectable()
export class MinioService implements OnModuleInit {
    private client: Client;
    private readonly logger = new Logger(MinioService.name);
    private readonly buckets = {
        ...minioImageEntities.reduce(
            (acc, entity) => {
                acc[entity] = entity;
                return acc;
            },
            {} as Record<MinioImageEntity, string>,
        ),

        "reactor-post": "reactor-post",
    };

    constructor(private readonly configService: ConfigService) {}

    onModuleInit() {
        this.client = new Client({
            endPoint: this.configService.config.minio.endpoint,
            port: this.configService.config.minio.port,
            useSSL: this.configService.config.minio.useSSL,
            accessKey: this.configService.config.minio.accessKey,
            secretKey: this.configService.config.minio.secretKey,
        });

        this.initializeBuckets();
    }

    private async initializeBuckets(): Promise<void> {
        await Promise.all(
            Object.values(this.buckets).map(async (bucket) => {
                const exists = await this.client.bucketExists(bucket);

                if (!exists) {
                    await this.client.makeBucket(bucket, "us-east-1");

                    this.logger.log(`Bucket '${bucket}' created successfully`);

                    // Set bucket policy to allow public read access
                    const policy = {
                        Version: "2012-10-17",
                        Statement: [
                            {
                                Effect: "Allow",
                                Principal: { AWS: ["*"] },
                                Action: ["s3:GetObject"],
                                Resource: [`arn:aws:s3:::${bucket}/*`],
                            },
                        ],
                    };

                    await this.client.setBucketPolicy(
                        bucket,
                        JSON.stringify(policy),
                    );

                    this.logger.log(
                        `Public read policy set for bucket '${bucket}'`,
                    );
                }
            }),
        ).catch((error) => {
            this.logger.error("Failed to initialize MinIO buckets", error);
        });
    }

    getImageObjectName(entityId: string, index?: number) {
        return `${entityId}${index !== undefined ? `.${index}` : ""}`;
    }

    async uploadImage(
        file: FileInfo,
        entity: MinioImageEntity,
        entityId: string,
        index?: number,
    ) {
        const bucket = this.buckets[entity];
        const objectName = this.getImageObjectName(entityId, index);

        if (!bucket) {
            throw new Error(`There's no bucket for entity: ${entity}`);
        }

        return await this.uploadFile(file, bucket, objectName);
    }

    async uploadPostImage(id: string, file: FileInfo) {
        const bucket = this.buckets["reactor-post"];
        const objectName = `${id}${path.extname(file.originalname)}`;

        return await this.uploadFile(file, bucket, objectName);
    }

    /**
     * @returns url
     */
    async uploadFile(
        file: FileInfo,
        bucket: string,
        objectName: string,
    ): Promise<string> {
        try {
            await this.client.putObject(
                bucket,
                objectName,
                file.buffer,
                undefined,
                {
                    "Content-Type": file.mimetype,
                },
            );

            return `${bucket}/${objectName}`;
        } catch (error) {
            this.logger.error(
                `Failed to upload file ${objectName} to bucket ${bucket}`,
                error,
            );

            throw error;
        }
    }

    async deleteImage(
        entity: MinioImageEntity,
        entityId: string,
        index?: number,
    ) {
        const bucket = this.buckets[entity];
        const objectName = this.getImageObjectName(entityId, index);

        return await this.deleteFile(bucket, objectName);
    }

    async deleteFile(bucket: string, objectName: string): Promise<void> {
        try {
            await this.client.removeObject(bucket, objectName);
        } catch (error) {
            throw new Error(
                `Failed to delete file ${objectName} from bucket ${bucket}`,
                { cause: error },
            );
        }
    }
}
