import { g as getClient } from './acrpc-nBPcN0GL.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';
import './current-user-BM0W6LNm.js';

const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const me = await api.user.me.get({ fetch, ctx: { url } });
  return { me };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 17;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DCuryJZz.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/profile/+page.ts";
const imports = ["_app/immutable/nodes/17.CHdhlmr2.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/CL12WlkV.js","_app/immutable/chunks/CBe4EX5h.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/BiLRrsV0.js"];
const stylesheets = ["_app/immutable/assets/create-post-modal.BRelZfpq.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=17-CS5bWQPO.js.map
