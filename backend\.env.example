# Database Configuration
DATABASE_URL = ""

# MinIO Configuration
MINIO_ENDPOINT = "localhost"
MINIO_PORT = "9000"
MINIO_ACCESS_KEY = "admin"
MINIO_SECRET_KEY = "password"
MINIO_USE_SSL = "false"

# Other Configuration

# Instance Configuration
INSTANCE_NAME = "Commune Dev (local)"
INSTANCE_EMAIL_DOMAIN = "dev.commune.my"

# Session Configuration
SESSION_SECRET = "session_secret_very_long_key_change_in_production"

# Email Configuration
EMAIL_HOST = "mail.dev.commune.my"
EMAIL_PORT = "587"
EMAIL_USER = "<EMAIL>"
EMAIL_PASSWORD = "hLE-mP6-qY9-vSU"
# EMAIL_REJECT_UNAUTHORIZED = ""

OTP_EMAIL_SENDER = "otp"
OTP_EXPIRATION_TIME_MS = "600000"

INVITE_EMAIL_SENDER = "invite"

# Dev Mode Configuration
IGNORE_EMAIL_ERRORS = "1"
DISABLE_OTP_EMAIL = "1"
DISABLE_LOGIN_OTP_CHECK = "1"
DISABLE_REGISTER_OTP_CHECK = "1"
DISABLE_ALL_EMAILS
DISABLE_OTP_EMAILS
DISABLE_INVITE_EMAILS
