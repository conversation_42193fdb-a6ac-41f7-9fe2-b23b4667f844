import{s as se}from"./DeAm3Eed.js";import{H as at,S as yt,R as wt}from"./CYgJF_JY.js";import{c as ie,H as N,N as $,r as nt,i as vt,b as T,s as rt,p as I,w as ce,n as dt,f as Mt,g as pt,a as z,d as ft,S as bt,P as Vt,e as jt,o as Et,h as le,m as fe,j as ue,k as he,l as qt,q as B,t as de,u as pe,v as Kt,x as ge}from"./DiZKRWcx.js";import{z as me}from"./RHWQbow4.js";const _e=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function ye(t){const n=[];return{pattern:t==="/"?/^\/$/:new RegExp(`^${ve(t).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return n.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const o=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(o)return n.push({name:o[1],matcher:o[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const i=r.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,f)=>{if(f%2){if(c.startsWith("x+"))return ut(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return ut(String.fromCharCode(...c.slice(2).split("-").map(h=>parseInt(h,16))));const p=_e.exec(c),[,u,_,l,m]=p;return n.push({name:l,matcher:m,optional:!!u,rest:!!_,chained:_?f===1&&i[0]==="":!1}),_?"(.*?)":u?"([^/]*)?":"([^/]+?)"}return ut(c)}).join("")}).join("")}/?$`),params:n}}function we(t){return t!==""&&!/^\([^)]+\)$/.test(t)}function ve(t){return t.slice(1).split("/").filter(we)}function be(t,n,e){const r={},a=t.slice(1),o=a.filter(s=>s!==void 0);let i=0;for(let s=0;s<n.length;s+=1){const c=n[s];let f=a[s-i];if(c.chained&&c.rest&&i&&(f=a.slice(s-i,s+1).filter(p=>p).join("/"),i=0),f===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||e[c.matcher](f)){r[c.name]=f;const p=n[s+1],u=a[s+1];p&&!p.rest&&p.optional&&u&&c.chained&&(i=0),!p&&!u&&Object.keys(r).length===o.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return r}function ut(t){return t.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Ee({nodes:t,server_loads:n,dictionary:e,matchers:r}){const a=new Set(n);return Object.entries(e).map(([s,[c,f,p]])=>{const{pattern:u,params:_}=ye(s),l={id:s,exec:m=>{const h=u.exec(m);if(h)return be(h,_,r)},errors:[1,...p||[]].map(m=>t[m]),layouts:[0,...f||[]].map(i),leaf:o(c)};return l.errors.length=l.layouts.length=Math.max(l.errors.length,l.layouts.length),l});function o(s){const c=s<0;return c&&(s=~s),[c,t[s]]}function i(s){return s===void 0?s:[a.has(s),t[s]]}}function Gt(t,n=JSON.parse){try{return n(sessionStorage[t])}catch{}}function Nt(t,n,e=JSON.stringify){const r=e(n);try{sessionStorage[t]=r}catch{}}function Ot(t){const n=Se(t),e=new ArrayBuffer(n.length),r=new DataView(e);for(let a=0;a<e.byteLength;a++)r.setUint8(a,n.charCodeAt(a));return e}const ke="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Se(t){t.length%4===0&&(t=t.replace(/==?$/,""));let n="",e=0,r=0;for(let a=0;a<t.length;a++)e<<=6,e|=ke.indexOf(t[a]),r+=6,r===24&&(n+=String.fromCharCode((e&16711680)>>16),n+=String.fromCharCode((e&65280)>>8),n+=String.fromCharCode(e&255),e=r=0);return r===12?(e>>=4,n+=String.fromCharCode(e)):r===18&&(e>>=2,n+=String.fromCharCode((e&65280)>>8),n+=String.fromCharCode(e&255)),n}const Ae=-1,Re=-2,Ie=-3,Le=-4,Ue=-5,Te=-6;function Pe(t,n){if(typeof t=="number")return a(t,!0);if(!Array.isArray(t)||t.length===0)throw new Error("Invalid input");const e=t,r=Array(e.length);function a(o,i=!1){if(o===Ae)return;if(o===Ie)return NaN;if(o===Le)return 1/0;if(o===Ue)return-1/0;if(o===Te)return-0;if(i)throw new Error("Invalid input");if(o in r)return r[o];const s=e[o];if(!s||typeof s!="object")r[o]=s;else if(Array.isArray(s))if(typeof s[0]=="string"){const c=s[0],f=n==null?void 0:n[c];if(f)return r[o]=f(a(s[1]));switch(c){case"Date":r[o]=new Date(s[1]);break;case"Set":const p=new Set;r[o]=p;for(let l=1;l<s.length;l+=1)p.add(a(s[l]));break;case"Map":const u=new Map;r[o]=u;for(let l=1;l<s.length;l+=2)u.set(a(s[l]),a(s[l+1]));break;case"RegExp":r[o]=new RegExp(s[1],s[2]);break;case"Object":r[o]=Object(s[1]);break;case"BigInt":r[o]=BigInt(s[1]);break;case"null":const _=Object.create(null);r[o]=_;for(let l=1;l<s.length;l+=2)_[s[l]]=a(s[l+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const l=globalThis[c],m=s[1],h=Ot(m),d=new l(h);r[o]=d;break}case"ArrayBuffer":{const l=s[1],m=Ot(l);r[o]=m;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(s.length);r[o]=c;for(let f=0;f<s.length;f+=1){const p=s[f];p!==Re&&(c[f]=a(p))}}else{const c={};r[o]=c;for(const f in s){const p=s[f];c[f]=a(p)}}return r[o]}return a(0)}function Ce(t){return t.filter(n=>n!=null)}const xe="x-sveltekit-invalidated",je="x-sveltekit-trailing-slash";function W(t){return t instanceof at||t instanceof yt?t.status:500}function Ne(t){return t instanceof yt?t.text:"Internal Error"}const Oe="/__data.json",De=".html__data.json";function $e(t){return t.endsWith(".html")?t.replace(/\.html$/,De):t.replace(/\/$/,"")+Oe}const{tick:Fe}=se,Be=me??(t=>t()),He=new Set(["icon","shortcut icon","apple-touch-icon"]),O=Gt(Kt)??{},K=Gt(qt)??{},C={url:jt({}),page:jt({}),navigating:ce(null),updated:ie()};function kt(t){O[t]=rt()}function Me(t,n){let e=t+1;for(;O[e];)delete O[e],e+=1;for(e=n+1;K[e];)delete K[e],e+=1}function F(t){return location.href=t.href,new Promise(()=>{})}async function Yt(){if("serviceWorker"in navigator){const t=await navigator.serviceWorker.getRegistration(T||"/");t&&await t.update()}}function Dt(){}let St,gt,X,P,mt,E;const J=[],Z=[];let U=null;const Y=new Map,zt=new Set,Ve=new Set,M=new Set;let v={branch:[],error:null,url:null},At=!1,Q=!1,$t=!0,G=!1,H=!1,Wt=!1,Rt=!1,It,S,L,j;const V=new Set;async function sa(t,n,e){var o,i,s,c;document.URL!==location.href&&(location.href=location.href),E=t,await((i=(o=t.hooks).init)==null?void 0:i.call(o)),St=Ee(t),P=document.documentElement,mt=n,gt=t.nodes[0],X=t.nodes[1],gt(),X(),S=(s=history.state)==null?void 0:s[N],L=(c=history.state)==null?void 0:c[$],S||(S=L=Date.now(),history.replaceState({...history.state,[N]:S,[$]:L},""));const r=O[S];function a(){r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y))}e?(a(),await Qe(mt,e)):(await q({type:"enter",url:nt(E.hash?ea(new URL(location.href)):location.href),replace_state:!0}),a()),Ze()}function qe(){J.length=0,Rt=!1}function Xt(t){Z.some(n=>n==null?void 0:n.snapshot)&&(K[t]=Z.map(n=>{var e;return(e=n==null?void 0:n.snapshot)==null?void 0:e.capture()}))}function Jt(t){var n;(n=K[t])==null||n.forEach((e,r)=>{var a,o;(o=(a=Z[r])==null?void 0:a.snapshot)==null||o.restore(e)})}function Ft(){kt(S),Nt(Kt,O),Xt(L),Nt(qt,K)}async function Lt(t,n,e,r){return q({type:"goto",url:nt(t),keepfocus:n.keepFocus,noscroll:n.noScroll,replace_state:n.replaceState,state:n.state,redirect_count:e,nav_token:r,accept:()=>{n.invalidateAll&&(Rt=!0),n.invalidate&&n.invalidate.forEach(Je)}})}async function Ke(t){if(t.id!==(U==null?void 0:U.id)){const n={};V.add(n),U={id:t.id,token:n,promise:te({...t,preload:n}).then(e=>(V.delete(n),e.type==="loaded"&&e.state.error&&(U=null),e))}}return U.promise}async function ht(t){var e;const n=(e=await st(t,!1))==null?void 0:e.route;n&&await Promise.all([...n.layouts,n.leaf].map(r=>r==null?void 0:r[1]()))}function Zt(t,n,e){var a;v=t.state;const r=document.querySelector("style[data-sveltekit]");if(r&&r.remove(),Object.assign(I,t.props.page),It=new E.root({target:n,props:{...t.props,stores:C,components:Z},hydrate:e,sync:!1}),Jt(L),e){const o={from:null,to:{params:v.params,route:{id:((a=v.route)==null?void 0:a.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};M.forEach(i=>i(o))}Q=!0}function tt({url:t,params:n,branch:e,status:r,error:a,route:o,form:i}){let s="never";if(T&&(t.pathname===T||t.pathname===T+"/"))s="always";else for(const l of e)(l==null?void 0:l.slash)!==void 0&&(s=l.slash);t.pathname=ue(t.pathname,s),t.search=t.search;const c={type:"loaded",state:{url:t,params:n,branch:e,error:a,route:o},props:{constructors:Ce(e).map(l=>l.node.component),page:it(I)}};i!==void 0&&(c.props.form=i);let f={},p=!I,u=0;for(let l=0;l<Math.max(e.length,v.branch.length);l+=1){const m=e[l],h=v.branch[l];(m==null?void 0:m.data)!==(h==null?void 0:h.data)&&(p=!0),m&&(f={...f,...m.data},p&&(c.props[`data_${u}`]=f),u+=1)}return(!v.url||t.href!==v.url.href||v.error!==a||i!==void 0&&i!==I.form||p)&&(c.props.page={error:a,params:n,route:{id:(o==null?void 0:o.id)??null},state:{},status:r,url:new URL(t),form:i??null,data:p?f:I.data}),c}async function Ut({loader:t,parent:n,url:e,params:r,route:a,server_data_node:o}){var p,u,_;let i=null,s=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},f=await t();if((p=f.universal)!=null&&p.load){let l=function(...h){for(const d of h){const{href:w}=new URL(d,e);c.dependencies.add(w)}};const m={route:new Proxy(a,{get:(h,d)=>(s&&(c.route=!0),h[d])}),params:new Proxy(r,{get:(h,d)=>(s&&c.params.add(d),h[d])}),data:(o==null?void 0:o.data)??null,url:fe(e,()=>{s&&(c.url=!0)},h=>{s&&c.search_params.add(h)},E.hash),async fetch(h,d){h instanceof Request&&(d={body:h.method==="GET"||h.method==="HEAD"?void 0:await h.blob(),cache:h.cache,credentials:h.credentials,headers:[...h.headers].length>0?h==null?void 0:h.headers:void 0,integrity:h.integrity,keepalive:h.keepalive,method:h.method,mode:h.mode,redirect:h.redirect,referrer:h.referrer,referrerPolicy:h.referrerPolicy,signal:h.signal,...d});const{resolved:w,promise:A}=Qt(h,d,e);return s&&l(w.href),A},setHeaders:()=>{},depends:l,parent(){return s&&(c.parent=!0),n()},untrack(h){s=!1;try{return h()}finally{s=!0}}};i=await f.universal.load.call(null,m)??null}return{node:f,loader:t,server:o,universal:(u=f.universal)!=null&&u.load?{type:"data",data:i,uses:c}:null,data:i??(o==null?void 0:o.data)??null,slash:((_=f.universal)==null?void 0:_.trailingSlash)??(o==null?void 0:o.slash)}}function Qt(t,n,e){let r=t instanceof Request?t.url:t;const a=new URL(r,e);a.origin===e.origin&&(r=a.href.slice(e.origin.length));const o=Q?de(r,a.href,n):pe(r,n);return{resolved:a,promise:o}}function Bt(t,n,e,r,a,o){if(Rt)return!0;if(!a)return!1;if(a.parent&&t||a.route&&n||a.url&&e)return!0;for(const i of a.search_params)if(r.has(i))return!0;for(const i of a.params)if(o[i]!==v.params[i])return!0;for(const i of a.dependencies)if(J.some(s=>s(new URL(i))))return!0;return!1}function Tt(t,n){return(t==null?void 0:t.type)==="data"?t:(t==null?void 0:t.type)==="skip"?n??null:null}function Ge(t,n){if(!t)return new Set(n.searchParams.keys());const e=new Set([...t.searchParams.keys(),...n.searchParams.keys()]);for(const r of e){const a=t.searchParams.getAll(r),o=n.searchParams.getAll(r);a.every(i=>o.includes(i))&&o.every(i=>a.includes(i))&&e.delete(r)}return e}function Ht({error:t,url:n,route:e,params:r}){return{type:"loaded",state:{error:t,url:n,route:e,params:r,branch:[]},props:{page:it(I),constructors:[]}}}async function te({id:t,invalidating:n,url:e,params:r,route:a,preload:o}){if((U==null?void 0:U.id)===t)return V.delete(U.token),U.promise;const{errors:i,layouts:s,leaf:c}=a,f=[...s,c];i.forEach(g=>g==null?void 0:g().catch(()=>{})),f.forEach(g=>g==null?void 0:g[1]().catch(()=>{}));let p=null;const u=v.url?t!==et(v.url):!1,_=v.route?a.id!==v.route.id:!1,l=Ge(v.url,e);let m=!1;const h=f.map((g,y)=>{var x;const b=v.branch[y],k=!!(g!=null&&g[0])&&((b==null?void 0:b.loader)!==g[1]||Bt(m,_,u,l,(x=b.server)==null?void 0:x.uses,r));return k&&(m=!0),k});if(h.some(Boolean)){try{p=await ne(e,h)}catch(g){const y=await D(g,{url:e,params:r,route:{id:t}});return V.has(o)?Ht({error:y,url:e,params:r,route:a}):ot({status:W(g),error:y,url:e,route:a})}if(p.type==="redirect")return p}const d=p==null?void 0:p.nodes;let w=!1;const A=f.map(async(g,y)=>{var ct;if(!g)return;const b=v.branch[y],k=d==null?void 0:d[y];if((!k||k.type==="skip")&&g[1]===(b==null?void 0:b.loader)&&!Bt(w,_,u,l,(ct=b.universal)==null?void 0:ct.uses,r))return b;if(w=!0,(k==null?void 0:k.type)==="error")throw k;return Ut({loader:g[1],url:e,params:r,route:a,parent:async()=>{var xt;const Ct={};for(let lt=0;lt<y;lt+=1)Object.assign(Ct,(xt=await A[lt])==null?void 0:xt.data);return Ct},server_data_node:Tt(k===void 0&&g[0]?{type:"skip"}:k??null,g[0]?b==null?void 0:b.server:void 0)})});for(const g of A)g.catch(()=>{});const R=[];for(let g=0;g<f.length;g+=1)if(f[g])try{R.push(await A[g])}catch(y){if(y instanceof wt)return{type:"redirect",location:y.location};if(V.has(o))return Ht({error:await D(y,{params:r,url:e,route:{id:a.id}}),url:e,params:r,route:a});let b=W(y),k;if(d!=null&&d.includes(y))b=y.status??b,k=y.error;else if(y instanceof at)k=y.body;else{if(await C.updated.check())return await Yt(),await F(e);k=await D(y,{params:r,url:e,route:{id:a.id}})}const x=await Ye(g,R,i);return x?tt({url:e,params:r,branch:R.slice(0,x.idx).concat(x.node),status:b,error:k,route:a}):await ae(e,{id:a.id},k,b)}else R.push(void 0);return tt({url:e,params:r,branch:R,status:200,error:null,route:a,form:n?void 0:null})}async function Ye(t,n,e){for(;t--;)if(e[t]){let r=t;for(;!n[r];)r-=1;try{return{idx:r+1,node:{node:await e[t](),loader:e[t],data:{},server:null,universal:null}}}catch{continue}}}async function ot({status:t,error:n,url:e,route:r}){const a={};let o=null;if(E.server_loads[0]===0)try{const s=await ne(e,[!0]);if(s.type!=="data"||s.nodes[0]&&s.nodes[0].type!=="data")throw 0;o=s.nodes[0]??null}catch{(e.origin!==Et||e.pathname!==location.pathname||At)&&await F(e)}try{const s=await Ut({loader:gt,url:e,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:Tt(o)}),c={node:await X(),loader:X,universal:null,server:null,data:null};return tt({url:e,params:a,branch:[s,c],status:t,error:n,route:null})}catch(s){if(s instanceof wt)return Lt(new URL(s.location,location.href),{},0);throw s}}async function ze(t){const n=t.href;if(Y.has(n))return Y.get(n);let e;try{const r=(async()=>{let a=await E.hooks.reroute({url:new URL(t),fetch:async(o,i)=>Qt(o,i,t).promise})??t;if(typeof a=="string"){const o=new URL(t);E.hash?o.hash=a:o.pathname=a,a=o}return a})();Y.set(n,r),e=await r}catch{Y.delete(n);return}return e}async function st(t,n){if(t&&!vt(t,T,E.hash)){const e=await ze(t);if(!e)return;const r=We(e);for(const a of St){const o=a.exec(r);if(o)return{id:et(t),invalidating:n,route:a,params:le(o),url:t}}}}function We(t){return he(E.hash?t.hash.replace(/^#/,"").replace(/[?#].+/,""):t.pathname.slice(T.length))||"/"}function et(t){return(E.hash?t.hash.replace(/^#/,""):t.pathname)+t.search}function ee({url:t,type:n,intent:e,delta:r}){let a=!1;const o=Pt(v,e,t,n);r!==void 0&&(o.navigation.delta=r);const i={...o.navigation,cancel:()=>{a=!0,o.reject(new Error("navigation cancelled"))}};return G||zt.forEach(s=>s(i)),a?null:o}async function q({type:t,url:n,popped:e,keepfocus:r,noscroll:a,replace_state:o,state:i={},redirect_count:s=0,nav_token:c={},accept:f=Dt,block:p=Dt}){const u=j;j=c;const _=await st(n,!1),l=t==="enter"?Pt(v,_,n,t):ee({url:n,type:t,delta:e==null?void 0:e.delta,intent:_});if(!l){p(),j===c&&(j=u);return}const m=S,h=L;f(),G=!0,Q&&l.navigation.type!=="enter"&&C.navigating.set(dt.current=l.navigation);let d=_&&await te(_);if(!d){if(vt(n,T,E.hash))return await F(n);d=await ae(n,{id:null},await D(new yt(404,"Not Found",`Not found: ${n.pathname}`),{url:n,params:{},route:{id:null}}),404)}if(n=(_==null?void 0:_.url)||n,j!==c)return l.reject(new Error("navigation aborted")),!1;if(d.type==="redirect")if(s>=20)d=await ot({status:500,error:await D(new Error("Redirect loop"),{url:n,params:{},route:{id:null}}),url:n,route:{id:null}});else return await Lt(new URL(d.location,n).href,{},s+1,c),!1;else d.props.page.status>=400&&await C.updated.check()&&(await Yt(),await F(n));if(qe(),kt(m),Xt(h),d.props.page.url.pathname!==n.pathname&&(n.pathname=d.props.page.url.pathname),i=e?e.state:i,!e){const g=o?0:1,y={[N]:S+=g,[$]:L+=g,[bt]:i};(o?history.replaceState:history.pushState).call(history,y,"",n),o||Me(S,L)}if(U=null,d.props.page.state=i,Q){const g=(await Promise.all(Array.from(Ve,y=>y(l.navigation)))).filter(y=>typeof y=="function");if(g.length>0){let y=function(){g.forEach(b=>{M.delete(b)})};g.push(y),g.forEach(b=>{M.add(b)})}v=d.state,d.props.page&&(d.props.page.url=n),It.$set(d.props),ge(d.props.page),Wt=!0}else Zt(d,mt,!1);const{activeElement:w}=document;await Fe();const A=e?e.scroll:a?rt():null;if($t){const g=n.hash&&document.getElementById(oe(n));A?scrollTo(A.x,A.y):g?g.scrollIntoView():scrollTo(0,0)}const R=document.activeElement!==w&&document.activeElement!==document.body;!r&&!R&&ta(n),$t=!0,d.props.page&&Object.assign(I,d.props.page),G=!1,t==="popstate"&&Jt(L),l.fulfil(void 0),M.forEach(g=>g(l.navigation)),C.navigating.set(dt.current=null)}async function ae(t,n,e,r){return t.origin===Et&&t.pathname===location.pathname&&!At?await ot({status:r,error:e,url:t,route:n}):await F(t)}function Xe(){let t,n,e;P.addEventListener("mousemove",s=>{const c=s.target;clearTimeout(t),t=setTimeout(()=>{o(c,B.hover)},20)});function r(s){s.defaultPrevented||o(s.composedPath()[0],B.tap)}P.addEventListener("mousedown",r),P.addEventListener("touchstart",r,{passive:!0});const a=new IntersectionObserver(s=>{for(const c of s)c.isIntersecting&&(ht(new URL(c.target.href)),a.unobserve(c.target))},{threshold:0});async function o(s,c){const f=Mt(s,P),p=f===n&&c>=e;if(!f||p)return;const{url:u,external:_,download:l}=pt(f,T,E.hash);if(_||l)return;const m=z(f),h=u&&et(v.url)===et(u);if(!(m.reload||h))if(c<=m.preload_data){n=f,e=B.tap;const d=await st(u,!1);if(!d)return;Ke(d)}else c<=m.preload_code&&(n=f,e=c,ht(u))}function i(){a.disconnect();for(const s of P.querySelectorAll("a")){const{url:c,external:f,download:p}=pt(s,T,E.hash);if(f||p)continue;const u=z(s);u.reload||(u.preload_code===B.viewport&&a.observe(s),u.preload_code===B.eager&&ht(c))}}M.add(i),i()}function D(t,n){if(t instanceof at)return t.body;const e=W(t),r=Ne(t);return E.hooks.handleError({error:t,event:n,status:e,message:r})??{message:r}}function ia(t,n={}){return t=new URL(nt(t)),t.origin!==Et?Promise.reject(new Error("goto: invalid URL")):Lt(t,n,0)}function Je(t){if(typeof t=="function")J.push(t);else{const{href:n}=new URL(t,location.href);J.push(e=>e.href===n)}}function ca(t,n){const e={[N]:S,[$]:L,[Vt]:I.url.href,[bt]:n};history.replaceState(e,"",nt(t)),I.state=n,It.$set({page:Be(()=>it(I))})}function Ze(){var n;history.scrollRestoration="manual",addEventListener("beforeunload",e=>{let r=!1;if(Ft(),!G){const a=Pt(v,void 0,null,"leave"),o={...a.navigation,cancel:()=>{r=!0,a.reject(new Error("navigation cancelled"))}};zt.forEach(i=>i(o))}r?(e.preventDefault(),e.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&Ft()}),(n=navigator.connection)!=null&&n.saveData||Xe(),P.addEventListener("click",async e=>{if(e.button||e.which!==1||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.defaultPrevented)return;const r=Mt(e.composedPath()[0],P);if(!r)return;const{url:a,external:o,target:i,download:s}=pt(r,T,E.hash);if(!a)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=z(r);if(!(r instanceof SVGAElement)&&a.protocol!==location.protocol&&!(a.protocol==="https:"||a.protocol==="http:")||s)return;const[p,u]=(E.hash?a.hash.replace(/^#/,""):a.href).split("#"),_=p===ft(location);if(o||c.reload&&(!_||!u)){ee({url:a,type:"link"})?G=!0:e.preventDefault();return}if(u!==void 0&&_){const[,l]=v.url.href.split("#");if(l===u){if(e.preventDefault(),u===""||u==="top"&&r.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const m=r.ownerDocument.getElementById(decodeURIComponent(u));m&&(m.scrollIntoView(),m.focus())}return}if(H=!0,kt(S),t(a),!c.replace_state)return;H=!1}e.preventDefault(),await new Promise(l=>{requestAnimationFrame(()=>{setTimeout(l,0)}),setTimeout(l,100)}),await q({type:"link",url:a,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??a.href===location.href})}),P.addEventListener("submit",e=>{if(e.defaultPrevented)return;const r=HTMLFormElement.prototype.cloneNode.call(e.target),a=e.submitter;if(((a==null?void 0:a.formTarget)||r.target)==="_blank"||((a==null?void 0:a.formMethod)||r.method)!=="get")return;const s=new URL((a==null?void 0:a.hasAttribute("formaction"))&&(a==null?void 0:a.formAction)||r.action);if(vt(s,T,!1))return;const c=e.target,f=z(c);if(f.reload)return;e.preventDefault(),e.stopPropagation();const p=new FormData(c),u=a==null?void 0:a.getAttribute("name");u&&p.append(u,(a==null?void 0:a.getAttribute("value"))??""),s.search=new URLSearchParams(p).toString(),q({type:"form",url:s,keepfocus:f.keepfocus,noscroll:f.noscroll,replace_state:f.replace_state??s.href===location.href})}),addEventListener("popstate",async e=>{var r;if(!_t){if((r=e.state)!=null&&r[N]){const a=e.state[N];if(j={},a===S)return;const o=O[a],i=e.state[bt]??{},s=new URL(e.state[Vt]??location.href),c=e.state[$],f=v.url?ft(location)===ft(v.url):!1;if(c===L&&(Wt||f)){i!==I.state&&(I.state=i),t(s),O[S]=rt(),o&&scrollTo(o.x,o.y),S=a;return}const u=a-S;await q({type:"popstate",url:s,popped:{state:i,scroll:o,delta:u},accept:()=>{S=a,L=c},block:()=>{history.go(-u)},nav_token:j})}else if(!H){const a=new URL(location.href);t(a),E.hash&&location.reload()}}}),addEventListener("hashchange",()=>{H&&(H=!1,history.replaceState({...history.state,[N]:++S,[$]:L},"",location.href))});for(const e of document.querySelectorAll("link"))He.has(e.rel)&&(e.href=e.href);addEventListener("pageshow",e=>{e.persisted&&C.navigating.set(dt.current=null)});function t(e){v.url=I.url=e,C.page.set(it(I)),C.page.notify()}}async function Qe(t,{status:n=200,error:e,node_ids:r,params:a,route:o,server_route:i,data:s,form:c}){At=!0;const f=new URL(location.href);let p;({params:a={},route:o={id:null}}=await st(f,!1)||{}),p=St.find(({id:l})=>l===o.id);let u,_=!0;try{const l=r.map(async(h,d)=>{const w=s[d];return w!=null&&w.uses&&(w.uses=re(w.uses)),Ut({loader:E.nodes[h],url:f,params:a,route:o,parent:async()=>{const A={};for(let R=0;R<d;R+=1)Object.assign(A,(await l[R]).data);return A},server_data_node:Tt(w)})}),m=await Promise.all(l);if(p){const h=p.layouts;for(let d=0;d<h.length;d++)h[d]||m.splice(d,0,void 0)}u=tt({url:f,params:a,branch:m,status:n,error:e,form:c,route:p??null})}catch(l){if(l instanceof wt){await F(new URL(l.location,location.href));return}u=await ot({status:W(l),error:await D(l,{url:f,params:a,route:o}),url:f,route:o}),t.textContent="",_=!1}u.props.page&&(u.props.page.state={}),Zt(u,t,_)}async function ne(t,n){var o;const e=new URL(t);e.pathname=$e(t.pathname),t.pathname.endsWith("/")&&e.searchParams.append(je,"1"),e.searchParams.append(xe,n.map(i=>i?"1":"0").join(""));const r=window.fetch,a=await r(e.href,{});if(!a.ok){let i;throw(o=a.headers.get("content-type"))!=null&&o.includes("application/json")?i=await a.json():a.status===404?i="Not Found":a.status===500&&(i="Internal Error"),new at(a.status,i)}return new Promise(async i=>{var _;const s=new Map,c=a.body.getReader(),f=new TextDecoder;function p(l){return Pe(l,{...E.decoders,Promise:m=>new Promise((h,d)=>{s.set(m,{fulfil:h,reject:d})})})}let u="";for(;;){const{done:l,value:m}=await c.read();if(l&&!u)break;for(u+=!m&&u?`
`:f.decode(m,{stream:!0});;){const h=u.indexOf(`
`);if(h===-1)break;const d=JSON.parse(u.slice(0,h));if(u=u.slice(h+1),d.type==="redirect")return i(d);if(d.type==="data")(_=d.nodes)==null||_.forEach(w=>{(w==null?void 0:w.type)==="data"&&(w.uses=re(w.uses),w.data=p(w.data))}),i(d);else if(d.type==="chunk"){const{id:w,data:A,error:R}=d,g=s.get(w);s.delete(w),R?g.reject(p(R)):g.fulfil(p(A))}}}})}function re(t){return{dependencies:new Set((t==null?void 0:t.dependencies)??[]),params:new Set((t==null?void 0:t.params)??[]),parent:!!(t!=null&&t.parent),route:!!(t!=null&&t.route),url:!!(t!=null&&t.url),search_params:new Set((t==null?void 0:t.search_params)??[])}}let _t=!1;function ta(t){const n=document.querySelector("[autofocus]");if(n)n.focus();else{const e=oe(t);if(e&&document.getElementById(e)){const{x:a,y:o}=rt();setTimeout(()=>{const i=history.state;_t=!0,location.replace(`#${e}`),E.hash&&location.replace(t.hash),history.replaceState(i,"",t.hash),scrollTo(a,o),_t=!1})}else{const a=document.body,o=a.getAttribute("tabindex");a.tabIndex=-1,a.focus({preventScroll:!0,focusVisible:!1}),o!==null?a.setAttribute("tabindex",o):a.removeAttribute("tabindex")}const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let o=0;o<r.rangeCount;o+=1)a.push(r.getRangeAt(o));setTimeout(()=>{if(r.rangeCount===a.length){for(let o=0;o<r.rangeCount;o+=1){const i=a[o],s=r.getRangeAt(o);if(i.commonAncestorContainer!==s.commonAncestorContainer||i.startContainer!==s.startContainer||i.endContainer!==s.endContainer||i.startOffset!==s.startOffset||i.endOffset!==s.endOffset)return}r.removeAllRanges()}})}}}function Pt(t,n,e,r){var c,f;let a,o;const i=new Promise((p,u)=>{a=p,o=u});return i.catch(()=>{}),{navigation:{from:{params:t.params,route:{id:((c=t.route)==null?void 0:c.id)??null},url:t.url},to:e&&{params:(n==null?void 0:n.params)??null,route:{id:((f=n==null?void 0:n.route)==null?void 0:f.id)??null},url:e},willUnload:!n,type:r,complete:i},fulfil:a,reject:o}}function it(t){return{data:t.data,error:t.error,form:t.form,params:t.params,route:t.route,state:t.state,status:t.status,url:t.url}}function ea(t){const n=new URL(t);return n.hash=decodeURIComponent(t.hash),n}function oe(t){let n;if(E.hash){const[,,e]=t.hash.split("#",3);n=e??""}else n=t.hash.slice(1);return decodeURIComponent(n)}export{sa as a,ia as g,ca as r,C as s};
