var Dt=Object.defineProperty;var At=(t,e,n)=>e in t?Dt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var v=(t,e,n)=>At(t,typeof e!="symbol"?e+"":e,n);import{Z as ot,u as m,a as c,t as K,b as i,d as E,f as S,g as N,r as Nt,h as _t}from"./CVTn1FV4.js";class jt{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,n){this.keyToValue.set(e,n),this.valueToKey.set(n,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class ht{constructor(e){this.generateIdentifier=e,this.kv=new jt}register(e,n){this.kv.getByValue(e)||(n||(n=this.generateIdentifier(e)),this.kv.set(n,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class kt extends ht{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,n){typeof n=="object"?(n.allowProps&&this.classToAllowedProps.set(e,n.allowProps),super.register(e,n.identifier)):super.register(e,n)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function Mt(t){if("values"in Object)return Object.values(t);const e=[];for(const n in t)t.hasOwnProperty(n)&&e.push(t[n]);return e}function Gt(t,e){const n=Mt(t);if("find"in n)return n.find(e);const r=n;for(let a=0;a<r.length;a++){const s=r[a];if(e(s))return s}}function k(t,e){Object.entries(t).forEach(([n,r])=>e(r,n))}function F(t,e){return t.indexOf(e)!==-1}function it(t,e){for(let n=0;n<t.length;n++){const r=t[n];if(e(r))return r}}class Lt{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return Gt(this.transfomers,n=>n.isApplicable(e))}findByName(e){return this.transfomers[e]}}const zt=t=>Object.prototype.toString.call(t).slice(8,-1),ft=t=>typeof t>"u",Wt=t=>t===null,W=t=>typeof t!="object"||t===null||t===Object.prototype?!1:Object.getPrototypeOf(t)===null?!0:Object.getPrototypeOf(t)===Object.prototype,X=t=>W(t)&&Object.keys(t).length===0,R=t=>Array.isArray(t),Vt=t=>typeof t=="string",xt=t=>typeof t=="number"&&!isNaN(t),Bt=t=>typeof t=="boolean",qt=t=>t instanceof RegExp,V=t=>t instanceof Map,x=t=>t instanceof Set,mt=t=>zt(t)==="Symbol",Kt=t=>t instanceof Date&&!isNaN(t.valueOf()),Ft=t=>t instanceof Error,ut=t=>typeof t=="number"&&isNaN(t),Ht=t=>Bt(t)||Wt(t)||ft(t)||xt(t)||Vt(t)||mt(t),$t=t=>typeof t=="bigint",Zt=t=>t===1/0||t===-1/0,Jt=t=>ArrayBuffer.isView(t)&&!(t instanceof DataView),Yt=t=>t instanceof URL,dt=t=>t.replace(/\./g,"\\."),Q=t=>t.map(String).map(dt).join("."),z=t=>{const e=[];let n="";for(let a=0;a<t.length;a++){let s=t.charAt(a);if(s==="\\"&&t.charAt(a+1)==="."){n+=".",a++;continue}if(s==="."){e.push(n),n="";continue}n+=s}const r=n;return e.push(r),e};function b(t,e,n,r){return{isApplicable:t,annotation:e,transform:n,untransform:r}}const St=[b(ft,"undefined",()=>null,()=>{}),b($t,"bigint",t=>t.toString(),t=>typeof BigInt<"u"?BigInt(t):(console.error("Please add a BigInt polyfill."),t)),b(Kt,"Date",t=>t.toISOString(),t=>new Date(t)),b(Ft,"Error",(t,e)=>{const n={name:t.name,message:t.message};return e.allowedErrorProps.forEach(r=>{n[r]=t[r]}),n},(t,e)=>{const n=new Error(t.message);return n.name=t.name,n.stack=t.stack,e.allowedErrorProps.forEach(r=>{n[r]=t[r]}),n}),b(qt,"regexp",t=>""+t,t=>{const e=t.slice(1,t.lastIndexOf("/")),n=t.slice(t.lastIndexOf("/")+1);return new RegExp(e,n)}),b(x,"set",t=>[...t.values()],t=>new Set(t)),b(V,"map",t=>[...t.entries()],t=>new Map(t)),b(t=>ut(t)||Zt(t),"number",t=>ut(t)?"NaN":t>0?"Infinity":"-Infinity",Number),b(t=>t===0&&1/t===-1/0,"number",()=>"-0",Number),b(Yt,"URL",t=>t.toString(),t=>new URL(t))];function H(t,e,n,r){return{isApplicable:t,annotation:e,transform:n,untransform:r}}const gt=H((t,e)=>mt(t)?!!e.symbolRegistry.getIdentifier(t):!1,(t,e)=>["symbol",e.symbolRegistry.getIdentifier(t)],t=>t.description,(t,e,n)=>{const r=n.symbolRegistry.getValue(e[1]);if(!r)throw new Error("Trying to deserialize unknown symbol");return r}),Qt=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((t,e)=>(t[e.name]=e,t),{}),It=H(Jt,t=>["typed-array",t.constructor.name],t=>[...t],(t,e)=>{const n=Qt[e[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(t)});function yt(t,e){return t!=null&&t.constructor?!!e.classRegistry.getIdentifier(t.constructor):!1}const vt=H(yt,(t,e)=>["class",e.classRegistry.getIdentifier(t.constructor)],(t,e)=>{const n=e.classRegistry.getAllowedProps(t.constructor);if(!n)return{...t};const r={};return n.forEach(a=>{r[a]=t[a]}),r},(t,e,n)=>{const r=n.classRegistry.getValue(e[1]);if(!r)throw new Error(`Trying to deserialize unknown class '${e[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(r.prototype),t)}),bt=H((t,e)=>!!e.customTransformerRegistry.findApplicable(t),(t,e)=>["custom",e.customTransformerRegistry.findApplicable(t).name],(t,e)=>e.customTransformerRegistry.findApplicable(t).serialize(t),(t,e,n)=>{const r=n.customTransformerRegistry.findByName(e[1]);if(!r)throw new Error("Trying to deserialize unknown custom value");return r.deserialize(t)}),Xt=[vt,gt,bt,It],ct=(t,e)=>{const n=it(Xt,a=>a.isApplicable(t,e));if(n)return{value:n.transform(t,e),type:n.annotation(t,e)};const r=it(St,a=>a.isApplicable(t,e));if(r)return{value:r.transform(t,e),type:r.annotation}},wt={};St.forEach(t=>{wt[t.annotation]=t});const te=(t,e,n)=>{if(R(e))switch(e[0]){case"symbol":return gt.untransform(t,e,n);case"class":return vt.untransform(t,e,n);case"custom":return bt.untransform(t,e,n);case"typed-array":return It.untransform(t,e,n);default:throw new Error("Unknown transformation: "+e)}else{const r=wt[e];if(!r)throw new Error("Unknown transformation: "+e);return r.untransform(t,n)}},_=(t,e)=>{if(e>t.size)throw new Error("index out of bounds");const n=t.keys();for(;e>0;)n.next(),e--;return n.next().value};function Ct(t){if(F(t,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(F(t,"prototype"))throw new Error("prototype is not allowed as a property");if(F(t,"constructor"))throw new Error("constructor is not allowed as a property")}const ee=(t,e)=>{Ct(e);for(let n=0;n<e.length;n++){const r=e[n];if(x(t))t=_(t,+r);else if(V(t)){const a=+r,s=+e[++n]==0?"key":"value",o=_(t,a);switch(s){case"key":t=o;break;case"value":t=t.get(o);break}}else t=t[r]}return t},tt=(t,e,n)=>{if(Ct(e),e.length===0)return n(t);let r=t;for(let s=0;s<e.length-1;s++){const o=e[s];if(R(r)){const l=+o;r=r[l]}else if(W(r))r=r[o];else if(x(r)){const l=+o;r=_(r,l)}else if(V(r)){if(s===e.length-2)break;const p=+o,w=+e[++s]==0?"key":"value",I=_(r,p);switch(w){case"key":r=I;break;case"value":r=r.get(I);break}}}const a=e[e.length-1];if(R(r)?r[+a]=n(r[+a]):W(r)&&(r[a]=n(r[a])),x(r)){const s=_(r,+a),o=n(s);s!==o&&(r.delete(s),r.add(o))}if(V(r)){const s=+e[e.length-2],o=_(r,s);switch(+a==0?"key":"value"){case"key":{const p=n(o);r.set(p,r.get(o)),p!==o&&r.delete(o);break}case"value":{r.set(o,n(r.get(o)));break}}}return t};function et(t,e,n=[]){if(!t)return;if(!R(t)){k(t,(s,o)=>et(s,e,[...n,...z(o)]));return}const[r,a]=t;a&&k(a,(s,o)=>{et(s,e,[...n,...z(o)])}),e(r,n)}function ne(t,e,n){return et(e,(r,a)=>{t=tt(t,a,s=>te(s,r,n))}),t}function re(t,e){function n(r,a){const s=ee(t,z(a));r.map(z).forEach(o=>{t=tt(t,o,()=>s)})}if(R(e)){const[r,a]=e;r.forEach(s=>{t=tt(t,z(s),()=>t)}),a&&k(a,n)}else k(e,n);return t}const ae=(t,e)=>W(t)||R(t)||V(t)||x(t)||yt(t,e);function se(t,e,n){const r=n.get(t);r?r.push(e):n.set(t,[e])}function oe(t,e){const n={};let r;return t.forEach(a=>{if(a.length<=1)return;e||(a=a.map(l=>l.map(String)).sort((l,p)=>l.length-p.length));const[s,...o]=a;s.length===0?r=o.map(Q):n[Q(s)]=o.map(Q)}),r?X(n)?[r]:[r,n]:X(n)?void 0:n}const Ot=(t,e,n,r,a=[],s=[],o=new Map)=>{const l=Ht(t);if(!l){se(t,a,e);const y=o.get(t);if(y)return r?{transformedValue:null}:y}if(!ae(t,n)){const y=ct(t,n),d=y?{transformedValue:y.value,annotations:[y.type]}:{transformedValue:t};return l||o.set(t,d),d}if(F(s,t))return{transformedValue:null};const p=ct(t,n),w=(p==null?void 0:p.value)??t,I=R(w)?[]:{},O={};k(w,(y,d)=>{if(d==="__proto__"||d==="constructor"||d==="prototype")throw new Error(`Detected property ${d}. This is a prototype pollution risk, please remove it from your object.`);const C=Ot(y,e,n,r,[...a,d],[...s,t],o);I[d]=C.transformedValue,R(C.annotations)?O[d]=C.annotations:W(C.annotations)&&k(C.annotations,(M,g)=>{O[dt(d)+"."+g]=M})});const B=X(O)?{transformedValue:I,annotations:p?[p.type]:void 0}:{transformedValue:I,annotations:p?[p.type,O]:O};return l||o.set(t,B),B};function Et(t){return Object.prototype.toString.call(t).slice(8,-1)}function lt(t){return Et(t)==="Array"}function ie(t){if(Et(t)!=="Object")return!1;const e=Object.getPrototypeOf(t);return!!e&&e.constructor===Object&&e===Object.prototype}function ue(t,e,n,r,a){const s={}.propertyIsEnumerable.call(r,e)?"enumerable":"nonenumerable";s==="enumerable"&&(t[e]=n),a&&s==="nonenumerable"&&Object.defineProperty(t,e,{value:n,enumerable:!1,writable:!0,configurable:!0})}function nt(t,e={}){if(lt(t))return t.map(a=>nt(a,e));if(!ie(t))return t;const n=Object.getOwnPropertyNames(t),r=Object.getOwnPropertySymbols(t);return[...n,...r].reduce((a,s)=>{if(lt(e.props)&&!e.props.includes(s))return a;const o=t[s],l=nt(o,e);return ue(a,s,l,t,e.nonenumerable),a},{})}class u{constructor({dedupe:e=!1}={}){this.classRegistry=new kt,this.symbolRegistry=new ht(n=>n.description??""),this.customTransformerRegistry=new Lt,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const n=new Map,r=Ot(e,n,this,this.dedupe),a={json:r.transformedValue};r.annotations&&(a.meta={...a.meta,values:r.annotations});const s=oe(n,this.dedupe);return s&&(a.meta={...a.meta,referentialEqualities:s}),a}deserialize(e){const{json:n,meta:r}=e;let a=nt(n);return r!=null&&r.values&&(a=ne(a,r.values,this)),r!=null&&r.referentialEqualities&&(a=re(a,r.referentialEqualities)),a}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,n){this.classRegistry.register(e,n)}registerSymbol(e,n){this.symbolRegistry.register(e,n)}registerCustom(e,n){this.customTransformerRegistry.register({name:n,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}u.defaultInstance=new u;u.serialize=u.defaultInstance.serialize.bind(u.defaultInstance);u.deserialize=u.defaultInstance.deserialize.bind(u.defaultInstance);u.stringify=u.defaultInstance.stringify.bind(u.defaultInstance);u.parse=u.defaultInstance.parse.bind(u.defaultInstance);u.registerClass=u.defaultInstance.registerClass.bind(u.defaultInstance);u.registerSymbol=u.defaultInstance.registerSymbol.bind(u.defaultInstance);u.registerCustom=u.defaultInstance.registerCustom.bind(u.defaultInstance);u.allowErrorProps=u.defaultInstance.allowErrorProps.bind(u.defaultInstance);function ce(t){var e;return((e=t[0])==null?void 0:e.toUpperCase())+t.slice(1).toLowerCase()}function le(t,e){const n=(e==null?void 0:e.split(""))??[],r=t.map(a=>typeof a=="string"?a:(n.push(...a.flags.split("")),String(a).slice(1,-a.flags.length-1)));return new RegExp(r.join("|"),[...new Set(n)].join(""))}var pe=/[^\p{L}\p{N}]/,he=new RegExp("(?<=\\p{Lu})(?=\\p{Lu}\\p{Ll})"),fe=new RegExp("(?<=\\p{Ll})(?=\\p{Lu})"),me=new RegExp("(?<=\\p{L})(?=\\p{N})"),de=new RegExp("(?<=\\p{N})(?=\\p{L})"),P,Se=(P=class{constructor(...e){v(this,"preserveCase");v(this,"preserveCaps");v(this,"capitalizeAfter");typeof e[0]=="boolean"?(this.preserveCase=!0,this.preserveCaps=!0,this.capitalizeAfter=Number.POSITIVE_INFINITY):(this.preserveCase=!1,this.preserveCaps=e[1],this.capitalizeAfter=e[0])}parse(e){return e.replace(P.parser," ").trim().split(/\s+/)}join(e){const n=Math.max(0,this.capitalizeAfter),r=e.slice(0,n),a=e.slice(n),s=this.preserveCase?a:a.map(o=>!this.preserveCaps||new RegExp("\\p{Ll}","u").test(o)?ce(o):o);return[...r,...s].join("")}},v(P,"parser",le([he,fe,me,de],"gu")),P),ge=class{constructor(t,e){this.separator=t,this.lowercaseJoined=e}parse(t){return t.split(this.separator).filter(e=>!!e.trim())}join(t){const e=t.join(this.separator);return this.lowercaseJoined?e.toLowerCase():e}},Ie=class extends ge{constructor(){super("-",!0)}},T,ye=(T=class extends Se{constructor(){super(!0)}parse(e){return super.parse(e.replace(T.parser," "))}join(e){return e.join(" ")}},v(T,"parser",new RegExp(pe,"gu")),T),ve=class{constructor(t,e){this.parseStrategy=t,this.joinStrategy=e}transform(t){return this.joinStrategy.join(this.parseStrategy.parse(t))}};function be(...t){}function j(...t){}var we=new ve(new ye,new Ie),Ce={serialize:JSON.stringify,deserialize:JSON.parse},Oe={serialize:u.stringify,deserialize:u.parse};function Ee(t){return t!=null&&typeof t=="object"&&"input"in t&&(t.input instanceof ot||t.input===null||t.input===void 0)&&"output"in t&&(t.output instanceof ot||t.output===null||t.output===void 0)}var Re=class extends Error{constructor(e,n,r,a){super(`Fetch at ${e.toUpperCase()} ${n} failed, status ${r}, description: '${a}'`);v(this,"method");v(this,"url");v(this,"status");v(this,"description");this.method=e,this.url=n,this.status=r,this.description=a}};function rt(){return typeof window<"u"?window.localStorage??null:typeof globalThis!==void 0?globalThis.localStorage??null:null}function Ue(t){if(!t.length)return[];const e=t.split("/").slice(1),n=[];for(let r=0;r<e.length;r++)n.push("/"+e.slice(0,r+1).join("/"));return n}function Pe(t){return function(n){const r=Ue(n);for(const a of r)t.has(a)||t.set(a,[]),t.get(a).push(n)}}function Te(t){for(const[e,n]of t)t.set(e,[...new Set(n)])}function De(t,e){for(const[n,r]of t)for(const a of r)e.has(a)||e.set(a,[]),e.get(a).push(n)}function Ae(t){var n,r;const e=(n=rt())==null?void 0:n.getItem("acrpc:invalid-paths");if(e)try{const a=JSON.parse(e);for(const s of a)t.add(s)}catch(a){console.error("Error parsing invalid paths",a),(r=rt())==null||r.removeItem("acrpc:invalid-paths")}}function Ne(t,e,n){return function(a,s){var w;const o=t.get(a)??[];j("invalidating path cache",{path:a,depth:s,masterPaths:o});const l=o[Math.max(0,o.length-s)],p=e.get(l)??[];j("invalidating path cache 2",{masterPath:l,paths:p});for(const I of p)n.add(I);(w=rt())==null||w.setItem("acrpc:invalid-paths",JSON.stringify([...n]))}}function _e(t,e){const n=e.transformer??Ce,r=e.entrypointUrl,a=new Map,s=new Map,o=new Set,l=Pe(a),p=Ne(a,s,o);j({invalidPathCacheSet:o});const w=e.fetch??fetch,I={...e.init};function O(y,d,C){for(const[M,g]of Object.entries(y)){const Rt=we.transform(M);if(Ee(g)){let $=function(G){return g.input===null?[void 0,{...G[0]}]:[G[0],G[1]]};const U=["",...d].join("/"),D=M;l(U);const Ut={[D]:async function(...G){var at;const[Z,h]=$(G);if(g.input!=null&&!Z)throw new Error("Input data argument not provided.");be(`Performing ${D.toUpperCase()} ${U}...`);const q=new URL(U,r),Pt=o.has(U),J={...I,...h,headers:{...I.headers,...h==null?void 0:h.headers,...Pt?{"Cache-Control":"reload"}:null},method:D.toUpperCase()};if(g.input!==null&&Z!==void 0){const L=n.serialize(Z);D==="get"?q.searchParams.set("__body",encodeURIComponent(L)):(J.headers["Content-Type"]="application/json",J.body=L)}const Tt=(h==null?void 0:h.fetch)??w;h==null||delete h.fetch;const A=await Tt(q.origin+q.pathname+q.search,J);if(h!=null&&h.skipInterceptor||await((at=e.interceptor)==null?void 0:at.call(e,{method:D,path:U,response:A,ctx:h==null?void 0:h.ctx})),A.ok){let L=null;if(g.output!==null){const Y=await A.text();L=n.deserialize(Y)}j({autoScopeInvalidationDepth:g.autoScopeInvalidationDepth,invalidate:g.invalidate}),j("before invalidations",{masterPathMap:a,invalidPathCacheSet:o});const st=g.autoScopeInvalidationDepth??0;if(st&&p(U,st),g.invalidate)for(const Y of g.invalidate)p(Y,0);return j("after invalidations",{masterPathMap:a,invalidPathCacheSet:o}),L}throw new Re(D,U,A.status,await A.text()||A.statusText)}};Object.assign(C,Ut)}else{const $=C[M]={};O(g,[...d,Rt],$)}}return C}const B=O(t,[],{});return Te(a),De(a,s),Ae(o),{fetcher:B}}var je="no-cache",pt="no-cache",ke="no-cache",f=je,Me={auth:{otp:{post:{input:N.SendOtpInputSchema,output:N.SendOtpOutputSchema,isMetadataUsed:!1}},signUp:{post:{input:N.SignupInputSchema,output:N.SuccessfulOutputSchema,isMetadataUsed:!1,invalidate:["/user/me"]}},signIn:{post:{input:N.SigninInputSchema,output:N.SuccessfulOutputSchema,isMetadataUsed:!1,invalidate:["/user/me"]}},signOut:{get:{input:null,output:null,isMetadataUsed:!1,invalidate:["/user/me"]}}},commune:{transferHeadStatus:{post:{input:S.TransferHeadStatusInputSchema,output:null,autoScopeInvalidationDepth:2}},list:{get:{input:S.GetCommunesInputSchema,output:S.GetCommunesOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:S.CreateCommuneInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:S.UpdateCommuneInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1},member:{list:{get:{input:S.GetCommuneMembersInputSchema,output:S.GetCommuneMembersOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:S.CreateCommuneMemberInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},invitation:{list:{get:{input:S.GetCommuneInvitationsInputSchema,output:S.GetCommuneInvitationsOutputSchema,cacheControl:f}},post:{input:S.CreateCommuneInvitationInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1},accept:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}},reject:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}}},joinRequest:{list:{get:{input:S.GetCommuneJoinRequestsInputSchema,output:S.GetCommuneJoinRequestsOutputSchema,cacheControl:f}},post:{input:S.CreateCommuneJoinRequestInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1},accept:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}},reject:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}}}},rating:{karma:{list:{get:{input:E.GetKarmaPointsInputSchema,output:E.GetKarmaPointsOutputSchema,cacheControl:f}},post:{input:E.SpendKarmaPointInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1,invalidate:["/rating/summary"]}},feedback:{list:{get:{input:E.GetUserFeedbacksInputSchema,output:E.GetUserFeedbacksOutputSchema,cacheControl:f}},post:{input:E.CreateUserFeedbackInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1,invalidate:["/rating/summary"]}},summary:{get:{input:E.GetUserSummaryInputSchema,output:E.GetUserSummaryOutputSchema,cacheControl:f}}},reactor:{post:{list:{get:{input:i.GetPostsInputSchema,output:i.GetPostsOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreatePostInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdatePostInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:i.DeletePostInputSchema,output:null,autoScopeInvalidationDepth:1},rating:{post:{input:i.UpdatePostRatingInputSchema,output:i.UpdatePostRatingOutputSchema,autoScopeInvalidationDepth:2}},usefulness:{post:{input:i.UpdatePostUsefulnessInputSchema,output:i.UpdatePostUsefulnessOutputSchema,autoScopeInvalidationDepth:2}},image:{list:{get:{input:i.GetPostImagesInputSchema,output:i.GetPostImagesOutputSchema,cacheControl:f}}}},comment:{list:{get:{input:i.GetCommentsInputSchema,output:i.GetCommentsOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreateCommentInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateCommentInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:i.DeleteCommentInputSchema,output:null,autoScopeInvalidationDepth:1},rating:{post:{input:i.UpdateCommentRatingInputSchema,output:i.UpdateCommentRatingOutputSchema,autoScopeInvalidationDepth:2}},anonimify:{post:{input:i.AnonimifyCommentInputSchema,output:null,autoScopeInvalidationDepth:2}}},lens:{list:{get:{input:null,output:i.GetLensesOutputSchema,cacheControl:ke}},post:{input:i.CreateLensInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateLensInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},hub:{list:{get:{input:i.GetHubsInputSchema,output:i.GetHubsOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreateHubInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateHubInputSchema,output:null,autoScopeInvalidationDepth:1}},community:{list:{get:{input:i.GetCommunitiesInputSchema,output:i.GetCommunitiesOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreateCommunityInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateCommunityInputSchema,output:null,autoScopeInvalidationDepth:1}}},tag:{list:{get:{input:K.GetTagsInputSchema,output:K.GetTagsOutputSchema,cacheControl:pt}},post:{input:K.CreateTagInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:K.UpdateTagInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},user:{list:{get:{input:m.GetUsersInputSchema,output:m.GetUsersOutputSchema,cacheControl:f}},me:{get:{input:null,output:m.GetMeOutputSchema,cacheControl:pt}},patch:{input:m.UpdateUserInputSchema,output:null,autoScopeInvalidationDepth:1},title:{list:{get:{input:m.GetUserTitlesInputSchema,output:m.GetUserTitlesOutputSchema,cacheControl:f}},post:{input:m.CreateUserTitleInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:m.UpdateUserTitleInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},note:{get:{input:m.GetUserNoteInputSchema,output:m.GetUserNoteOutputSchema,cacheControl:f},put:{input:m.UpdateUserNoteInputSchema,output:null,autoScopeInvalidationDepth:1}},invite:{list:{get:{input:m.GetUserInvitesInputSchema,output:m.GetUserInvitesOutputSchema,cacheControl:f}},put:{input:m.UpsertUserInviteInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:m.DeleteUserInviteInputSchema,output:null,autoScopeInvalidationDepth:1}}}},Ge=Oe;let Le;function Ve(){return Le??(Le=_e(Me,{entrypointUrl:"http://localhost:4000",transformer:Ge,init:{credentials:"include"},interceptor:async({response:t,ctx:e})=>{if(t.status===401){Nt();const n=e?e.url.pathname+e.url.search:window.location.pathname+window.location.search;_t(302,`/auth?redirectFrom=${encodeURIComponent(n)}`)}}}))}export{Ve as g};
