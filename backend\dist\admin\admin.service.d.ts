import { User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { EmailService } from "src/email/email.service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class AdminService {
    private readonly prisma;
    private readonly emailService;
    constructor(prisma: PrismaService, emailService: EmailService);
    isInviteExists(email: string): Promise<boolean>;
    useInvite(email: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        locale: import("@prisma/client").$Enums.Locale;
        email: string;
        isUsed: boolean;
    }>;
    getUserInvites(input: User.GetUserInvitesInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        locale: import("@prisma/client").$Enums.Locale;
        email: string;
        isUsed: boolean;
    }[]>;
    upsertUserInvite(input: User.UpsertUserInviteInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        locale: import("@prisma/client").$Enums.Locale;
        email: string;
        isUsed: boolean;
    }>;
    deleteUserInvite(input: User.DeleteUserInviteInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string | null;
        locale: import("@prisma/client").$Enums.Locale;
        email: string;
        isUsed: boolean;
    }>;
}
