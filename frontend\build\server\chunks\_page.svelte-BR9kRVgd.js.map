{"version": 3, "file": "_page.svelte-BR9kRVgd.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/admin/invites/_page.svelte.js"], "sourcesContent": ["import { K as ensure_array_like, z as escape_html, G as attr_class, y as attr, N as maybe_selected, w as pop, u as push } from \"../../../../chunks/index.js\";\nimport \"../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../chunks/acrpc.js\";\nimport { M as Modal } from \"../../../../chunks/modal.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../chunks/exports.js\";\nimport \"../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\n/* empty css                                                                        */\nfunction _page($$payload, $$props) {\n  push();\n  const { data } = $$props;\n  const { fetcher: api } = getClient();\n  let invites = data.invites;\n  let isHasMoreInvites = data.isHasMoreInvites;\n  let isLoadingMore = false;\n  let showInviteModal = false;\n  let newInviteEmail = \"\";\n  let newInviteName = \"\";\n  let newInviteLocale = \"ru\";\n  let isInviting = false;\n  let inviteError = null;\n  let inviteSuccess = null;\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  async function handleInviteUser() {\n    if (!newInviteEmail.trim()) {\n      inviteError = \"Email is required\";\n      return;\n    }\n    if (!emailRegex.test(newInviteEmail.trim())) {\n      inviteError = \"Please enter a valid email address\";\n      return;\n    }\n    isInviting = true;\n    inviteError = null;\n    inviteSuccess = null;\n    try {\n      const response = await api.user.invite.put({\n        email: newInviteEmail.trim(),\n        name: newInviteName.trim() || null,\n        locale: newInviteLocale\n      });\n      const existingIndex = invites.findIndex((invite) => invite.email === newInviteEmail.trim());\n      const newInviteItem = {\n        id: response.id,\n        email: newInviteEmail.trim(),\n        name: newInviteName.trim() || null,\n        locale: newInviteLocale,\n        isUsed: false\n      };\n      if (existingIndex >= 0) {\n        invites[existingIndex] = newInviteItem;\n      } else {\n        invites = [newInviteItem, ...invites];\n      }\n      inviteSuccess = \"Invitation sent successfully!\";\n      newInviteEmail = \"\";\n      newInviteName = \"\";\n      newInviteLocale = \"ru\";\n      setTimeout(\n        () => {\n          showInviteModal = false;\n          inviteSuccess = null;\n        },\n        1500\n      );\n    } catch (error) {\n      inviteError = error instanceof Error ? error.message : \"Failed to send invitation\";\n      console.error(\"Error sending invitation:\", error);\n    } finally {\n      isInviting = false;\n    }\n  }\n  function closeInviteModal() {\n    showInviteModal = false;\n    newInviteEmail = \"\";\n    newInviteName = \"\";\n    newInviteLocale = \"ru\";\n    inviteError = null;\n    inviteSuccess = null;\n  }\n  $$payload.out.push(`<div class=\"admin-invites svelte-17chgb9\"><div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom\"><h1 class=\"h2\">User Invites</h1> <button type=\"button\" class=\"btn btn-primary\"><i class=\"bi bi-plus-circle me-1\"></i> Send Invite</button></div> `);\n  if (invites.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><div class=\"mb-3\"><i class=\"bi bi-envelope display-1 text-muted\"></i></div> <h4 class=\"text-muted\">No invitations yet</h4> <p class=\"text-muted\">Start by sending your first user invitation.</p> <button type=\"button\" class=\"btn btn-primary\"><i class=\"bi bi-plus-circle me-1\"></i> Send First Invite</button></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(invites);\n    $$payload.out.push(`<div class=\"card\"><div class=\"card-header\"><h5 class=\"mb-0\"><i class=\"bi bi-list-ul me-2\"></i> All Invitations (${escape_html(invites.length)})</h5></div> <div class=\"card-body p-0\"><div class=\"table-responsive\"><table class=\"table table-hover mb-0\"><thead class=\"table-light\"><tr><th scope=\"col\">Email</th><th scope=\"col\">Name</th><th scope=\"col\">Language</th><th scope=\"col\">Status</th><th scope=\"col\">Actions</th></tr></thead><tbody><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let invite = each_array[$$index];\n      $$payload.out.push(`<tr${attr_class(\"svelte-17chgb9\", void 0, { \"table-secondary\": invite.isUsed })}><td><div class=\"d-flex align-items-center\"><i class=\"bi bi-envelope me-2 text-muted svelte-17chgb9\"></i> <span${attr_class(\"svelte-17chgb9\", void 0, { \"text-muted\": invite.isUsed })}>${escape_html(invite.email)}</span></div></td><td><div class=\"d-flex align-items-center\"><i class=\"bi bi-person me-2 text-muted svelte-17chgb9\"></i> <span${attr_class(\"svelte-17chgb9\", void 0, { \"text-muted\": invite.isUsed })}>${escape_html(invite.name || \"—\")}</span></div></td><td><div class=\"d-flex align-items-center\"><i class=\"bi bi-globe me-2 text-muted svelte-17chgb9\"></i> <span class=\"badge bg-light text-dark border\">${escape_html(invite.locale.toUpperCase())}</span></div></td><td>`);\n      if (invite.isUsed) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<span class=\"badge bg-success\"><i class=\"bi bi-check-circle me-1\"></i> Used</span>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<span class=\"badge bg-warning text-dark\"><i class=\"bi bi-clock me-1\"></i> Pending</span>`);\n      }\n      $$payload.out.push(`<!--]--></td><td><button type=\"button\" class=\"btn btn-sm btn-outline-danger\" aria-label=\"Delete invitation\"><i class=\"bi bi-trash\"></i></button></td></tr>`);\n    }\n    $$payload.out.push(`<!--]--></tbody></table></div></div></div> `);\n    if (isHasMoreInvites) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"text-center mt-4\"><button type=\"button\" class=\"btn btn-outline-primary\"${attr(\"disabled\", isLoadingMore, true)}>`);\n      {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<i class=\"bi bi-arrow-down-circle me-1\"></i> Load More`);\n      }\n      $$payload.out.push(`<!--]--></button></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  Modal($$payload, {\n    show: showInviteModal,\n    title: \"Send User Invitation\",\n    onClose: closeInviteModal,\n    onSubmit: handleInviteUser,\n    submitText: \"Send Invite\",\n    cancelText: \"Cancel\",\n    submitDisabled: isInviting || !newInviteEmail.trim() || !emailRegex.test(newInviteEmail.trim()),\n    isSubmitting: isInviting,\n    children: ($$payload2) => {\n      if (inviteSuccess) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"alert alert-success\" role=\"alert\"><i class=\"bi bi-check-circle me-2\"></i> ${escape_html(inviteSuccess)}</div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n        $$payload2.out.push(`<form><div class=\"mb-3\"><label for=\"inviteEmail\" class=\"form-label\">Email Address *</label> <input type=\"email\" class=\"form-control\" id=\"inviteEmail\"${attr(\"value\", newInviteEmail)} placeholder=\"Enter email address\" required${attr(\"disabled\", isInviting, true)}/> <div class=\"form-text\">The user will receive an invitation to join the platform.</div></div> <div class=\"mb-3\"><label for=\"inviteName\" class=\"form-label\">Name (Optional)</label> <input type=\"text\" class=\"form-control\" id=\"inviteName\"${attr(\"value\", newInviteName)} placeholder=\"Enter user's name\"${attr(\"disabled\", isInviting, true)}/> <div class=\"form-text\">This name will be used in the invitation email.</div></div> <div class=\"mb-3\"><label for=\"inviteLocale\" class=\"form-label\">Language *</label> <select class=\"form-select\" id=\"inviteLocale\"${attr(\"disabled\", isInviting, true)}>`);\n        $$payload2.select_value = newInviteLocale;\n        $$payload2.out.push(`<option value=\"en\"${maybe_selected($$payload2, \"en\")}>English</option><option value=\"ru\"${maybe_selected($$payload2, \"ru\")}>Русский</option>`);\n        $$payload2.select_value = void 0;\n        $$payload2.out.push(`</select> <div class=\"form-text\">The language for the invitation email.</div></div> `);\n        if (inviteError) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`<div class=\"alert alert-danger\" role=\"alert\"><i class=\"bi bi-exclamation-triangle me-2\"></i> ${escape_html(inviteError)}</div>`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n        }\n        $$payload2.out.push(`<!--]--></form>`);\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n  });\n  $$payload.out.push(`<!---->`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,EAAE,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AAC9C,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,IAAI;AAC1B,EAAE,MAAM,UAAU,GAAG,4BAA4B;AACjD,EAAE,eAAe,gBAAgB,GAAG;AACpC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE;AAChC,MAAM,WAAW,GAAG,mBAAmB;AACvC,MAAM;AACN,IAAI;AACJ,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE;AACjD,MAAM,WAAW,GAAG,oCAAoC;AACxD,MAAM;AACN,IAAI;AACJ,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AACjD,QAAQ,KAAK,EAAE,cAAc,CAAC,IAAI,EAAE;AACpC,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,IAAI;AAC1C,QAAQ,MAAM,EAAE;AAChB,OAAO,CAAC;AACR,MAAM,MAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,cAAc,CAAC,IAAI,EAAE,CAAC;AACjG,MAAM,MAAM,aAAa,GAAG;AAC5B,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE;AACvB,QAAQ,KAAK,EAAE,cAAc,CAAC,IAAI,EAAE;AACpC,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,IAAI;AAC1C,QAAQ,MAAM,EAAE,eAAe;AAC/B,QAAQ,MAAM,EAAE;AAChB,OAAO;AACP,MAAM,IAAI,aAAa,IAAI,CAAC,EAAE;AAC9B,QAAQ,OAAO,CAAC,aAAa,CAAC,GAAG,aAAa;AAC9C,MAAM,CAAC,MAAM;AACb,QAAQ,OAAO,GAAG,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC;AAC7C,MAAM;AACN,MAAM,aAAa,GAAG,+BAA+B;AACrD,MAAM,cAAc,GAAG,EAAE;AACzB,MAAM,aAAa,GAAG,EAAE;AACxB,MAAM,eAAe,GAAG,IAAI;AAC5B,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,eAAe,GAAG,KAAK;AACjC,UAAU,aAAa,GAAG,IAAI;AAC9B,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,WAAW,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,2BAA2B;AACxF,MAAM,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACvD,IAAI,CAAC,SAAS;AACd,MAAM,UAAU,GAAG,KAAK;AACxB,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAI,cAAc,GAAG,EAAE;AACvB,IAAI,aAAa,GAAG,EAAE;AACtB,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gTAAgT,CAAC,CAAC;AACxU,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qVAAqV,CAAC,CAAC;AAC/W,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC;AACjD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gHAAgH,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,8SAA8S,CAAC,CAAC;AACtd,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;AACtC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,+GAA+G,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,8HAA8H,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,sKAAsK,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAChxB,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;AACzB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kFAAkF,CAAC,CAAC;AAChH,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wFAAwF,CAAC,CAAC;AACtH,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0JAA0J,CAAC,CAAC;AACtL,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2CAA2C,CAAC,CAAC;AACrE,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mFAAmF,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACxJ,MAAM;AACN,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sDAAsD,CAAC,CAAC;AACpF,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACnD,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,KAAK,EAAE,sBAAsB;AACjC,IAAI,OAAO,EAAE,gBAAgB;AAC7B,IAAI,QAAQ,EAAE,gBAAgB;AAC9B,IAAI,UAAU,EAAE,aAAa;AAC7B,IAAI,UAAU,EAAE,QAAQ;AACxB,IAAI,cAAc,EAAE,UAAU,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;AACnG,IAAI,YAAY,EAAE,UAAU;AAC5B,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sFAAsF,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AACxJ,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qJAAqJ,EAAE,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,2CAA2C,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,4OAA4O,EAAE,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,qNAAqN,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/2B,QAAQ,UAAU,CAAC,YAAY,GAAG,eAAe;AACjD,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,mCAAmC,EAAE,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC3K,QAAQ,UAAU,CAAC,YAAY,GAAG,MAAM;AACxC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oFAAoF,CAAC,CAAC;AACnH,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6FAA6F,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AAC/J,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}