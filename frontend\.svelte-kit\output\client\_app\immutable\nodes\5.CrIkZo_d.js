import"../chunks/Bzak7iHL.js";import{f as K,t as M,b as O,d as a,g as u,s as r,r as e,u as m}from"../chunks/RHWQbow4.js";import{s as c}from"../chunks/BlWcudmi.js";import{s as Q}from"../chunks/CkTdM00m.js";import{s}from"../chunks/BdpLTtcP.js";import{s as d}from"../chunks/Cxg-bych.js";import{s as V}from"../chunks/CaC9IHEK.js";import{L as W}from"../chunks/CBe4EX5h.js";import"../chunks/BiLRrsV0.js";var X=K('<div class="page-wrapper svelte-5oi5pp"><nav class="navbar navbar-expand-lg sticky-top reactor-navbar svelte-5oi5pp"><div class="container"><a class="navbar-brand py-0 ps-5 svelte-5oi5pp"><img src="/images/full-v3-transparent-white.svg" alt="Site Logo"/></a> <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button> <div class="collapse navbar-collapse svelte-5oi5pp" id="navbarNav"><ul class="navbar-nav mx-auto svelte-5oi5pp"><li><a class="nav-link svelte-5oi5pp"> </a></li> <li><a class="nav-link svelte-5oi5pp"> </a></li> <li><a class="nav-link svelte-5oi5pp"> </a></li> <li><a class="nav-link svelte-5oi5pp"> </a></li></ul> <ul class="navbar-nav svelte-5oi5pp"><li class="nav-item svelte-5oi5pp"><!></li></ul></div></div></nav> <main class="container-fluid flex-grow-1 mb-5 reactor-container svelte-5oi5pp"><!></main></div>');function ia(N,l){const k={ru:{navGap:"mx-1",commune:"Коммуна",theLaw:"Право",rules:"Правила",newEnglish:"Новый английский",newCalendar:"Новый календарь",news:"Новости",users:"Пользователи",communes:"Коммуны",hubs:"Хабы",communities:"Сообщества",profile:"Профиль",feed:"Лента",hole:"Яма"},en:{navGap:"mx-2",commune:"Commune",theLaw:"The Law",rules:"Rules",newEnglish:"New English",newCalendar:"New Calendar",news:"News",users:"Users",communes:"Communes",hubs:"Hubs",communities:"Communities",profile:"Profile",feed:"Feed",hole:"Pit"}},y=m(()=>l.data.locale),A=m(()=>l.data.routeLocale),i=m(()=>l.data.toLocaleHref),t=m(()=>k[u(y)]);var f=X(),b=a(f),B=a(b),n=a(B),g=a(n);s(g,"height",60),s(g,"width",60),V(g,"",{},{width:"auto"}),e(n);var L=r(n,4),h=a(L),o=a(h),_=a(o),H=a(_,!0);e(_),e(o);var v=r(o,2),w=a(v),P=a(w,!0);e(w),e(v);var p=r(v,2),x=a(p),T=a(x,!0);e(x),e(p);var E=r(p,2),C=a(E),R=a(C,!0);e(C),e(E),e(h);var D=r(h,2),F=a(D),S=a(F);W(S,{get currentLocale(){return u(A)}}),e(F),e(D),e(L),e(B),e(b);var G=r(b,2),U=a(G);Q(U,()=>l.children),e(G),e(f),M((j,q,z,I,J)=>{s(n,"href",j),d(o,1,`nav-item dropdown ${u(t).navGap??""} text-nowrap`,"svelte-5oi5pp"),s(_,"href",q),c(H,u(t).commune),d(v,1,`nav-item ${u(t).navGap} text-nowrap`,"svelte-5oi5pp"),s(w,"href",z),c(P,u(t).feed),d(p,1,`nav-item ${u(t).navGap} text-nowrap`,"svelte-5oi5pp"),s(x,"href",I),c(T,u(t).hubs),d(E,1,`nav-item ${u(t).navGap} text-nowrap`,"svelte-5oi5pp"),s(C,"href",J),c(R,u(t).communities)},[()=>u(i)("/"),()=>u(i)("/"),()=>u(i)("/reactor"),()=>u(i)("/reactor/hubs"),()=>u(i)("/reactor/communities")]),O(N,f)}export{ia as component};
