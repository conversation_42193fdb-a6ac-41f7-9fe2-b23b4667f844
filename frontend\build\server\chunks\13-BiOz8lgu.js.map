{"version": 3, "file": "13-BiOz8lgu.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_id_/invitations/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/13.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { a as consts_exports } from \"../../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, params, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    user,\n    [commune]\n  ] = await Promise.all([\n    api.user.me.get({ fetch, ctx: { url } }),\n    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } })\n  ]);\n  if (!commune) {\n    throw error(404, \"Commune not found\");\n  }\n  const isAdmin = user?.role === \"admin\";\n  const isHeadMember = user && commune.headMember.actorType === \"user\" && commune.headMember.actorId === user.id;\n  if (!isAdmin && !isHeadMember) {\n    throw new Error(\"Access denied: You must be an admin or commune head to view invitations\");\n  }\n  const invitations = await api.commune.invitation.list.get(\n    { communeId: params.id },\n    { fetch, ctx: { url } }\n  );\n  const users = invitations.length ? await api.user.list.get(\n    { ids: invitations.map(({ userId }) => userId) },\n    { fetch, ctx: { url } }\n  ) : [];\n  const userMap = new Map(users.map((user2) => [user2.id, user2]));\n  const invitationsWithUserDetails = invitations.map((invitation) => ({\n    ...invitation,\n    user: userMap.get(invitation.userId)\n  }));\n  return {\n    commune,\n    invitations: invitationsWithUserDetails,\n    isHasMoreInvitations: invitations.length === consts_exports.PAGE_SIZE,\n    userPermissions: {\n      isAdmin,\n      isHeadMember,\n      canManageInvitations: isAdmin || isHeadMember\n    }\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/communes/_id_/invitations/_page.ts.js';\n\nexport const index = 13;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/_id_/invitations/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/communes/[id]/invitations/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/13.BOYhCB60.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/DiZKRWcx.js\",\"_app/immutable/chunks/CL12WlkV.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC/C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,CAAC,OAAO;AACZ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACtE,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AACzC,EAAE;AACF,EAAE,MAAM,OAAO,GAAG,IAAI,EAAE,IAAI,KAAK,OAAO;AACxC,EAAE,MAAM,YAAY,GAAG,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,KAAK,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;AAChH,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC;AAC9F,EAAE;AACF,EAAE,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG;AAC3D,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE;AAC5B,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AACzB,GAAG;AACH,EAAE,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAC5D,IAAI,EAAE,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE;AACpD,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AACzB,GAAG,GAAG,EAAE;AACR,EAAE,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AAClE,EAAE,MAAM,0BAA0B,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,MAAM;AACtE,IAAI,GAAG,UAAU;AACjB,IAAI,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM;AACvC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,WAAW,EAAE,0BAA0B;AAC3C,IAAI,oBAAoB,EAAE,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;AACzE,IAAI,eAAe,EAAE;AACrB,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,oBAAoB,EAAE,OAAO,IAAI;AACvC;AACA,GAAG;AACH,CAAC;;;;;;;ACzCW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA+E,CAAC,EAAE;AAE7I,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACjnB,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}