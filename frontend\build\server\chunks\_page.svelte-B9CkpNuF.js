import { u as push, x as head, y as attr, z as escape_html, J as attr_class, G as attr_style, K as stringify, w as pop } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { g as getUserRateColor } from './get-user-rate-color-CzjBgne7.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './schema-CmMg_B_X.js';

function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "— Commune" },
      userNotFound: "User not found",
      userDetails: "User Details",
      joinedOn: "Joined on",
      dateFormatLocale: "en-US",
      userNote: "Personal Note",
      userNotePlaceholder: "Write your personal note about this user...",
      saved: "Saved...",
      rating: "Rating",
      karma: "Karma",
      rate: "Rate",
      noImage: "No image available",
      userImageAlt: "User image",
      socialOpinion: "Social Opinion"
    },
    ru: {
      _page: { title: "— Коммуна" },
      userNotFound: "Пользователь не найден",
      userDetails: "Информация о пользователе",
      joinedOn: "Дата регистрации",
      dateFormatLocale: "ru-RU",
      userNote: "Личная заметка",
      userNotePlaceholder: "Напишите свою личную заметку об этом пользователе...",
      saved: "Сохранено...",
      rating: "Рейтинг",
      karma: "Карма",
      rate: "Оценка",
      noImage: "Нет доступных изображений",
      userImageAlt: "Изображение пользователя",
      socialOpinion: "Общественное мнение"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const {
    user,
    locale,
    getAppropriateLocalization,
    ratingSummary,
    toLocaleHref
  } = data;
  const t = i18n[locale];
  let userNote = data.userNote;
  const userName = getAppropriateLocalization(user.name);
  const userDescription = getAppropriateLocalization(user.description);
  const joinDate = user ? new Date(user.createdAt) : /* @__PURE__ */ new Date();
  const formattedDate = joinDate.toLocaleDateString(t.dateFormatLocale, { year: "numeric", month: "long", day: "numeric" });
  const getBadgeClass = (role) => {
    switch (role) {
      case "admin":
        return "bg-danger";
      case "moderator":
        return "bg-warning";
      default:
        return "bg-primary";
    }
  };
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(userName)} ${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container py-4"><div class="row"><div class="col-lg-8">`);
  if (user.image) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"><img${attr("src", `/images/${user.image}`)}${attr("alt", `${t.userImageAlt}`)} style="width: 100%; height: 100%; object-fit: contain;"/></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="bg-light text-center rounded mb-4 d-flex align-items-center justify-content-center" style="height: 300px;"><span class="text-muted">${escape_html(t.noImage)}</span></div>`);
  }
  $$payload.out.push(`<!--]--> <div class="mb-4"><div class="d-flex justify-content-between align-items-center mb-3"><h2 class="mb-0">${escape_html(userName)}</h2> <span${attr_class(`badge ${getBadgeClass(user.role)}`, "svelte-1hels8p")}>${escape_html(user.role)}</span></div> <p class="lead text-muted">${escape_html(userDescription || "")}</p></div></div> <div class="col-lg-4"><div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title">${escape_html(t.userDetails)}</h5> <hr/> <div class="d-flex align-items-center"><i class="bi bi-calendar-date me-2 text-primary"></i> <span>${escape_html(t.joinedOn)} ${escape_html(formattedDate)}</span></div></div></div> `);
  if (ratingSummary) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title">${escape_html(t.socialOpinion)}</h5> <hr/> <div class="row g-3"><div class="col-4"><div class="rating-block border rounded p-3 text-center svelte-1hels8p" style="border-color: #fd7e14 !important;"><div class="rating-label text-muted small mb-1">${escape_html(t.rating)}</div> <div class="rating-value fw-bold" style="color: #fd7e14; font-size: 1.5rem;">${escape_html(ratingSummary.rating)}</div></div></div> <div class="col-4"><a${attr("href", toLocaleHref(`/users/${user.id}/karma`))} class="karma-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p" style="border-color: #d63384 !important;"><div class="karma-label text-muted small mb-1">${escape_html(t.karma)}</div> <div class="karma-value fw-bold" style="color: #d63384; font-size: 1.5rem;">${escape_html(ratingSummary.karma)}</div></a></div> <div class="col-4"><a${attr("href", toLocaleHref(`/users/${user.id}/feedback`))} class="rate-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p" style="border-color: #6c757d !important;"><div class="rate-label text-muted small mb-1">${escape_html(t.rate)}</div> <div${attr_class(`rate-value fw-bold ${stringify(ratingSummary.rate === null ? "text-muted" : "")}`)}${attr_style(`font-size: 1.5rem; ${stringify(ratingSummary.rate !== null ? `color: ${getUserRateColor(ratingSummary.rate)};` : "")}`)}>${escape_html(ratingSummary.rate !== null ? ratingSummary.rate.toFixed(1) : "N/A")}</div></a></div></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title">${escape_html(t.userNote)}</h5> <hr/> <div class="mb-2"><textarea class="form-control" rows="4"${attr("placeholder", t.userNotePlaceholder)}>`);
  const $$body = escape_html(userNote);
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div></div></div></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-B9CkpNuF.js.map
