import { u as push, Q as copy_payload, T as assign_payload, w as pop, x as head, z as escape_html, y as attr, N as ensure_array_like, J as attr_class, K as stringify } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Karma — Commune" },
      userNotFound: "User not found",
      karmaHistory: "Karma History",
      changeKarma: "Change Karma",
      karmaModalTitle: "Change Karma",
      giveKarma: "Give Karma (+1)",
      takeKarma: "Take Karma (-1)",
      comment: "Comment",
      commentPlaceholder: "Enter your comment...",
      cancel: "Cancel",
      submit: "Submit",
      submitting: "Submitting...",
      success: "Karma changed successfully",
      errorSubmitting: "Error submitting karma change",
      noKarmaChanges: "No karma changes found",
      loadingMore: "Loading more...",
      errorOccurred: "An error occurred",
      errorFetchingKarma: "Error fetching karma changes",
      insufficientPoints: {
        title: "Insufficient Karma Points",
        message: "You don't have enough spendable karma points to perform this action.",
        explanation: "You need at least one spendable karma point to give or take karma from other users.",
        howToEarn: "You get one karma point per week.",
        backToProfile: "Back to Profile"
      }
    },
    ru: {
      _page: {
        title: "Карма — Коммуна"
      },
      userNotFound: "Пользователь не найден",
      karmaHistory: "История кармы",
      changeKarma: "Изменить карму",
      karmaModalTitle: "Изменить карму",
      giveKarma: "Дать карму (+1)",
      takeKarma: "Забрать карму (-1)",
      comment: "Комментарий",
      commentPlaceholder: "Введите ваш комментарий...",
      cancel: "Отмена",
      submit: "Отправить",
      submitting: "Отправка...",
      success: "Карма успешно изменена",
      errorSubmitting: "Ошибка при изменении кармы",
      noKarmaChanges: "Изменения кармы не найдены",
      loadingMore: "Загрузка...",
      errorOccurred: "Произошла ошибка",
      errorFetchingKarma: "Ошибка загрузки изменений кармы",
      insufficientPoints: {
        title: "Недостаточно очков кармы",
        message: "У вас недостаточно доступных очков кармы для выполнения этого действия.",
        explanation: "Вам нужно как минимум одно доступное очко кармы, чтобы давать или забирать карму у других пользователей.",
        howToEarn: "Вы получаете одно очко кармы в неделю.",
        backToProfile: "Вернуться к профилю"
      }
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { me, user, locale, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let karmaPoints = data.karmaPoints;
  let isHasMoreKarma = data.isHasMoreKarma;
  const userName = getAppropriateLocalization(user.name);
  const getAuthorDisplayName = (author) => {
    return getAppropriateLocalization(author.name);
  };
  const formatQuantity = (quantity) => {
    return quantity > 0 ? `+${quantity}` : `${quantity}`;
  };
  const getQuantityColorClass = (quantity) => {
    return quantity > 0 ? "text-success" : "text-danger";
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(userName)} ${escape_html(t._page.title)}</title>`;
    });
    $$payload2.out.push(`<div class="container py-4"><div class="responsive-container">`);
    if (!user) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="alert alert-danger" role="alert">${escape_html(t.userNotFound)}</div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="d-flex justify-content-between align-items-center mb-4"><div><h2 class="mb-1">${escape_html(userName)}</h2> <p class="text-muted mb-0">${escape_html(t.karmaHistory)}</p></div> `);
      if (me.id !== user.id) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div><button class="btn btn-primary btn-sm"${attr("aria-label", t.changeKarma)}><i class="bi bi-arrow-up-down me-1"></i> ${escape_html(t.changeKarma)}</button></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div> `);
      if (karmaPoints.length === 0) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noKarmaChanges)}</p></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
        const each_array = ensure_array_like(karmaPoints);
        $$payload2.out.push(`<!--[-->`);
        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
          let karmaPoint = each_array[$$index];
          $$payload2.out.push(`<div class="card mb-3 shadow-sm svelte-5uit7u"><div class="card-body"><div class="d-flex align-items-start"><div class="me-3">`);
          if (karmaPoint.author.image) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<img${attr("src", `/images/${karmaPoint.author.image}`)}${attr("alt", getAuthorDisplayName(karmaPoint.author))} class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
            $$payload2.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white" style="width: 48px; height: 48px;"><i class="bi bi-person-fill"></i></div>`);
          }
          $$payload2.out.push(`<!--]--></div> <div class="flex-grow-1"><div class="d-flex justify-content-between align-items-start mb-2"><div><h6 class="mb-1">${escape_html(getAuthorDisplayName(karmaPoint.author))}</h6></div> <span${attr_class(`badge ${stringify(getQuantityColorClass(karmaPoint.quantity))} fs-6`, "svelte-5uit7u")}>${escape_html(formatQuantity(karmaPoint.quantity))}</span></div> <p class="mb-0 text-muted">${escape_html(getAppropriateLocalization(karmaPoint.comment))}</p></div></div></div></div>`);
        }
        $$payload2.out.push(`<!--]-->`);
      }
      $$payload2.out.push(`<!--]--> `);
      if (isHasMoreKarma) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="text-center py-3">`);
        {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]--></div> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BWbeUunt.js.map
