import {
  __export
} from "./chunk-J5LGTIGS.mjs";

// src/sitemap.ts
var sitemap_exports = {};
__export(sitemap_exports, {
  GetSitemapGenerationDataOutputSchema: () => GetSitemapGenerationDataOutputSchema
});
import { z as z2 } from "zod";

// src/common.ts
var common_exports = {};
__export(common_exports, {
  FormDataToObject: () => FormDataToObject,
  ImageSchema: () => ImageSchema,
  ImagesSchema: () => ImagesSchema,
  JsonStringToObject: () => JsonStringToObject,
  LocalizationLocaleSchema: () => LocalizationLocaleSchema,
  LocalizationLocalesSchema: () => LocalizationLocalesSchema,
  LocalizationSchema: () => LocalizationSchema,
  LocalizationsSchema: () => LocalizationsSchema,
  ObjectWithIdSchema: () => ObjectWithIdSchema,
  PaginationSchema: () => PaginationSchema,
  WebsiteLocaleSchema: () => WebsiteLocaleSchema,
  createdAt: () => createdAt,
  deletedAt: () => deletedAt,
  email: () => email,
  id: () => id,
  idOrNull: () => idOrNull,
  imageUrl: () => imageUrl,
  maybeImageUrl: () => maybeImageUrl,
  pagination: () => pagination,
  parseInput: () => parseInput,
  parseUnknown: () => parseUnknown,
  query: () => query,
  searchIds: () => searchIds,
  searchQuery: () => searchQuery,
  stringToDate: () => stringToDate,
  updatedAt: () => updatedAt,
  url: () => url
});

// src/consts.ts
var consts_exports = {};
__export(consts_exports, {
  ALLOWED_IMAGE_FILE_TYPES: () => ALLOWED_IMAGE_FILE_TYPES,
  MAX_IMAGE_FILE_SIZE: () => MAX_IMAGE_FILE_SIZE,
  PAGE_SIZE: () => PAGE_SIZE
});
var PAGE_SIZE = 20;
var ALLOWED_IMAGE_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
var MAX_IMAGE_FILE_SIZE = 5 * 1024 * 1024;

// src/common.ts
import { z } from "zod";
var id = z.string().nanoid();
var idOrNull = id.nullable().default(null);
var url = z.string().url();
var email = z.string().email();
var query = z.string().nonempty();
var imageUrl = z.string().nonempty();
var maybeImageUrl = imageUrl.nullable();
var createdAt = z.date();
var updatedAt = z.date();
var deletedAt = z.date().nullable();
var searchIds = z.array(id).min(1);
var searchQuery = z.string().nonempty();
var stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());
function JsonStringToObject(schema) {
  return z.string().transform((value) => JSON.parse(value)).pipe(z.object(schema));
}
function FormDataToObject(schema) {
  return z.object({
    data: JsonStringToObject(schema)
  });
}
var ObjectWithIdSchema = z.object({ id });
var WebsiteLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);
var LocalizationSchema = z.object({
  locale: LocalizationLocaleSchema,
  value: z.string().nonempty()
});
var LocalizationsSchema = z.array(LocalizationSchema);
var ImageSchema = z.object({
  id,
  url: z.string(),
  createdAt: stringToDate,
  updatedAt: stringToDate
});
var ImagesSchema = z.array(ImageSchema);
var pagination = {
  offset: z.coerce.number().int().default(0),
  limit: z.coerce.number().int().positive().max(100).default(PAGE_SIZE),
  page: z.coerce.number().int().positive().default(1),
  size: z.coerce.number().int().positive().max(100).default(PAGE_SIZE)
};
var PaginationSchema = z.object({
  page: pagination.page,
  size: pagination.size
}).default({
  page: 1,
  size: PAGE_SIZE
});
function parseInput(schema, value) {
  return schema.parse(value);
}
function parseUnknown(schema, value) {
  return schema.parse(value);
}

// src/sitemap.ts
var GetSitemapGenerationDataOutputSchema = z2.object({
  communeIds: z2.array(id),
  reactorPostIds: z2.array(id),
  reactorHubIds: z2.array(id),
  reactorCommunityIds: z2.array(id)
});

// src/auth.ts
var auth_exports = {};
__export(auth_exports, {
  SendOtpInputSchema: () => SendOtpInputSchema,
  SendOtpOutputSchema: () => SendOtpOutputSchema,
  SigninInputSchema: () => SigninInputSchema,
  SignupInputSchema: () => SignupInputSchema,
  SuccessfulOutputSchema: () => SuccessfulOutputSchema,
  otp: () => otp
});
import { z as z4 } from "zod";

// src/user.ts
var user_exports = {};
__export(user_exports, {
  CreateUserTitleInputSchema: () => CreateUserTitleInputSchema,
  DeleteUserInviteInputSchema: () => DeleteUserInviteInputSchema,
  GetMeOutputSchema: () => GetMeOutputSchema,
  GetUserInvitesInputSchema: () => GetUserInvitesInputSchema,
  GetUserInvitesOutputSchema: () => GetUserInvitesOutputSchema,
  GetUserNoteInputSchema: () => GetUserNoteInputSchema,
  GetUserNoteOutputSchema: () => GetUserNoteOutputSchema,
  GetUserOutputSchema: () => GetUserOutputSchema,
  GetUserTitlesInputSchema: () => GetUserTitlesInputSchema,
  GetUserTitlesOutputSchema: () => GetUserTitlesOutputSchema,
  GetUsersInputSchema: () => GetUsersInputSchema,
  GetUsersOutputSchema: () => GetUsersOutputSchema,
  SimpleUserSchema: () => SimpleUserSchema,
  UpdateUserInputSchema: () => UpdateUserInputSchema,
  UpdateUserNoteInputSchema: () => UpdateUserNoteInputSchema,
  UpdateUserTitleInputSchema: () => UpdateUserTitleInputSchema,
  UpsertUserInviteInputSchema: () => UpsertUserInviteInputSchema,
  UserRoleSchema: () => UserRoleSchema,
  userDescription: () => userDescription,
  userImage: () => userImage,
  userName: () => userName,
  userNoteText: () => userNoteText,
  userTitleColor: () => userTitleColor,
  userTitleIsActive: () => userTitleIsActive,
  userTitleName: () => userTitleName
});
import { z as z3 } from "zod";
var userName = LocalizationsSchema.min(1);
var userDescription = LocalizationsSchema;
var userImage = imageUrl.nullable();
var userTitleName = LocalizationsSchema.min(1);
var userTitleIsActive = z3.boolean();
var userTitleColor = z3.string().nonempty().nullable();
var userNoteText = z3.string().nonempty();
var UserRoleSchema = z3.enum([
  "admin",
  "moderator",
  "user"
]);
var SimpleUserSchema = z3.object({
  id,
  name: userName,
  image: userImage
});
var GetMeOutputSchema = z3.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: imageUrl.nullable(),
  createdAt,
  updatedAt
});
var GetUsersInputSchema = z3.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetUserOutputSchema = z3.object({
  id,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: userImage,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetUsersOutputSchema = z3.array(GetUserOutputSchema);
var UpdateUserInputSchema = z3.object({
  id,
  name: userName.optional(),
  description: userDescription.optional()
});
var CreateUserTitleInputSchema = z3.object({
  userId: id,
  name: userTitleName,
  isActive: userTitleIsActive,
  color: userTitleColor
});
var UpdateUserTitleInputSchema = z3.object({
  id,
  name: userTitleName.optional(),
  isActive: userTitleIsActive.optional(),
  color: userTitleColor.optional()
});
var GetUserTitlesInputSchema = z3.object({
  userId: id,
  ids: searchIds.optional(),
  isActive: userTitleIsActive.optional()
});
var GetUserTitlesOutputSchema = z3.array(
  z3.object({
    id,
    userId: id,
    name: userTitleName,
    isActive: userTitleIsActive,
    color: userTitleColor,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var GetUserNoteInputSchema = z3.object({
  userId: id
});
var GetUserNoteOutputSchema = z3.object({
  text: userNoteText.nullable()
});
var UpdateUserNoteInputSchema = z3.object({
  userId: id,
  text: userNoteText.nullable()
});
var GetUserInvitesInputSchema = z3.object({
  pagination: PaginationSchema
});
var GetUserInvitesOutputSchema = z3.array(z3.object({
  id,
  email,
  name: z3.string().nonempty().nullable(),
  locale: LocalizationLocaleSchema,
  isUsed: z3.boolean()
}));
var UpsertUserInviteInputSchema = z3.object({
  email,
  name: z3.string().nonempty().nullable(),
  locale: LocalizationLocaleSchema
});
var DeleteUserInviteInputSchema = z3.object({
  id
});

// src/auth.ts
var otp = z4.string().nonempty().length(6);
var SendOtpInputSchema = z4.object({
  email
});
var SendOtpOutputSchema = z4.object({
  isSent: z4.boolean()
});
var SignupInputSchema = z4.object({
  referrerId: id.nullable(),
  email,
  otp
});
var SigninInputSchema = z4.object({
  email,
  otp
});
var SuccessfulOutputSchema = z4.object({
  id,
  email,
  role: UserRoleSchema
});

// src/commune.ts
var commune_exports = {};
__export(commune_exports, {
  CommuneInvitationStatusSchema: () => CommuneInvitationStatusSchema,
  CommuneJoinRequestStatusSchema: () => CommuneJoinRequestStatusSchema,
  CommuneMemberTypeSchema: () => CommuneMemberTypeSchema,
  CreateCommuneInputSchema: () => CreateCommuneInputSchema,
  CreateCommuneInvitationInputSchema: () => CreateCommuneInvitationInputSchema,
  CreateCommuneJoinRequestInputSchema: () => CreateCommuneJoinRequestInputSchema,
  CreateCommuneMemberInputSchema: () => CreateCommuneMemberInputSchema,
  GetCommuneInvitationsInputSchema: () => GetCommuneInvitationsInputSchema,
  GetCommuneInvitationsOutputSchema: () => GetCommuneInvitationsOutputSchema,
  GetCommuneJoinRequestsInputSchema: () => GetCommuneJoinRequestsInputSchema,
  GetCommuneJoinRequestsOutputSchema: () => GetCommuneJoinRequestsOutputSchema,
  GetCommuneMemberOutputSchema: () => GetCommuneMemberOutputSchema,
  GetCommuneMembersInputSchema: () => GetCommuneMembersInputSchema,
  GetCommuneMembersOutputSchema: () => GetCommuneMembersOutputSchema,
  GetCommuneOutputSchema: () => GetCommuneOutputSchema,
  GetCommunesInputSchema: () => GetCommunesInputSchema,
  GetCommunesOutputSchema: () => GetCommunesOutputSchema,
  TransferHeadStatusInputSchema: () => TransferHeadStatusInputSchema,
  UpdateCommuneInputSchema: () => UpdateCommuneInputSchema,
  communeDescription: () => communeDescription,
  communeMemberActorType: () => communeMemberActorType,
  communeMemberName: () => communeMemberName,
  communeName: () => communeName
});
import { z as z5 } from "zod";
var CommuneMemberTypeSchema = z5.enum(["user"]);
var communeName = LocalizationsSchema.min(1);
var communeDescription = LocalizationsSchema;
var communeMemberActorType = CommuneMemberTypeSchema;
var communeMemberName = z5.union([userName, communeName]);
var TransferHeadStatusInputSchema = z5.object({
  communeId: id,
  newHeadUserId: id
});
var GetCommunesInputSchema = z5.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  userId: id
}).partial();
var GetCommuneOutputSchema = z5.object({
  id,
  name: communeName,
  description: communeDescription,
  headMember: z5.object({
    actorType: communeMemberActorType,
    actorId: id,
    name: communeMemberName,
    image: maybeImageUrl
  }),
  memberCount: z5.number().int().positive(),
  image: maybeImageUrl,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetCommunesOutputSchema = z5.array(GetCommuneOutputSchema);
var CreateCommuneInputSchema = z5.object({
  headUserId: id.optional(),
  name: communeName,
  description: communeDescription
});
var UpdateCommuneInputSchema = z5.object({
  id,
  name: communeName.optional(),
  description: communeDescription.optional()
});
var GetCommuneMembersInputSchema = z5.object({
  pagination: PaginationSchema,
  communeId: id
});
var GetCommuneMemberOutputSchema = z5.object({
  id,
  actorType: communeMemberActorType,
  actorId: id,
  name: communeMemberName,
  image: maybeImageUrl,
  createdAt,
  deletedAt: deletedAt.nullable()
});
var GetCommuneMembersOutputSchema = z5.array(GetCommuneMemberOutputSchema);
var CreateCommuneMemberInputSchema = z5.object({
  communeId: id,
  userId: id
});
var CommuneInvitationStatusSchema = z5.enum(["pending", "accepted", "rejected", "expired"]);
var GetCommuneInvitationsInputSchema = z5.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneInvitationsOutputSchema = z5.array(
  z5.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneInvitationStatusSchema,
    createdAt: z5.date(),
    updatedAt: z5.date()
  })
);
var CreateCommuneInvitationInputSchema = z5.object({
  communeId: id,
  userId: id
});
var CommuneJoinRequestStatusSchema = z5.enum(["pending", "accepted", "rejected"]);
var GetCommuneJoinRequestsInputSchema = z5.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneJoinRequestsOutputSchema = z5.array(
  z5.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneJoinRequestStatusSchema,
    createdAt: z5.date(),
    updatedAt: z5.date()
  })
);
var CreateCommuneJoinRequestInputSchema = z5.object({
  communeId: id,
  userId: id
});

// src/reactor.ts
var reactor_exports = {};
__export(reactor_exports, {
  AnonimifyCommentInputSchema: () => AnonimifyCommentInputSchema,
  CommentEntityTypeSchema: () => CommentEntityTypeSchema,
  CreateCommentInputSchema: () => CreateCommentInputSchema,
  CreateCommunityInputSchema: () => CreateCommunityInputSchema,
  CreateHubInputSchema: () => CreateHubInputSchema,
  CreateLensInputSchema: () => CreateLensInputSchema,
  CreatePostInputSchema: () => CreatePostInputSchema,
  DeleteCommentInputSchema: () => DeleteCommentInputSchema,
  DeletePostInputSchema: () => DeletePostInputSchema,
  GetCommentsInputSchema: () => GetCommentsInputSchema,
  GetCommentsOutputSchema: () => GetCommentsOutputSchema,
  GetCommunitiesInputSchema: () => GetCommunitiesInputSchema,
  GetCommunitiesOutputSchema: () => GetCommunitiesOutputSchema,
  GetHubsInputSchema: () => GetHubsInputSchema,
  GetHubsOutputSchema: () => GetHubsOutputSchema,
  GetLensesOutputSchema: () => GetLensesOutputSchema,
  GetPostImagesInputSchema: () => GetPostImagesInputSchema,
  GetPostImagesOutputSchema: () => GetPostImagesOutputSchema,
  GetPostOutputSchema: () => GetPostOutputSchema,
  GetPostsInputSchema: () => GetPostsInputSchema,
  GetPostsOutputSchema: () => GetPostsOutputSchema,
  PostImageSchema: () => PostImageSchema,
  PostUsefulnessSchema: () => PostUsefulnessSchema,
  RatingSchema: () => RatingSchema,
  RatingTypeSchema: () => RatingTypeSchema,
  UpdateCommentInputSchema: () => UpdateCommentInputSchema,
  UpdateCommentRatingInputSchema: () => UpdateCommentRatingInputSchema,
  UpdateCommentRatingOutputSchema: () => UpdateCommentRatingOutputSchema,
  UpdateCommunityInputSchema: () => UpdateCommunityInputSchema,
  UpdateHubInputSchema: () => UpdateHubInputSchema,
  UpdateLensInputSchema: () => UpdateLensInputSchema,
  UpdatePostInputSchema: () => UpdatePostInputSchema,
  UpdatePostRatingInputSchema: () => UpdatePostRatingInputSchema,
  UpdatePostRatingOutputSchema: () => UpdatePostRatingOutputSchema,
  UpdatePostUsefulnessInputSchema: () => UpdatePostUsefulnessInputSchema,
  UpdatePostUsefulnessOutputSchema: () => UpdatePostUsefulnessOutputSchema,
  commentBody: () => commentBody,
  communityDescription: () => communityDescription,
  communityImage: () => communityImage,
  communityName: () => communityName,
  hubDescription: () => hubDescription,
  hubImage: () => hubImage,
  hubName: () => hubName,
  lensCode: () => lensCode,
  lensName: () => lensName,
  postBody: () => postBody,
  postTitle: () => postTitle,
  postUsefulness: () => postUsefulness
});
import { z as z7 } from "zod";

// src/tag.ts
var tag_exports = {};
__export(tag_exports, {
  CreateTagInputSchema: () => CreateTagInputSchema,
  GetTagsInputSchema: () => GetTagsInputSchema,
  GetTagsOutputSchema: () => GetTagsOutputSchema,
  UpdateTagInputSchema: () => UpdateTagInputSchema,
  tagName: () => tagName
});
import { z as z6 } from "zod";
var tagName = LocalizationsSchema.min(1);
var GetTagsInputSchema = z6.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetTagsOutputSchema = z6.array(
  z6.object({
    id,
    name: tagName,
    deletedAt: deletedAt.optional()
  })
);
var CreateTagInputSchema = z6.object({
  name: tagName
});
var UpdateTagInputSchema = z6.object({
  id,
  name: tagName
});

// src/reactor.ts
var RatingTypeSchema = z7.enum(["like", "dislike"]);
var RatingSchema = z7.object({
  likes: z7.number().int().nonnegative(),
  dislikes: z7.number().int().nonnegative(),
  status: RatingTypeSchema.nullable()
});
var hubName = LocalizationsSchema.min(1);
var hubDescription = LocalizationsSchema.min(1);
var hubImage = maybeImageUrl;
var communityName = LocalizationsSchema.min(1);
var communityDescription = LocalizationsSchema.min(1);
var communityImage = maybeImageUrl;
var postUsefulness = z7.number().int().min(0).max(10);
var PostUsefulnessSchema = z7.object({
  value: postUsefulness.nullable(),
  count: z7.number().int().nonnegative(),
  totalValue: z7.number().min(0).max(10).nullable()
});
var postTitle = LocalizationsSchema.min(1);
var postBody = LocalizationsSchema.min(1);
var PostImageSchema = z7.object({
  id,
  url: imageUrl
});
var GetPostOutputSchema = z7.object({
  id,
  hub: z7.object({
    id,
    name: hubName,
    image: hubImage
  }).nullable(),
  community: z7.object({
    id,
    name: communityName,
    image: communityImage
  }).nullable(),
  author: SimpleUserSchema,
  rating: RatingSchema,
  usefulness: PostUsefulnessSchema,
  title: postTitle,
  body: postBody,
  tags: z7.array(
    z7.object({
      id,
      name: tagName
    })
  ),
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetPostsInputSchema = z7.object({
  pagination: PaginationSchema,
  id: id.optional(),
  lensId: id.nullable()
});
var GetPostsOutputSchema = z7.array(GetPostOutputSchema);
var GetPostImagesInputSchema = z7.object({
  id
});
var GetPostImagesOutputSchema = z7.array(PostImageSchema);
var CreatePostInputSchema = z7.object({
  hubId: id.nullable(),
  communityId: id.nullable(),
  title: postTitle,
  body: postBody,
  tagIds: z7.array(id),
  imageIds: z7.array(id)
});
var UpdatePostInputSchema = z7.object({
  id,
  title: postTitle.optional(),
  body: postBody.optional(),
  tagIds: z7.array(id).optional(),
  imageIds: z7.array(id).optional()
});
var DeletePostInputSchema = z7.object({
  id,
  reason: z7.string().nonempty().nullable()
});
var UpdatePostRatingInputSchema = z7.object({
  id,
  type: RatingTypeSchema
});
var UpdatePostRatingOutputSchema = RatingSchema;
var UpdatePostUsefulnessInputSchema = z7.object({
  id,
  value: postUsefulness.nullable()
});
var UpdatePostUsefulnessOutputSchema = PostUsefulnessSchema;
var CommentEntityTypeSchema = z7.enum(["post", "comment"]);
var commentBody = LocalizationsSchema.min(1);
var GetCommentsInputSchema = z7.union([
  z7.object({
    id,
    entityType: z7.never().optional(),
    entityId: z7.never().optional()
  }),
  z7.object({
    id: z7.never().optional(),
    entityType: CommentEntityTypeSchema,
    entityId: id
  })
]);
var GetCommentsOutputSchema = z7.array(
  z7.object({
    id,
    path: z7.string().nonempty(),
    author: SimpleUserSchema.nullable(),
    isAnonymous: z7.boolean(),
    anonimityReason: z7.string().nonempty().nullable(),
    rating: RatingSchema,
    body: commentBody.nullable(),
    childrenCount: z7.number().int().nonnegative(),
    deleteReason: z7.string().nonempty().nullable(),
    createdAt,
    updatedAt,
    deletedAt
  })
);
var CreateCommentInputSchema = z7.object({
  entityType: CommentEntityTypeSchema,
  entityId: id,
  body: commentBody
});
var UpdateCommentInputSchema = z7.object({
  id,
  body: commentBody.optional()
});
var DeleteCommentInputSchema = z7.object({
  id,
  reason: z7.string().nonempty().nullable()
});
var UpdateCommentRatingInputSchema = z7.object({
  id,
  type: RatingTypeSchema
});
var UpdateCommentRatingOutputSchema = RatingSchema;
var AnonimifyCommentInputSchema = z7.object({
  id,
  reason: z7.string().nonempty().nullable()
});
var lensName = z7.string().nonempty();
var lensCode = z7.string().nonempty();
var GetLensesOutputSchema = z7.array(
  z7.object({
    id,
    name: lensName,
    code: lensCode
  })
);
var CreateLensInputSchema = z7.object({
  name: lensName,
  code: lensCode
});
var UpdateLensInputSchema = z7.object({
  id,
  name: lensName.optional(),
  code: lensCode.optional()
});
var GetHubsInputSchema = z7.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetHubsOutputSchema = z7.array(
  z7.object({
    id,
    headUser: SimpleUserSchema,
    image: hubImage,
    name: hubName,
    description: hubDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateHubInputSchema = z7.object({
  headUserId: id.nullable(),
  name: hubName,
  description: hubDescription
});
var UpdateHubInputSchema = z7.object({
  id,
  name: hubName.optional(),
  description: hubDescription.optional()
});
var GetCommunitiesInputSchema = z7.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  hubId: id
}).partial();
var GetCommunitiesOutputSchema = z7.array(
  z7.object({
    id,
    hub: z7.object({
      id,
      name: hubName,
      image: hubImage
    }).nullable(),
    headUser: SimpleUserSchema,
    image: communityImage,
    name: communityName,
    description: communityDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateCommunityInputSchema = z7.object({
  hubId: id.nullable(),
  headUserId: id.nullable(),
  name: communityName,
  description: communityDescription
});
var UpdateCommunityInputSchema = z7.object({
  id,
  name: communityName.optional(),
  description: communityDescription.optional()
});

// src/rating.ts
var rating_exports = {};
__export(rating_exports, {
  CreateUserFeedbackInputSchema: () => CreateUserFeedbackInputSchema,
  GetKarmaPointsInputSchema: () => GetKarmaPointsInputSchema,
  GetKarmaPointsOutputSchema: () => GetKarmaPointsOutputSchema,
  GetUserFeedbacksInputSchema: () => GetUserFeedbacksInputSchema,
  GetUserFeedbacksOutputSchema: () => GetUserFeedbacksOutputSchema,
  GetUserSummaryInputSchema: () => GetUserSummaryInputSchema,
  GetUserSummaryOutputSchema: () => GetUserSummaryOutputSchema,
  SpendKarmaPointInputSchema: () => SpendKarmaPointInputSchema,
  karmaPointComment: () => karmaPointComment,
  karmaPointQuantity: () => karmaPointQuantity,
  userFeedbackText: () => userFeedbackText,
  userFeedbackValue: () => userFeedbackValue
});
import { z as z8 } from "zod";
var karmaPointQuantity = z8.number().int();
var karmaPointComment = LocalizationsSchema.min(1);
var GetKarmaPointsInputSchema = z8.object({
  pagination: PaginationSchema,
  userId: id
});
var GetKarmaPointsOutputSchema = z8.array(
  z8.object({
    id,
    author: SimpleUserSchema,
    quantity: karmaPointQuantity,
    comment: karmaPointComment
  })
);
var SpendKarmaPointInputSchema = z8.object({
  sourceUserId: id,
  targetUserId: id,
  quantity: karmaPointQuantity,
  comment: karmaPointComment
});
var userFeedbackValue = z8.number().int().min(0).max(10);
var userFeedbackText = LocalizationsSchema.min(1);
var GetUserFeedbacksInputSchema = z8.object({
  pagination: PaginationSchema,
  userId: id
});
var GetUserFeedbacksOutputSchema = z8.array(
  z8.object({
    id,
    author: SimpleUserSchema.nullable(),
    isAnonymous: z8.boolean(),
    value: userFeedbackValue,
    text: userFeedbackText
  })
);
var CreateUserFeedbackInputSchema = z8.object({
  sourceUserId: id,
  targetUserId: id,
  value: userFeedbackValue,
  isAnonymous: z8.boolean(),
  text: userFeedbackText
});
var GetUserSummaryInputSchema = z8.object({
  userId: id
});
var GetUserSummaryOutputSchema = z8.object({
  rating: z8.number().int(),
  karma: z8.number().int(),
  rate: z8.number().min(0).max(10).nullable()
});

export {
  consts_exports,
  common_exports,
  sitemap_exports,
  user_exports,
  auth_exports,
  commune_exports,
  tag_exports,
  reactor_exports,
  rating_exports
};
//# sourceMappingURL=chunk-UDAIHPC2.mjs.map