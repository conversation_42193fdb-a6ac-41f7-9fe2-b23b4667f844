import{e as et,c as He}from"../chunks/CVTn1FV4.js";import{g as ze}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{o as tt}from"../chunks/DeAm3Eed.js";import{p as ut,av as N,aw as De,f as g,h as rt,t as p,b as m,c as at,s as o,d as u,g as e,$ as st,u as ne,r as t,ax as y,a as Y,az as ce}from"../chunks/RHWQbow4.js";import{d as it,s as d}from"../chunks/BlWcudmi.js";import{i as D}from"../chunks/CtoItwj4.js";import{e as Le}from"../chunks/Dnfvvefi.js";import{s as we}from"../chunks/BdpLTtcP.js";import{s as Te}from"../chunks/Cxg-bych.js";import{b as nt}from"../chunks/B5DcI8qy.js";import"../chunks/DiZKRWcx.js";import{f as Ue}from"../chunks/CL12WlkV.js";const ct=async({fetch:w,params:_,url:b})=>{const{fetcher:C}=ze(),[R,[T]]=await Promise.all([C.user.me.get({fetch:w,ctx:{url:b}}),C.commune.list.get({ids:[_.id]},{fetch:w,ctx:{url:b}})]);if(!T)throw et(404,"Commune not found");const M=(R==null?void 0:R.role)==="admin",s=R&&T.headMember.actorType==="user"&&T.headMember.actorId===R.id;if(!M&&!s)throw new Error("Access denied: You must be an admin or commune head to view join requests");const h=await C.commune.joinRequest.list.get({communeId:_.id},{fetch:w,ctx:{url:b}}),j=h.length?await C.user.list.get({ids:h.map(({userId:E})=>E)},{fetch:w,ctx:{url:b}}):[],k=new Map(j.map(E=>[E.id,E])),te=h.map(E=>({...E,user:k.get(E.userId)}));return{commune:T,joinRequests:te,isHasMoreJoinRequests:h.length===He.PAGE_SIZE,userPermissions:{isAdmin:M,isHeadMember:s,canManageJoinRequests:M||s}}},Wt=Object.freeze(Object.defineProperty({__proto__:null,load:ct},Symbol.toStringTag,{value:"Module"}));var ot=g('<div class="text-center py-5"><p class="text-muted"> </p></div>'),dt=g('<img alt="User avatar" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;"/>'),vt=g('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;"><i class="bi bi-person text-white"></i></div>'),lt=(w,_,b)=>_(e(b).id),mt=g('<span class="spinner-border spinner-border-sm me-1" role="status"></span> ',1),_t=(w,_,b)=>_(e(b).id),pt=g('<span class="spinner-border spinner-border-sm me-1" role="status"></span> ',1),gt=g('<div class="d-flex gap-1"><button class="btn btn-sm btn-success"><!></button> <button class="btn btn-sm btn-outline-danger"><!></button></div>'),ft=g('<span class="text-muted">—</span>'),bt=g('<tr><td><div class="d-flex align-items-center"><!> <div><div class="fw-medium"> </div></div></div></td><td><span> </span></td><td class="text-muted"> </td><td><!></td></tr>'),ht=g('<img alt="User avatar" class="rounded-circle me-3" style="width: 48px; height: 48px; object-fit: cover;"/>'),xt=g('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;"><i class="bi bi-person text-white"></i></div>'),At=(w,_,b)=>_(e(b).id),yt=g('<span class="spinner-border spinner-border-sm me-1" role="status"></span> ',1),Et=(w,_,b)=>_(e(b).id),Ft=g('<span class="spinner-border spinner-border-sm me-1" role="status"></span> ',1),Dt=g('<div class="d-flex gap-1"><button class="btn btn-sm btn-success"><!></button> <button class="btn btn-sm btn-outline-danger"><!></button></div>'),wt=g('<div class="col-12"><div class="card"><div class="card-body"><div class="d-flex align-items-start justify-content-between mb-3"><div class="d-flex align-items-center"><!> <div><div class="fw-medium"> </div></div></div> <span> </span></div> <div class="d-flex justify-content-between align-items-center"><small class="text-muted"> </small> <!></div></div></div></div>'),jt=g('<div class="d-none d-md-block"><div class="table-responsive"><table class="table table-hover"><thead><tr><th> </th><th> </th><th> </th><th> </th></tr></thead><tbody></tbody></table></div></div> <div class="d-md-none"><div class="row g-3"></div></div>',1),qt=g('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),Ct=g('<div class="text-center py-3"><!></div>'),Rt=g('<div class="alert alert-danger" role="alert"> </div>'),Bt=g('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><div><h1> </h1> <p class="text-muted mb-0"> </p></div> <a class="btn btn-outline-secondary"> </a></div> <!> <!> <!></div>');function Zt(w,_){ut(_,!0);const b={en:{_page:{title:"Join Requests — Commune"},communeJoinRequests:"Join Requests",loading:"Loading...",noJoinRequests:"No join requests found",errorFetchingJoinRequests:"Failed to fetch join requests",errorOccurred:"An error occurred while fetching join requests",loadingMore:"Loading more join requests...",accept:"Accept",reject:"Reject",pending:"Pending",accepted:"Accepted",rejected:"Rejected",requestedOn:"Requested on",acceptingRequest:"Accepting...",rejectingRequest:"Rejecting...",errorAcceptingRequest:"Failed to accept join request",errorRejectingRequest:"Failed to reject join request",requestAccepted:"Join request accepted",requestRejected:"Join request rejected",backToCommune:"Back to Commune",requestingUser:"Requesting User",status:"Status",actions:"Actions",confirmAccept:"Are you sure you want to accept this join request?",confirmReject:"Are you sure you want to reject this join request?"},ru:{_page:{title:"Заявки на вступление — Коммуна"},communeJoinRequests:"Заявки на вступление",loading:"Загрузка...",noJoinRequests:"Заявки не найдены",errorFetchingJoinRequests:"Не удалось загрузить заявки",errorOccurred:"Произошла ошибка при загрузке заявок",loadingMore:"Загружаем больше заявок...",accept:"Принять",reject:"Отклонить",pending:"Ожидает",accepted:"Принято",rejected:"Отклонено",requestedOn:"Подана",acceptingRequest:"Принимаем...",rejectingRequest:"Отклоняем...",errorAcceptingRequest:"Не удалось принять заявку",errorRejectingRequest:"Не удалось отклонить заявку",requestAccepted:"Заявка принята",requestRejected:"Заявка отклонена",backToCommune:"Назад к коммуне",requestingUser:"Пользователь",status:"Статус",actions:"Действия",confirmAccept:"Вы уверены, что хотите принять эту заявку?",confirmReject:"Вы уверены, что хотите отклонить эту заявку?"}},{fetcher:C}=ze(),R=ne(()=>_.data.locale),T=ne(()=>_.data.toLocaleHref),M=ne(()=>_.data.getAppropriateLocalization),s=ne(()=>b[e(R)]);let h=N(De(_.data.joinRequests)),j=N(null),k=N(!1),te=N(1),E=N(De(_.data.isHasMoreJoinRequests)),K=N(null),F=De({});async function Ie(){if(!(e(k)||!e(E))){y(k,!0),y(j,null);try{const r=e(te)+1,a=await C.commune.joinRequest.list.get({pagination:{page:r},communeId:_.data.commune.id}),x=a.length?await C.user.list.get({ids:a.map(A=>A.userId)}):[],B=new Map(x.map(A=>[A.id,A])),q=a.map(A=>({...A,user:B.get(A.userId)}));y(h,[...e(h),...q],!0),y(te,r),y(E,a.length===He.PAGE_SIZE)}catch(r){y(j,r instanceof Error?r.message:e(s).errorOccurred,!0),console.error(r)}finally{y(k,!1)}}}async function je(r){if(confirm(e(s).confirmAccept)){F[r]="accepting",y(j,null);try{await C.commune.joinRequest.accept.post({id:r}),y(h,e(h).map(a=>a.id===r?{...a,status:"accepted"}:a),!0),alert(e(s).requestAccepted)}catch(a){y(j,a instanceof Error?a.message:e(s).errorAcceptingRequest,!0),console.error(a)}finally{F[r]=null}}}async function qe(r){if(confirm(e(s).confirmReject)){F[r]="rejecting",y(j,null);try{await C.commune.joinRequest.reject.post({id:r}),y(h,e(h).map(a=>a.id===r?{...a,status:"rejected"}:a),!0),alert(e(s).requestRejected)}catch(a){y(j,a instanceof Error?a.message:e(s).errorRejectingRequest,!0),console.error(a)}finally{F[r]=null}}}tt(()=>{let r;const a=()=>{e(K)&&(r=new IntersectionObserver(x=>{x[0].isIntersecting&&e(E)&&!e(k)&&Ie()},{rootMargin:"100px",threshold:.1}),r.observe(e(K)))};return e(K)?a():setTimeout(a,100),()=>{r&&r.disconnect()}});function Ce(r){switch(r){case"pending":return"bg-warning text-dark";case"accepted":return"bg-success";case"rejected":return"bg-danger";default:return"bg-secondary"}}function Re(r){switch(r){case"pending":return e(s).pending;case"accepted":return e(s).accepted;case"rejected":return e(s).rejected;default:return r}}var oe=Bt();rt(r=>{p(()=>st.title=e(s)._page.title)});var de=u(oe),ve=u(de),le=u(ve),Ge=u(le,!0);t(le);var Be=o(le,2),We=u(Be,!0);t(Be),t(ve);var me=o(ve,2),Ze=u(me,!0);t(me),t(de);var Je=o(de,2);{var Ne=r=>{var a=ot(),x=u(a),B=u(x,!0);t(x),t(a),p(()=>d(B,e(s).noJoinRequests)),m(r,a)},Ye=r=>{var a=jt(),x=Y(a),B=u(x),q=u(B),A=u(q),U=u(A),H=u(U),_e=u(H,!0);t(H);var z=o(H),pe=u(z,!0);t(z);var ge=o(z),Xe=u(ge,!0);t(ge);var ke=o(ge),$e=u(ke,!0);t(ke),t(U),t(A);var Oe=o(A);Le(Oe,21,()=>e(h),O=>O.id,(O,n)=>{var P=bt(),I=u(P),Q=u(I),G=u(Q);{var V=i=>{var v=dt();p(()=>we(v,"src",`/images/${e(n).user.image}`)),m(i,v)},ue=i=>{var v=vt();m(i,v)};D(G,i=>{e(n).user.image?i(V):i(ue,!1)})}var re=o(G,2),ae=u(re),se=u(ae,!0);t(ae),t(re),t(Q),t(I);var W=o(I),X=u(W),$=u(X,!0);t(X),t(W);var ee=o(W),ie=u(ee,!0);t(ee);var Z=o(ee),fe=u(Z);{var be=i=>{var v=gt(),f=u(v);f.__click=[lt,je,n];var S=u(f);{var xe=c=>{var l=mt(),L=o(Y(l));p(()=>d(L,` ${e(s).acceptingRequest??""}`)),m(c,l)},Ae=c=>{var l=ce();p(()=>d(l,e(s).accept)),m(c,l)};D(S,c=>{F[e(n).id]==="accepting"?c(xe):c(Ae,!1)})}t(f);var J=o(f,2);J.__click=[_t,qe,n];var ye=u(J);{var Ee=c=>{var l=pt(),L=o(Y(l));p(()=>d(L,` ${e(s).rejectingRequest??""}`)),m(c,l)},Fe=c=>{var l=ce();p(()=>d(l,e(s).reject)),m(c,l)};D(ye,c=>{F[e(n).id]==="rejecting"?c(Ee):c(Fe,!1)})}t(J),t(v),p(()=>{f.disabled=F[e(n).id]==="accepting",J.disabled=F[e(n).id]==="rejecting"}),m(i,v)},he=i=>{var v=ft();m(i,v)};D(fe,i=>{e(n).status==="pending"?i(be):i(he,!1)})}t(Z),t(P),p((i,v,f,S)=>{d(se,i),Te(X,1,v),d($,f),d(ie,S)},[()=>e(M)(e(n).user.name),()=>`badge ${Ce(e(n).status)}`,()=>Re(e(n).status),()=>Ue(e(n).createdAt,e(R))]),m(O,P)}),t(Oe),t(q),t(B),t(x);var Pe=o(x,2),Se=u(Pe);Le(Se,21,()=>e(h),O=>O.id,(O,n)=>{var P=wt(),I=u(P),Q=u(I),G=u(Q),V=u(G),ue=u(V);{var re=i=>{var v=ht();p(()=>we(v,"src",`/images/${e(n).user.image}`)),m(i,v)},ae=i=>{var v=xt();m(i,v)};D(ue,i=>{e(n).user.image?i(re):i(ae,!1)})}var se=o(ue,2),W=u(se),X=u(W,!0);t(W),t(se),t(V);var $=o(V,2),ee=u($,!0);t($),t(G);var ie=o(G,2),Z=u(ie),fe=u(Z);t(Z);var be=o(Z,2);{var he=i=>{var v=Dt(),f=u(v);f.__click=[At,je,n];var S=u(f);{var xe=c=>{var l=yt(),L=o(Y(l));p(()=>d(L,` ${e(s).acceptingRequest??""}`)),m(c,l)},Ae=c=>{var l=ce();p(()=>d(l,e(s).accept)),m(c,l)};D(S,c=>{F[e(n).id]==="accepting"?c(xe):c(Ae,!1)})}t(f);var J=o(f,2);J.__click=[Et,qe,n];var ye=u(J);{var Ee=c=>{var l=Ft(),L=o(Y(l));p(()=>d(L,` ${e(s).rejectingRequest??""}`)),m(c,l)},Fe=c=>{var l=ce();p(()=>d(l,e(s).reject)),m(c,l)};D(ye,c=>{F[e(n).id]==="rejecting"?c(Ee):c(Fe,!1)})}t(J),t(v),p(()=>{f.disabled=F[e(n).id]==="accepting",J.disabled=F[e(n).id]==="rejecting"}),m(i,v)};D(be,i=>{e(n).status==="pending"&&i(he)})}t(ie),t(Q),t(I),t(P),p((i,v,f,S)=>{d(X,i),Te($,1,v),d(ee,f),d(fe,`${e(s).requestedOn??""}
                    ${S??""}`)},[()=>e(M)(e(n).user.name),()=>`badge ${Ce(e(n).status)}`,()=>Re(e(n).status),()=>Ue(e(n).createdAt,e(R))]),m(O,P)}),t(Se),t(Pe),p(()=>{d(_e,e(s).requestingUser),d(pe,e(s).status),d(Xe,e(s).requestedOn),d($e,e(s).actions)}),m(r,a)};D(Je,r=>{e(h).length===0?r(Ne):r(Ye,!1)})}var Me=o(Je,2);{var Ke=r=>{var a=Ct(),x=u(a);{var B=q=>{var A=qt(),U=Y(A),H=u(U),_e=u(H,!0);t(H),t(U);var z=o(U,2),pe=u(z,!0);t(z),p(()=>{d(_e,e(s).loadingMore),d(pe,e(s).loadingMore)}),m(q,A)};D(x,q=>{e(k)&&q(B)})}t(a),nt(a,q=>y(K,q),()=>e(K)),m(r,a)};D(Me,r=>{e(E)&&r(Ke)})}var Qe=o(Me,2);{var Ve=r=>{var a=Rt(),x=u(a,!0);t(a),p(()=>d(x,e(j))),m(r,a)};D(Qe,r=>{e(j)&&r(Ve)})}t(oe),p((r,a)=>{d(Ge,e(s).communeJoinRequests),d(We,r),we(me,"href",a),d(Ze,e(s).backToCommune)},[()=>e(M)(_.data.commune.name),()=>e(T)(`/communes/${_.data.commune.id}`)]),m(w,oe),at()}it(["click"]);export{Zt as component,Wt as universal};
