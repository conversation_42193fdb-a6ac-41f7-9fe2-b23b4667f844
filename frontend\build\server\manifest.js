const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png","images/default-avatar.png","images/full-v3-transparent-white.svg","images/full-v3-transparent.svg","images/home-page-main-image-mobile.png","images/home-page-main-image.png","tinymce/icons/default/icons.js","tinymce/icons/default/icons.min.js","tinymce/icons/default/index.js","tinymce/models/dom/index.js","tinymce/models/dom/model.js","tinymce/models/dom/model.min.js","tinymce/plugins/image/index.js","tinymce/plugins/image/plugin.js","tinymce/plugins/image/plugin.min.js","tinymce/plugins/lists/index.js","tinymce/plugins/lists/plugin.js","tinymce/plugins/lists/plugin.min.js","tinymce/skins/content/dark/content.css","tinymce/skins/content/dark/content.js","tinymce/skins/content/dark/content.min.css","tinymce/skins/content/default/content.css","tinymce/skins/content/default/content.js","tinymce/skins/content/default/content.min.css","tinymce/skins/content/document/content.css","tinymce/skins/content/document/content.js","tinymce/skins/content/document/content.min.css","tinymce/skins/content/tinymce-5/content.css","tinymce/skins/content/tinymce-5/content.js","tinymce/skins/content/tinymce-5/content.min.css","tinymce/skins/content/tinymce-5-dark/content.css","tinymce/skins/content/tinymce-5-dark/content.js","tinymce/skins/content/tinymce-5-dark/content.min.css","tinymce/skins/content/writer/content.css","tinymce/skins/content/writer/content.js","tinymce/skins/content/writer/content.min.css","tinymce/skins/ui/oxide/content.css","tinymce/skins/ui/oxide/content.inline.css","tinymce/skins/ui/oxide/content.inline.js","tinymce/skins/ui/oxide/content.inline.min.css","tinymce/skins/ui/oxide/content.js","tinymce/skins/ui/oxide/content.min.css","tinymce/skins/ui/oxide/skin.css","tinymce/skins/ui/oxide/skin.js","tinymce/skins/ui/oxide/skin.min.css","tinymce/skins/ui/oxide/skin.shadowdom.css","tinymce/skins/ui/oxide/skin.shadowdom.js","tinymce/skins/ui/oxide/skin.shadowdom.min.css","tinymce/skins/ui/oxide-dark/content.css","tinymce/skins/ui/oxide-dark/content.inline.css","tinymce/skins/ui/oxide-dark/content.inline.js","tinymce/skins/ui/oxide-dark/content.inline.min.css","tinymce/skins/ui/oxide-dark/content.js","tinymce/skins/ui/oxide-dark/content.min.css","tinymce/skins/ui/oxide-dark/skin.css","tinymce/skins/ui/oxide-dark/skin.js","tinymce/skins/ui/oxide-dark/skin.min.css","tinymce/skins/ui/oxide-dark/skin.shadowdom.css","tinymce/skins/ui/oxide-dark/skin.shadowdom.js","tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css","tinymce/skins/ui/tinymce-5/content.css","tinymce/skins/ui/tinymce-5/content.inline.css","tinymce/skins/ui/tinymce-5/content.inline.js","tinymce/skins/ui/tinymce-5/content.inline.min.css","tinymce/skins/ui/tinymce-5/content.js","tinymce/skins/ui/tinymce-5/content.min.css","tinymce/skins/ui/tinymce-5/skin.css","tinymce/skins/ui/tinymce-5/skin.js","tinymce/skins/ui/tinymce-5/skin.min.css","tinymce/skins/ui/tinymce-5/skin.shadowdom.css","tinymce/skins/ui/tinymce-5/skin.shadowdom.js","tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css","tinymce/skins/ui/tinymce-5-dark/content.css","tinymce/skins/ui/tinymce-5-dark/content.inline.css","tinymce/skins/ui/tinymce-5-dark/content.inline.js","tinymce/skins/ui/tinymce-5-dark/content.inline.min.css","tinymce/skins/ui/tinymce-5-dark/content.js","tinymce/skins/ui/tinymce-5-dark/content.min.css","tinymce/skins/ui/tinymce-5-dark/skin.css","tinymce/skins/ui/tinymce-5-dark/skin.js","tinymce/skins/ui/tinymce-5-dark/skin.min.css","tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.css","tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.js","tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css","tinymce/themes/silver/index.js","tinymce/themes/silver/theme.js","tinymce/themes/silver/theme.min.js","tinymce/tinymce.min.js"]),
	mimeTypes: {".png":"image/png",".svg":"image/svg+xml",".js":"text/javascript",".css":"text/css"},
	_: {
		client: {start:"_app/immutable/entry/start.mDrnrnPv.js",app:"_app/immutable/entry/app.VoR2INgK.js",imports:["_app/immutable/entry/start.mDrnrnPv.js","_app/immutable/chunks/KKeKRB0S.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/entry/app.VoR2INgK.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/CR3e0W7L.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./chunks/0-D6uiBvlI.js')),
			__memo(() => import('./chunks/1-Dll3-PT4.js')),
			__memo(() => import('./chunks/2-DoAE_oue.js')),
			__memo(() => import('./chunks/3-BeHfmddr.js')),
			__memo(() => import('./chunks/4-BtA2vuD2.js')),
			__memo(() => import('./chunks/5-BnpIy87C.js')),
			__memo(() => import('./chunks/6-BLGt0gll.js')),
			__memo(() => import('./chunks/7-BEj5OC8D.js')),
			__memo(() => import('./chunks/8-Bu_9a_kr.js')),
			__memo(() => import('./chunks/9-DXY335kv.js')),
			__memo(() => import('./chunks/10-DTfXx7OV.js')),
			__memo(() => import('./chunks/11-C05_NXVH.js')),
			__memo(() => import('./chunks/12-BEtKNiNX.js')),
			__memo(() => import('./chunks/13-BiOz8lgu.js')),
			__memo(() => import('./chunks/14-CG0yQ_dX.js')),
			__memo(() => import('./chunks/15-D5JOuB-k.js')),
			__memo(() => import('./chunks/16-DtiAq9Tv.js')),
			__memo(() => import('./chunks/17-CS5bWQPO.js')),
			__memo(() => import('./chunks/18-BATQg0v8.js')),
			__memo(() => import('./chunks/19-CJuQXQWr.js')),
			__memo(() => import('./chunks/20-CVr22--2.js')),
			__memo(() => import('./chunks/21-C_aHu0Q8.js')),
			__memo(() => import('./chunks/22-muSY4O7R.js')),
			__memo(() => import('./chunks/23-DtzwQQqo.js')),
			__memo(() => import('./chunks/24-B_arxhQ4.js')),
			__memo(() => import('./chunks/25-bO2pmhUg.js')),
			__memo(() => import('./chunks/26-1t3Ldck9.js')),
			__memo(() => import('./chunks/27-DKc1qha2.js')),
			__memo(() => import('./chunks/28-uAZNemYg.js')),
			__memo(() => import('./chunks/29-DqQo6CQg.js')),
			__memo(() => import('./chunks/30-BFz4PVq0.js')),
			__memo(() => import('./chunks/31-B5QoNcUf.js')),
			__memo(() => import('./chunks/32-BqMdZfkA.js'))
		],
		routes: [
			{
				id: "/admin",
				pattern: /^\/admin\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 6 },
				endpoint: null
			},
			{
				id: "/admin/invites",
				pattern: /^\/admin\/invites\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 7 },
				endpoint: null
			},
			{
				id: "/api/[...slug]",
				pattern: /^\/api(?:\/(.*))?\/?$/,
				params: [{"name":"slug","optional":false,"rest":true,"chained":true}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-e-UuZ_Uf.js'))
			},
			{
				id: "/[[locale]]/auth",
				pattern: /^(?:\/([^/]+))?\/auth\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 24 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes",
				pattern: /^(?:\/([^/]+))?\/communes\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 9 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/invitations",
				pattern: /^(?:\/([^/]+))?\/communes\/invitations\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 10 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/join-requests",
				pattern: /^(?:\/([^/]+))?\/communes\/join-requests\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 11 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/[id]",
				pattern: /^(?:\/([^/]+))?\/communes\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 12 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/[id]/invitations",
				pattern: /^(?:\/([^/]+))?\/communes\/([^/]+?)\/invitations\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 13 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/[id]/join-requests",
				pattern: /^(?:\/([^/]+))?\/communes\/([^/]+?)\/join-requests\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 14 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/new-calendar",
				pattern: /^(?:\/([^/]+))?\/new-calendar\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 15 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/new-english",
				pattern: /^(?:\/([^/]+))?\/new-english\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 16 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/profile",
				pattern: /^(?:\/([^/]+))?\/profile\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 17 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor",
				pattern: /^(?:\/([^/]+))?\/reactor\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 25 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/communities",
				pattern: /^(?:\/([^/]+))?\/reactor\/communities\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 26 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/communities/[id]",
				pattern: /^(?:\/([^/]+))?\/reactor\/communities\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 27 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/hubs",
				pattern: /^(?:\/([^/]+))?\/reactor\/hubs\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 28 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/hubs/[id]",
				pattern: /^(?:\/([^/]+))?\/reactor\/hubs\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 29 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/[id]",
				pattern: /^(?:\/([^/]+))?\/reactor\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 30 },
				endpoint: null
			},
			{
				id: "/robots.txt",
				pattern: /^\/robots\.txt\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CqQwnGzg.js'))
			},
			{
				id: "/[[locale]]/(index)/rules",
				pattern: /^(?:\/([^/]+))?\/rules\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 18 },
				endpoint: null
			},
			{
				id: "/sitemap.xml",
				pattern: /^\/sitemap\.xml\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BtlrEslv.js'))
			},
			{
				id: "/[[locale]]/test/editor",
				pattern: /^(?:\/([^/]+))?\/test\/editor\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 31 },
				endpoint: null
			},
			{
				id: "/[[locale]]/test/tag",
				pattern: /^(?:\/([^/]+))?\/test\/tag\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 32 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/the-law",
				pattern: /^(?:\/([^/]+))?\/the-law\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 19 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users",
				pattern: /^(?:\/([^/]+))?\/users\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 20 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users/[id]",
				pattern: /^(?:\/([^/]+))?\/users\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 21 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users/[id]/feedback",
				pattern: /^(?:\/([^/]+))?\/users\/([^/]+?)\/feedback\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 22 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users/[id]/karma",
				pattern: /^(?:\/([^/]+))?\/users\/([^/]+?)\/karma\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 23 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)",
				pattern: /^(?:\/([^/]+))?\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 8 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();

const prerendered = new Set([]);

const base = "";

export { base, manifest, prerendered };
//# sourceMappingURL=manifest.js.map
