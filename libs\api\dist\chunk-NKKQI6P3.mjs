// src/acrpc/core.ts
import "zod";
import superjson from "superjson";
import {
  CaseTransformer,
  KebabCaseStrategy,
  UnknownCaseStrategy
} from "@ocelotjungle/case-converters";
function log(...args) {
}
function dir(...args) {
}
var kebabTransformer = new CaseTransformer(
  new UnknownCaseStrategy(),
  new KebabCaseStrategy()
);
var methods = [
  "get",
  "post",
  "put",
  "patch",
  "delete"
];
var jsonTransformer = {
  serialize: JSON.stringify,
  deserialize: JSON.parse
};
var superjsonTransformer = {
  serialize: superjson.stringify,
  deserialize: superjson.parse
};

export {
  log,
  dir,
  kebabTransformer,
  methods,
  jsonTransformer,
  superjsonTransformer
};
//# sourceMappingURL=chunk-NKKQI6P3.mjs.map