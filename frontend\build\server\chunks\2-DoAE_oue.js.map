{"version": 3, "file": "2-DoAE_oue.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/admin/_layout.ts.js", "../../../.svelte-kit/adapter-node/nodes/2.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { g as getClient } from \"../../../chunks/acrpc.js\";\nconst load = async ({ fetch, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    me\n  ] = await Promise.all([\n    api.user.me.get({ fetch, ctx: { url } })\n  ]);\n  if (me.role !== \"admin\") {\n    throw error(403, \"Access denied: Admin privileges required\");\n  }\n  return {\n    me\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/admin/_layout.ts.js';\n\nexport const index = 2;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/admin/_layout.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/admin/+layout.ts\";\nexport const imports = [\"_app/immutable/nodes/2.DBQkNEqf.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\"];\nexport const stylesheets = [\"_app/immutable/assets/2.DuFVuScv.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI;AACJ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AAC3C,GAAG,CAAC;AACJ,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,OAAO,EAAE;AAC3B,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,0CAA0C,CAAC;AAChE,EAAE;AACF,EAAE,OAAO;AACT,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;ACbW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,8BAA0C,CAAC,EAAE;AAExG,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC5S,MAAC,WAAW,GAAG,CAAC,sCAAsC;AACtD,MAAC,KAAK,GAAG;;;;"}