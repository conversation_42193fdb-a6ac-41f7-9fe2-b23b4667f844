{"version": 3, "file": "17-CS5bWQPO.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/profile/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/17.js"], "sourcesContent": ["import { g as getClient } from \"../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, url }) => {\n  const { fetcher: api } = getClient();\n  const me = await api.user.me.get({ fetch, ctx: { url } });\n  return { me };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/profile/_page.ts.js';\n\nexport const index = 17;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/profile/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/profile/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/17.CHdhlmr2.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/CaC9IHEK.js\",\"_app/immutable/chunks/KKeKRB0S.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/DiZKRWcx.js\",\"_app/immutable/chunks/CL12WlkV.js\",\"_app/immutable/chunks/CBe4EX5h.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/CR3e0W7L.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/BiLRrsV0.js\"];\nexport const stylesheets = [\"_app/immutable/assets/create-post-modal.BRelZfpq.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC3D,EAAE,OAAO,EAAE,EAAE,EAAE;AACf,CAAC;;;;;;;ACHW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA6D,CAAC,EAAE;AAE3H,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACryB,MAAC,WAAW,GAAG,CAAC,sDAAsD;AACtE,MAAC,KAAK,GAAG;;;;"}