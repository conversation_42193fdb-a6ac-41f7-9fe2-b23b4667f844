import { c as common_exports } from './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import { match } from '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import Negotiator from 'negotiator';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './schema-CmMg_B_X.js';

const DEFAULT_LOCALE = "en";
function getAppropriateLocalizationFactory(routeLocale, userLocales) {
  const requestedLocales = routeLocale ? [routeLocale, ...userLocales] : userLocales;
  function getAppropriateLocalization(localizations) {
    const availableLocales = localizations.map((localization) => localization.locale);
    try {
      const matchedLocale = match(requestedLocales, availableLocales, DEFAULT_LOCALE);
      const matchedLocalization = localizations.find(
        (localization) => localization.locale === matchedLocale
      );
      return matchedLocalization?.value ?? localizations[0]?.value ?? null;
    } catch (error) {
      console.error("Error during locale negotiation:", error);
      return null;
    }
  }
  return getAppropriateLocalization;
}
const load$1 = (event) => {
  const me = event.data.me;
  const user = me ? {
    id: me.id,
    email: me.email,
    role: me.role
  } : null;
  const routeLocale = getRouteLocale(event);
  const hrefLocale = routeLocale ? `/${routeLocale}` : "";
  const locale = routeLocale ?? event.data.preferredLocale ?? "en";
  return {
    routeLocale,
    preferredLocale: event.data.preferredLocale,
    locale,
    user,
    toLocaleHref(href) {
      return `${hrefLocale}${href}`;
    },
    getAppropriateLocalization: getAppropriateLocalizationFactory(
      routeLocale,
      event.data.userLocales
    )
  };
};
function getRouteLocale(event) {
  const parsedLocale = common_exports.WebsiteLocaleSchema.safeParse(event.params.locale);
  if (parsedLocale.success) {
    return parsedLocale.data;
  }
  return null;
}

var _layout_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load$1
});

const load = async (event) => {
  const { fetcher: api } = getClient();
  const me = await api.user.me.get({ fetch: event.fetch, skipInterceptor: true }).catch(() => null);
  const userLocales = getUserLocales(event);
  const preferredLocale = getPreferredLocale(event);
  return {
    me,
    preferredLocale,
    userLocales
  };
};
function getUserLocales(event) {
  const acceptLanguage = event.request.headers.get("accept-language");
  if (acceptLanguage) {
    const negotiatorRequest = {
      headers: {
        "accept-language": acceptLanguage
      }
    };
    const preferredLanguages = new Negotiator(negotiatorRequest).languages();
    return preferredLanguages;
  }
  return [];
}
function getPreferredLocale(event) {
  const userLocales = getUserLocales(event);
  return match(
    userLocales,
    common_exports.WebsiteLocaleSchema._def.values,
    "en"
  );
}

var _layout_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 3;
let component_cache;
const component = async () => component_cache ??= (await import('./layout.svelte-BSvrn-4a.js')).default;
const universal_id = "src/routes/[[locale]]/+layout.ts";
const server_id = "src/routes/[[locale]]/+layout.server.ts";
const imports = ["_app/immutable/nodes/3.CIONd69m.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/DiZKRWcx.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/Bzak7iHL.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets, _layout_ts as universal, universal_id };
//# sourceMappingURL=3-BeHfmddr.js.map
