{"version": 3, "file": "_server.ts-BtlrEslv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/sitemap.xml/_server.ts.js"], "sourcesContent": ["import { A as API_URL } from \"../../../chunks/private.js\";\nconst locales = [\"en\", \"ru\"];\nconst url = \"https://commune.my\";\nconst apiUrl = API_URL;\nfunction getXhtmlLink(locale, route) {\n  return `<xhtml:link rel=\"alternate\" hreflang=\"${locale === null ? \"x-default\" : locale}\" href=\"${url}${locale ? `/${locale}` : \"\"}/${route}\" />`;\n}\nfunction getUrl(...routes) {\n  const route = routes.join(\"/\");\n  return `\n<url>\n\t<loc>${url}/${route}</loc>\n\t${locales.map((locale) => getXhtmlLink(locale, route)).join(\"\\n\")}\n\t${getXhtmlLink(null, route)}\n</url>\n\t\t`.trim();\n}\nfunction generateBasicPages() {\n  const routes = [\n    \"\",\n    \"the-law\",\n    \"rules\",\n    \"new-english\",\n    \"new-calendar\"\n  ];\n  return routes.map((route) => getUrl(route));\n}\nfunction generateCommunePages(ids) {\n  return ids.map((id) => getUrl(\"communes\", id));\n}\nfunction generateReactorPostPages(ids) {\n  return ids.map((id) => getUrl(\"reactor\", id));\n}\nfunction generateReactorHubPages(ids) {\n  return ids.map((id) => getUrl(\"reactor\", \"hubs\", id));\n}\nfunction generateReactorCommunityPages(ids) {\n  return ids.map((id) => getUrl(\"reactor\", \"communities\", id));\n}\nfunction generateSitemap(data) {\n  const lines = [\n    '<?xml version=\"1.0\" encoding=\"UTF-8\" ?>',\n    '<urlset xmlns=\"https://www.sitemaps.org/schemas/sitemap/0.9\" xmlns:xhtml=\"https://www.w3.org/1999/xhtml\">',\n    ...generateBasicPages(),\n    ...generateCommunePages(data.communeIds),\n    ...generateReactorPostPages(data.reactorPostIds),\n    ...generateReactorHubPages(data.reactorHubIds),\n    ...generateReactorCommunityPages(data.reactorCommunityIds),\n    \"</urlset>\"\n  ];\n  return lines.join(\"\\n\");\n}\nconst SITEMAP_KEY = \"BycS9tEQKMvasaJGni12BUBVl5xl1E7fsGqocvG5xKe5es3XWlrMXeRGYvJ7r3iS\";\nasync function GET() {\n  const sitemapGenerationData = await fetch(\n    `${apiUrl}/sitemap/generation-data?key=${SITEMAP_KEY}`\n  ).then((res) => res.json());\n  console.dir({\n    sitemapGenerationData\n  }, { depth: null });\n  const sitemap = generateSitemap(sitemapGenerationData);\n  return new Response(\n    sitemap,\n    {\n      headers: {\n        \"Content-Type\": \"application/xml\"\n      }\n    }\n  );\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;AACA,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AAC5B,MAAM,GAAG,GAAG,oBAAoB;AAChC,MAAM,MAAM,GAAG,OAAO;AACtB,SAAS,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE;AACrC,EAAE,OAAO,CAAC,sCAAsC,EAAE,MAAM,KAAK,IAAI,GAAG,WAAW,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC;AAClJ;AACA,SAAS,MAAM,CAAC,GAAG,MAAM,EAAE;AAC3B,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AAChC,EAAE,OAAO;AACT;AACA,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;AACrB,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAClE,CAAC,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;AAC5B;AACA,EAAE,CAAC,CAAC,IAAI,EAAE;AACV;AACA,SAAS,kBAAkB,GAAG;AAC9B,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,EAAE;AACN,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,aAAa;AACjB,IAAI;AACJ,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7C;AACA,SAAS,oBAAoB,CAAC,GAAG,EAAE;AACnC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAChD;AACA,SAAS,wBAAwB,CAAC,GAAG,EAAE;AACvC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AAC/C;AACA,SAAS,uBAAuB,CAAC,GAAG,EAAE;AACtC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;AACvD;AACA,SAAS,6BAA6B,CAAC,GAAG,EAAE;AAC5C,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;AAC9D;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,yCAAyC;AAC7C,IAAI,2GAA2G;AAC/G,IAAI,GAAG,kBAAkB,EAAE;AAC3B,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,GAAG,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC;AACpD,IAAI,GAAG,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC;AAClD,IAAI,GAAG,6BAA6B,CAAC,IAAI,CAAC,mBAAmB,CAAC;AAC9D,IAAI;AACJ,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACzB;AACA,MAAM,WAAW,GAAG,kEAAkE;AACtF,eAAe,GAAG,GAAG;AACrB,EAAE,MAAM,qBAAqB,GAAG,MAAM,KAAK;AAC3C,IAAI,CAAC,EAAE,MAAM,CAAC,6BAA6B,EAAE,WAAW,CAAC;AACzD,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC;AAC7B,EAAE,OAAO,CAAC,GAAG,CAAC;AACd,IAAI;AACJ,GAAG,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AACrB,EAAE,MAAM,OAAO,GAAG,eAAe,CAAC,qBAAqB,CAAC;AACxD,EAAE,OAAO,IAAI,QAAQ;AACrB,IAAI,OAAO;AACX,IAAI;AACJ,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB;AACA;AACA,GAAG;AACH;;;;"}