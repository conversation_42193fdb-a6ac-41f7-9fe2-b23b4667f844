import express from "express";
import { handler } from "./build/handler.js"; // SvelteKit’s build output
import { createProxyMiddleware } from "http-proxy-middleware";

const app = express();

app.use(
  "/images",
  createProxyMiddleware({
    target: `http://${process.env.MINIO_HOST}:9000`,
    changeOrigin: true,
    pathRewrite: { "^/images": "" },
  }),
);

app.use(
  "/api",
  createProxyMiddleware({
    target: "http://localhost:4000",
    changeOrigin: true,
    pathRewrite: { "^/api": "" },
  }),
);

// Fallback to SvelteKit for all other routes
app.use(handler);

const port = process.env.PORT || 3000;

app.listen(port, () => {
  console.log(`Production server listening on port ${port}`);
});
