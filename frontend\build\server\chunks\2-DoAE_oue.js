import { e as error } from './index-CT944rr3.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './schema-CmMg_B_X.js';
import './current-user-BM0W6LNm.js';

const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const [
    me
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } })
  ]);
  if (me.role !== "admin") {
    throw error(403, "Access denied: Admin privileges required");
  }
  return {
    me
  };
};

var _layout_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 2;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-C4COQFSV.js')).default;
const universal_id = "src/routes/admin/+layout.ts";
const imports = ["_app/immutable/nodes/2.DBQkNEqf.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CSZ3sDel.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js"];
const stylesheets = ["_app/immutable/assets/2.DuFVuScv.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _layout_ts as universal, universal_id };
//# sourceMappingURL=2-DoAE_oue.js.map
