{"version": 3, "file": "20-CVr22--2.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/users/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/20.js"], "sourcesContent": ["import { a as consts_exports } from \"../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, url }) => {\n  const { fetcher: api } = getClient();\n  const users = await api.user.list.get({}, { fetch, ctx: { url } });\n  return {\n    users,\n    isHasMoreUsers: users.length === consts_exports.PAGE_SIZE\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/users/_page.ts.js';\n\nexport const index = 20;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/users/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/users/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/20.Cngeoin8.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/B5DcI8qy.js\"];\nexport const stylesheets = [\"_app/immutable/assets/20.CPoQ0XrN.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AACpE,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,cAAc,EAAE,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC;AACpD,GAAG;AACH,CAAC;;;;;;;ACPW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA2D,CAAC,EAAE;AAEzH,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACje,MAAC,WAAW,GAAG,CAAC,uCAAuC;AACvD,MAAC,KAAK,GAAG;;;;"}