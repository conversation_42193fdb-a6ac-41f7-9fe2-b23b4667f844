import{c as V}from"../chunks/CVTn1FV4.js";import{g as W}from"../chunks/CSZ3sDel.js";import"../chunks/Bzak7iHL.js";import{o as ce}from"../chunks/DeAm3Eed.js";import{p as ve,av as E,aw as R,f as d,h as me,t as g,b as l,c as ge,s as x,d as a,g as e,$ as fe,u as j,r,ax as p,a as _e}from"../chunks/RHWQbow4.js";import{s as f}from"../chunks/BlWcudmi.js";import{i as b}from"../chunks/CtoItwj4.js";import{e as pe}from"../chunks/Dnfvvefi.js";import{s as G}from"../chunks/BdpLTtcP.js";import{b as he}from"../chunks/B5DcI8qy.js";const xe=async({fetch:L,url:_})=>{const{fetcher:O}=W(),w=await O.user.list.get({},{fetch:L,ctx:{url:_}});return{users:w,isHasMoreUsers:w.length===V.PAGE_SIZE}},Ge=Object.freeze(Object.defineProperty({__proto__:null,load:xe},Symbol.toStringTag,{value:"Module"}));var Ee=d('<div class="text-center py-5"><p class="text-muted"> </p></div>'),be=d('<div style="height: 140px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"><img style="width: 100%; height: 100%; object-fit: cover;"/></div>'),ye=d('<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted"> </span></div>'),Be=d('<span class="badge bg-danger"> </span>'),Fe=d('<div class="col"><div class="card h-100 shadow-sm hover-card svelte-1g1c3uj"><a class="text-decoration-none text-black"><!> <div class="card-body d-flex flex-column"><h5 class="card-title fs-5 text-truncate"> </h5> <p class="card-text text-muted small" style="height: 3rem; overflow: hidden"> </p> <div class="mt-auto"><div class="small text-muted"><!></div></div></div></a></div></div>'),Ce=d('<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4"></div>'),we=d('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),Ae=d('<div class="text-center py-3"><!></div>'),Ue=d('<div class="alert alert-danger" role="alert"> </div>'),Me=d('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><h1> </h1></div> <!> <!> <!></div>');function Ne(L,_){ve(_,!0);const O={en:{_page:{title:"Users — Commune"},users:"Users",loading:"Loading...",noUsersFound:"No users found",errorFetchingUsers:"Failed to fetch users",errorOccurred:"An error occurred while fetching users",loadingMore:"Loading more users...",noImage:"No image",userImageAlt:"User image",role:{admin:"Admin"}},ru:{_page:{title:"Пользователи — Коммуна"},users:"Пользователи",loading:"Загрузка...",noUsersFound:"Пользователи не найдены",errorFetchingUsers:"Не удалось загрузить пользователей",errorOccurred:"Произошла ошибка при загрузке пользователей",loadingMore:"Загружаем больше пользователей...",noImage:"Нет изображения",userImageAlt:"Изображение пользователя",role:{admin:"Админ"}}},{fetcher:w}=W(),X=j(()=>_.data.locale),Y=j(()=>_.data.toLocaleHref),N=j(()=>_.data.getAppropriateLocalization),c=j(()=>O[e(X)]);let A=E(R(_.data.users)),U=E(null),y=E(!1),T=E(1),M=E(R(_.data.isHasMoreUsers)),B=E(null);async function $(){if(!(e(y)||!e(M))){p(y,!0),p(U,null);try{const u=e(T)+1,t=await w.user.list.get({pagination:{page:u}});p(A,[...e(A),...t],!0),p(T,u),p(M,t.length===V.PAGE_SIZE)}catch(u){p(U,u instanceof Error?u.message:e(c).errorOccurred,!0),console.error(u)}finally{p(y,!1)}}}ce(()=>{let u;const t=()=>{e(B)&&(u=new IntersectionObserver(o=>{o[0].isIntersecting&&e(M)&&!e(y)&&$()},{rootMargin:"100px",threshold:.1}),u.observe(e(B)))};return e(B)?t():setTimeout(t,100),()=>{u&&u.disconnect()}});var H=Me();me(u=>{g(()=>fe.title=e(c)._page.title)});var P=a(H),Z=a(P),ee=a(Z,!0);r(Z),r(P);var q=x(P,2);{var ue=u=>{var t=Ee(),o=a(t),n=a(o,!0);r(o),r(t),g(()=>f(n,e(c).noUsersFound)),l(u,t)},re=u=>{var t=Ce();pe(t,21,()=>e(A),o=>o.id,(o,n)=>{var v=Fe(),F=a(v),h=a(F),C=a(h);{var S=i=>{var s=be(),m=a(s);r(s),g(()=>{G(m,"src",`/images/${e(n).image}`),G(m,"alt",`${e(c).userImageAlt}`)}),l(i,s)},I=i=>{var s=ye(),m=a(s),de=a(m,!0);r(m),r(s),g(()=>f(de,e(c).noImage)),l(i,s)};b(C,i=>{e(n).image?i(S):i(I,!1)})}var D=x(C,2),k=a(D),oe=a(k,!0);r(k);var z=x(k,2),ie=a(z,!0);r(z);var K=x(z,2),Q=a(K),ne=a(Q);{var le=i=>{var s=Be(),m=a(s,!0);r(s),g(()=>f(m,e(c).role.admin)),l(i,s)};b(ne,i=>{e(n).role==="admin"&&i(le)})}r(Q),r(K),r(D),r(h),r(F),r(v),g((i,s,m)=>{G(h,"href",i),f(oe,s),f(ie,m)},[()=>e(Y)(`/users/${e(n).id}`),()=>e(N)(e(n).name),()=>e(N)(e(n).description)||""]),l(o,v)}),r(t),l(u,t)};b(q,u=>{e(A).length===0?u(ue):u(re,!1)})}var J=x(q,2);{var te=u=>{var t=Ae(),o=a(t);{var n=v=>{var F=we(),h=_e(F),C=a(h),S=a(C,!0);r(C),r(h);var I=x(h,2),D=a(I,!0);r(I),g(()=>{f(S,e(c).loadingMore),f(D,e(c).loadingMore)}),l(v,F)};b(o,v=>{e(y)&&v(n)})}r(t),he(t,v=>p(B,v),()=>e(B)),l(u,t)};b(J,u=>{e(M)&&u(te)})}var ae=x(J,2);{var se=u=>{var t=Ue(),o=a(t,!0);r(t),g(()=>f(o,e(U))),l(u,t)};b(ae,u=>{e(U)&&u(se)})}r(H),g(()=>f(ee,e(c).users)),l(L,H),ge()}export{Ne as component,Ge as universal};
