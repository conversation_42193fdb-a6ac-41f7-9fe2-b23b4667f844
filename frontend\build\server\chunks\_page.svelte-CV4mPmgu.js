import { u as push, Q as copy_payload, T as assign_payload, w as pop, x as head, z as escape_html, y as attr, N as ensure_array_like, G as attr_style, K as stringify } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { g as getUserRateColor } from './get-user-rate-color-CzjBgne7.js';
import './schema-CmMg_B_X.js';

/* empty css                                                                                 */
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Feedback — Commune" },
      userNotFound: "User not found",
      feedbackHistory: "Feedback History",
      sendFeedback: "Send Feedback",
      feedbackModalTitle: "Send Feedback",
      rating: "Rating",
      anonymous: "Anonymous",
      sendAnonymously: "Send anonymously",
      feedback: "Feedback",
      feedbackPlaceholder: "Enter your feedback...",
      cancel: "Cancel",
      submit: "Submit",
      submitting: "Submitting...",
      success: "Feedback sent successfully",
      errorSubmitting: "Error submitting feedback",
      noFeedbacks: "No feedback found",
      loadingMore: "Loading more...",
      errorOccurred: "An error occurred",
      errorFetchingFeedbacks: "Error fetching feedbacks",
      ratingRequired: "Rating is required",
      feedbackRequired: "Feedback text is required"
    },
    ru: {
      _page: {
        title: "Отзывы — Коммуна"
      },
      userNotFound: "Пользователь не найден",
      feedbackHistory: "История отзывов",
      sendFeedback: "Отправить отзыв",
      feedbackModalTitle: "Отправить отзыв",
      rating: "Оценка",
      anonymous: "Анонимно",
      sendAnonymously: "Отправить анонимно",
      feedback: "Отзыв",
      feedbackPlaceholder: "Введите ваш отзыв...",
      cancel: "Отмена",
      submit: "Отправить",
      submitting: "Отправка...",
      success: "Отзыв успешно отправлен",
      errorSubmitting: "Ошибка при отправке отзыва",
      noFeedbacks: "Отзывы не найдены",
      loadingMore: "Загрузка...",
      errorOccurred: "Произошла ошибка",
      errorFetchingFeedbacks: "Ошибка загрузки отзывов",
      ratingRequired: "Оценка обязательна",
      feedbackRequired: "Текст отзыва обязателен"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { me, user, locale, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let feedbacks = data.feedbacks;
  let isHasMoreFeedbacks = data.isHasMoreFeedbacks;
  const userName = getAppropriateLocalization(user.name);
  const getAuthorDisplayName = (author) => {
    if (!author) return t.anonymous;
    return getAppropriateLocalization(author.name);
  };
  const getStarDisplay = (value) => {
    return "★".repeat(Math.floor(value / 2)) + (value % 2 === 1 ? "☆" : "");
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(userName)} ${escape_html(t._page.title)}</title>`;
    });
    $$payload2.out.push(`<div class="container py-4"><div class="responsive-container">`);
    if (!user) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="alert alert-danger" role="alert">${escape_html(t.userNotFound)}</div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="d-flex justify-content-between align-items-center mb-4"><div><h2 class="mb-1">${escape_html(userName)}</h2> <p class="text-muted mb-0">${escape_html(t.feedbackHistory)}</p></div> `);
      if (me.id !== user.id) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div><button class="btn btn-primary btn-sm"${attr("aria-label", t.sendFeedback)}><i class="bi bi-chat-dots me-1"></i> ${escape_html(t.sendFeedback)}</button></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div> `);
      if (feedbacks.length === 0) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noFeedbacks)}</p></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
        const each_array = ensure_array_like(feedbacks);
        $$payload2.out.push(`<!--[-->`);
        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
          let feedback = each_array[$$index];
          $$payload2.out.push(`<div class="card mb-3 shadow-sm svelte-s9if29"><div class="card-body"><div class="d-flex align-items-start"><div class="me-3">`);
          if (feedback.author?.image) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<img${attr("src", `/images/${feedback.author.image}`)}${attr("alt", getAuthorDisplayName(feedback.author))} class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
            $$payload2.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white" style="width: 48px; height: 48px;"><i class="bi bi-person-fill"></i></div>`);
          }
          $$payload2.out.push(`<!--]--></div> <div class="flex-grow-1"><div class="d-flex justify-content-between align-items-start mb-2"><div><h6 class="mb-1">${escape_html(getAuthorDisplayName(feedback.author))}</h6></div> <div class="d-flex align-items-center gap-2"><span class="badge fs-6"${attr_style(`color: ${stringify(getUserRateColor(feedback.value))}; border: 1px solid ${stringify(getUserRateColor(feedback.value))};`)}>${escape_html(feedback.value)}/10</span> <span${attr_style(`font-size: 1.2rem; color: ${stringify(getUserRateColor(feedback.value))};`)}>${escape_html(getStarDisplay(feedback.value))}</span></div></div> <p class="mb-0 text-muted">${escape_html(getAppropriateLocalization(feedback.text))}</p></div></div></div></div>`);
        }
        $$payload2.out.push(`<!--]-->`);
      }
      $$payload2.out.push(`<!--]--> `);
      if (isHasMoreFeedbacks) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="text-center py-3">`);
        {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]--></div> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CV4mPmgu.js.map
