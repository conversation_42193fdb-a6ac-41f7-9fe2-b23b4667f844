import { F as attr_style, G as attr_class, z as escape_html, y as attr, w as pop, u as push } from "../../../../chunks/index.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/client2.js";
import "../../../../chunks/current-user.js";
import "@formatjs/intl-localematcher";
import "@sveltejs/kit";
import { g as getClient } from "../../../../chunks/acrpc.js";
function _page($$payload, $$props) {
  push();
  const { fetcher: api } = getClient();
  const i18n = {
    en: {
      _page: { title: "Auth — Commune" },
      authType: { login: "Login", register: "Register" },
      login: "Log In",
      register: "Create Account",
      email: "Email",
      otp: {
        short: "OTP",
        long: "Email Verification Code",
        send: "Send OTP",
        enterThe6DigitCodeSentToYourEmail: "Enter the 6-digit code sent to your email.",
        sent: "OTP Sent",
        sendingDisabledByServer: "OTP sending is disabled by the server",
        placeholder: "6-digit code"
      },
      failedToSubmitPleaseTryAgain: "Failed to submit. Please try again.",
      invite: {
        required: "Invitation Required",
        notInvited: "You are not invited yet, write to one of our chats.",
        telegram: "Telegram",
        telegramLink: "https://t.me/ds_commune_en"
      }
    },
    ru: {
      _page: {
        title: "Вход — Коммуна"
      },
      authType: {
        login: "Вход",
        register: "Регистрация"
      },
      login: "Войти",
      register: "Создать аккаунт",
      email: "Email",
      otp: {
        short: "OTP",
        long: "Код проверки почты",
        send: "Отправить OTP",
        enterThe6DigitCodeSentToYourEmail: "Введите 6-значный код, отправленный на ваш email.",
        sent: "OTP отправлен",
        sendingDisabledByServer: "Отправка OTP отключена сервером",
        placeholder: "Шестизначный код"
      },
      failedToSubmitPleaseTryAgain: "Не удалось отправить. Пожалуйста, попробуйте снова.",
      invite: {
        required: "Требуется приглашение",
        notInvited: "Вы ещё не приглашены, напишите в один из наших чатов.",
        telegram: "Telegram",
        telegramLink: "https://t.me/ds_commune_ru"
      }
    }
  };
  const { data } = $$props;
  const { locale } = data;
  const t = i18n[locale];
  var OtpStatus = /* @__PURE__ */ ((OtpStatus2) => {
    OtpStatus2["None"] = "none";
    OtpStatus2["Pending"] = "pending";
    OtpStatus2["Sent"] = "sent";
    OtpStatus2["SendingDisabledByServer"] = "sending-disabled-by-server";
    OtpStatus2["Error"] = "error";
    return OtpStatus2;
  })(OtpStatus || {});
  var SubmitStatus = /* @__PURE__ */ ((SubmitStatus2) => {
    SubmitStatus2["None"] = "none";
    SubmitStatus2["Pending"] = "pending";
    SubmitStatus2["Error"] = "error";
    return SubmitStatus2;
  })(SubmitStatus || {});
  let email = "";
  let otp = "";
  let otpStatus = "none";
  let submitStatus = "none";
  let submitErrorMessage = null;
  $$payload.out.push(`<div class="container min-vh-100 d-flex align-items-center justify-content-center"><div class="card shadow-lg border-0"${attr_style("", { width: "100%", "max-width": "400px" })}><div class="card-header bg-white border-0 pt-4 pb-0"><div class="position-relative"><ul class="nav nav-tabs border-0 card-header-tabs"><li class="nav-item flex-grow-1 text-center"><button${attr_class(`nav-link border-0 w-100 ${"active"}`, "svelte-il0jyn")}>${escape_html(t.authType.login)}</button></li> <li class="nav-item flex-grow-1 text-center"><button${attr_class(`nav-link border-0 w-100 ${""}`, "svelte-il0jyn")}>${escape_html(t.authType.register)}</button></li></ul> <div class="position-absolute bottom-0 bg-primary"${attr_style("", {
    height: "3px",
    width: "50%",
    left: "0",
    transition: "left 0.3s ease-in-out",
    borderRadius: "3px 3px 0 0"
  })}></div></div></div> <div class="card-body p-4"><form><div class="mb-3"><label for="email" class="form-label">${escape_html(t.email)}</label> <input type="email" autoComplete="email" class="form-control" id="email"${attr("value", email)} placeholder="<EMAIL>" required/></div> <div class="mb-3"><button type="button" class="btn btn-outline-primary w-100"${attr("disabled", !email, true)}>`);
  if (otpStatus === OtpStatus.Pending) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> ${escape_html(t.otp.send)}</button></div> <div class="mb-3"><div class="d-flex justify-content-between align-items-center mb-2"><label for="otp" class="form-label mb-0">${escape_html(t.otp.long)}</label> `);
  if (otpStatus === OtpStatus.Sent) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill">${escape_html(t.otp.sent)}</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
    if (otpStatus === OtpStatus.SendingDisabledByServer) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="badge bg-warning-subtle text-warning px-2 py-1 rounded-pill">${escape_html(t.otp.sendingDisabledByServer)}</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> <input id="otp" class="form-control" type="text"${attr("value", otp)}${attr("placeholder", t.otp.placeholder)}${attr("maxlength", 6)} aria-describedby="otpHelp"/> <div id="otpHelp" class="form-text">${escape_html(t.otp.enterThe6DigitCodeSentToYourEmail)}</div></div> <div class="d-grid gap-2"><button type="submit" class="btn btn-primary"${attr("disabled", !email, true)}>`);
  if (submitStatus === SubmitStatus.Pending && otp) ;
  else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`${escape_html(t.login)}`);
  }
  $$payload.out.push(`<!--]--></button> `);
  if (submitStatus === SubmitStatus.Error) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="text-danger">${escape_html(t.failedToSubmitPleaseTryAgain)} <br/> ${escape_html(submitErrorMessage)}</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></form> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div></div>`);
  pop();
}
export {
  _page as default
};
