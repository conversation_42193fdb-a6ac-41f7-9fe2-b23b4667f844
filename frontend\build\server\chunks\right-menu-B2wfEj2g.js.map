{"version": 3, "file": "right-menu-B2wfEj2g.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/right-menu.js"], "sourcesContent": ["import { u as push, O as copy_payload, P as assign_payload, V as bind_props, w as pop, z as escape_html, y as attr, G as attr_class, J as stringify, K as ensure_array_like, F as attr_style } from \"./index.js\";\nimport { g as goto } from \"./client.js\";\nimport { g as getClient } from \"./acrpc.js\";\nimport { a as consts_exports } from \"./current-user.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { M as Modal } from \"./modal.js\";\nimport { L as Localized_input } from \"./localized-input.js\";\nimport { E as Editor } from \"./editor.js\";\n/* empty css                                                       */\nimport { R as Reactor_hub_picker } from \"./reactor-hub-picker.js\";\nimport { h as html } from \"./html.js\";\nfunction Localized_editor($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      languages: { en: \"English\", ru: \"Russian\" },\n      providedTranslations: \"Provided translations:\"\n    },\n    ru: {\n      languages: {\n        en: \"Английский\",\n        ru: \"Русский\"\n      },\n      providedTranslations: \"Указанные переводы:\"\n    }\n  };\n  let { value = void 0, $$slots, $$events, ...props } = $$props;\n  const {\n    id,\n    label,\n    required = false,\n    locale,\n    languageSelectPosition = \"top\",\n    children,\n    onEditorInit\n  } = props;\n  const t = i18n[locale];\n  let selectedLanguage = locale;\n  function getLanguageDisplay() {\n    return selectedLanguage.toUpperCase();\n  }\n  let editorContent = \"\";\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out.push(`<div class=\"mb-3\">`);\n    if (languageSelectPosition === \"top\") {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"d-flex justify-content-between align-items-center mb-2\"><p class=\"form-label mb-0 svelte-3fvz9w\">${escape_html(label)} `);\n      if (required) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<span class=\"text-danger\">*</span>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></p> <div class=\"dropdown\"><button class=\"btn btn-outline-secondary btn-sm dropdown-toggle\" type=\"button\"${attr(\"id\", `dropdown-${id}`)} data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"width: 60px; display: flex; justify-content: space-between; align-items: center;\">${escape_html(getLanguageDisplay())}</button> <ul class=\"dropdown-menu dropdown-menu-end\"${attr(\"aria-labelledby\", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"en\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"ru\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<p class=\"form-label mb-2 svelte-3fvz9w\">${escape_html(label)} `);\n      if (required) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<span class=\"text-danger\">*</span>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></p>`);\n    }\n    $$payload2.out.push(`<!--]--> <div${attr(\"id\", id)}>`);\n    Editor($$payload2, {\n      onEditorInit,\n      get content() {\n        return editorContent;\n      },\n      set content($$value) {\n        editorContent = $$value;\n        $$settled = false;\n      }\n    });\n    $$payload2.out.push(`<!----></div> `);\n    if (languageSelectPosition === \"bottom\") {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"d-flex justify-content-between align-items-center mt-2\"><div class=\"d-flex align-items-center\">`);\n      if (children) {\n        $$payload2.out.push(\"<!--[-->\");\n        children($$payload2);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div> <div class=\"dropdown\"><button class=\"btn btn-outline-secondary btn-sm dropdown-toggle\" type=\"button\"${attr(\"id\", `dropdown-${id}`)} data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"width: 60px; display: flex; justify-content: space-between; align-items: center;\">${escape_html(getLanguageDisplay())}</button> <ul class=\"dropdown-menu dropdown-menu-end\"${attr(\"aria-labelledby\", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"en\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"ru\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (value.length > 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      const each_array = ensure_array_like(value.filter(Boolean));\n      $$payload2.out.push(`<div class=\"mt-2 small text-muted\"><div>${escape_html(t.providedTranslations)}</div> <ul class=\"list-unstyled mb-0 mt-1\"><!--[-->`);\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let val = each_array[$$index];\n        $$payload2.out.push(`<li class=\"badge bg-light text-dark me-1\">${escape_html(t.languages[val.locale])}</li>`);\n      }\n      $$payload2.out.push(`<!--]--></ul></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div>`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { value });\n  pop();\n}\nfunction Tag_picker($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      tags: \"Tags\",\n      addTag: \"Add tag\",\n      searchTags: \"Search tags...\",\n      noTagsFound: \"No tags found\",\n      loading: \"Loading...\"\n    },\n    ru: {\n      tags: \"Теги\",\n      addTag: \"Добавить тег\",\n      searchTags: \"Поиск тегов...\",\n      noTagsFound: \"Теги не найдены\",\n      loading: \"Загрузка...\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  let { selectedTagIds = void 0, locale, label, placeholder } = $$props;\n  const t = i18n[locale];\n  let showTagInput = false;\n  let selectedTags = [];\n  function getAppropriateLocalization(localizations) {\n    const localization = localizations.find((l) => l.locale === locale);\n    return localization?.value || localizations[0]?.value || \"\";\n  }\n  const each_array = ensure_array_like(selectedTags);\n  $$payload.out.push(`<div class=\"mb-3\">`);\n  if (label && showTagInput) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<label for=\"tag-search-input\" class=\"form-label\">${escape_html(label)}</label>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"tag-picker svelte-1snh682\"><div class=\"selected-tags d-flex flex-wrap gap-2 mb-2 svelte-1snh682\"><!--[-->`);\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let tag = each_array[$$index];\n    $$payload.out.push(`<span class=\"badge bg-primary d-flex align-items-center\">${escape_html(getAppropriateLocalization(tag.name))} <button type=\"button\" class=\"btn-close btn-close-white ms-1\" style=\"font-size: 0.7em;\" aria-label=\"Remove tag\"></button></span>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<button type=\"button\" class=\"btn btn-outline-secondary btn-sm\"><i class=\"bi bi-plus\"></i> ${escape_html(t.addTag)}</button>`);\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n  bind_props($$props, { selectedTagIds });\n  pop();\n}\nfunction Reactor_community_picker($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      community: \"Community\",\n      selectCommunity: \"Select community\",\n      searchCommunities: \"Search communities...\",\n      noCommunitiesFound: \"No communities found\",\n      loading: \"Loading...\",\n      clearSelection: \"Clear selection\"\n    },\n    ru: {\n      community: \"Сообщество\",\n      selectCommunity: \"Выбрать сообщество\",\n      searchCommunities: \"Поиск сообществ...\",\n      noCommunitiesFound: \"Сообщества не найдены\",\n      loading: \"Загрузка...\",\n      clearSelection: \"Очистить выбор\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  let {\n    selectedCommunityId = void 0,\n    locale,\n    hubId,\n    label,\n    placeholder\n  } = $$props;\n  const t = i18n[locale];\n  let showCommunityInput = false;\n  $$payload.out.push(`<div class=\"mb-3\">`);\n  if (label && showCommunityInput) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<label for=\"community-search-input\" class=\"form-label\">${escape_html(label)}</label>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"community-picker svelte-njlx63\"><div class=\"selected-community d-flex align-items-center gap-2 mb-2 svelte-njlx63\">`);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n    {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<button type=\"button\" class=\"btn btn-outline-secondary\"><i class=\"bi bi-people-fill\"></i> ${escape_html(t.selectCommunity)}</button>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n  bind_props($$props, { selectedCommunityId });\n  pop();\n}\nfunction Create_post_modal($$payload, $$props) {\n  push();\n  const { show, locale, toLocaleHref, onClose, onPostCreated, post } = $$props;\n  const { fetcher: api } = getClient();\n  const i18n = {\n    en: {\n      createPostTitle: \"Create New Post\",\n      editPostTitle: \"Edit Post\",\n      cancel: \"Cancel\",\n      create: \"Create\",\n      save: \"Save Changes\",\n      hub: \"Hub\",\n      hubPlaceholder: \"Select hub (optional)...\",\n      community: \"Community\",\n      communityPlaceholder: \"Select community (optional)...\",\n      hubDisabledByCommunity: \"Hub selection is disabled when Community is specified\",\n      communityDisabledByHub: \"Community selection is disabled when Hub is specified\",\n      tags: \"Tags\",\n      title: \"Title\",\n      titlePlaceholder: \"Enter post title...\",\n      body: \"Body\",\n      bodyPlaceholder: \"Write your post content...\",\n      titleRequired: \"Title is required\",\n      bodyRequired: \"Body is required\",\n      createSuccess: \"Post created successfully!\",\n      createError: \"Failed to create post\",\n      updateSuccess: \"Post updated successfully!\",\n      updateError: \"Failed to update post\",\n      // Image upload translations\n      images: \"Images\",\n      uploadImages: \"Upload Images\",\n      dragDropImages: \"Drag & drop images here or click to select\",\n      maxImages: \"Maximum 10 images\",\n      uploading: \"Uploading...\",\n      imageUploadSuccess: \"Images uploaded successfully!\",\n      imageUploadError: \"Failed to upload images\",\n      invalidFileType: \"Invalid file type. Please upload JPG, PNG, or WebP images.\",\n      fileTooLarge: \"File is too large. Maximum size is 5MB.\",\n      tooManyFiles: \"Too many files. Maximum 10 images per post.\",\n      insertImage: \"Insert image\",\n      removeImage: \"Remove image\",\n      imageGallery: \"Image Gallery\",\n      loadingImages: \"Loading images...\"\n    },\n    ru: {\n      createPostTitle: \"Создать новый пост\",\n      editPostTitle: \"Редактировать пост\",\n      cancel: \"Отмена\",\n      create: \"Создать\",\n      save: \"Сохранить изменения\",\n      hub: \"Хаб\",\n      hubPlaceholder: \"Выберите хаб (необязательно)...\",\n      community: \"Сообщество\",\n      communityPlaceholder: \"Выберите сообщество (необязательно)...\",\n      hubDisabledByCommunity: \"Выбор хаба отключен, когда указано сообщество\",\n      communityDisabledByHub: \"Выбор сообщества отключен, когда указан хаб\",\n      tags: \"Теги\",\n      title: \"Заголовок\",\n      titlePlaceholder: \"Введите заголовок поста...\",\n      body: \"Содержание\",\n      bodyPlaceholder: \"Напишите содержание поста...\",\n      titleRequired: \"Заголовок обязателен\",\n      bodyRequired: \"Содержание обязательно\",\n      createSuccess: \"Пост успешно создан!\",\n      createError: \"Не удалось создать пост\",\n      updateSuccess: \"Пост успешно обновлен!\",\n      updateError: \"Не удалось обновить пост\",\n      // Image upload translations\n      images: \"Изображения\",\n      uploadImages: \"Загрузить изображения\",\n      dragDropImages: \"Перетащите изображения сюда или нажмите для выбора\",\n      maxImages: \"Максимум 10 изображений\",\n      uploading: \"Загрузка...\",\n      imageUploadSuccess: \"Изображения загружены успешно!\",\n      imageUploadError: \"Не удалось загрузить изображения\",\n      invalidFileType: \"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображения.\",\n      fileTooLarge: \"Файл слишком большой. Максимальный размер - 5MB.\",\n      tooManyFiles: \"Слишком много файлов. Максимум 10 изображений на пост.\",\n      insertImage: \"Вставить изображение\",\n      removeImage: \"Удалить изображение\",\n      imageGallery: \"Галерея изображений\",\n      loadingImages: \"Загрузка изображений...\"\n    }\n  };\n  const t = i18n[locale];\n  let postTitle = [];\n  let postBody = [];\n  let hubId = null;\n  let communityId = null;\n  let selectedTags = [];\n  let isSubmitting = false;\n  let formError = null;\n  let formSuccess = null;\n  let uploadedImages = [];\n  async function handleSubmitPost() {\n    formError = null;\n    formSuccess = null;\n    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);\n    const hasBody = postBody.some((item) => item.value.trim().length > 0);\n    if (!hasTitle) {\n      formError = t.titleRequired;\n      return;\n    }\n    if (!hasBody) {\n      formError = t.bodyRequired;\n      return;\n    }\n    isSubmitting = true;\n    try {\n      if (post) {\n        await api.reactor.post.patch({\n          id: post.id,\n          title: postTitle.filter((item) => item.value.trim().length > 0),\n          body: postBody.filter((item) => item.value.trim().length > 0),\n          tagIds: selectedTags,\n          imageIds: uploadedImages.map((img) => img.id)\n        });\n        formSuccess = t.updateSuccess;\n        setTimeout(\n          () => {\n            onClose();\n            window.location.reload();\n          },\n          1500\n        );\n      } else {\n        const { id } = await api.reactor.post.post({\n          hubId,\n          communityId,\n          title: postTitle.filter((item) => item.value.trim().length > 0),\n          body: postBody.filter((item) => item.value.trim().length > 0),\n          tagIds: selectedTags,\n          imageIds: uploadedImages.map((img) => img.id)\n        });\n        formSuccess = t.createSuccess;\n        if (onPostCreated) {\n          onPostCreated();\n        }\n        setTimeout(\n          () => {\n            goto(toLocaleHref(`/reactor/${id}`));\n          },\n          1500\n        );\n      }\n    } catch (error) {\n      console.error(\"Error submitting post:\", error);\n      formError = error instanceof Error ? error.message : post ? t.updateError : t.createError;\n    } finally {\n      isSubmitting = false;\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    Modal($$payload2, {\n      show,\n      title: post ? t.editPostTitle : t.createPostTitle,\n      onClose,\n      onSubmit: handleSubmitPost,\n      submitText: post ? t.save : t.create,\n      cancelText: t.cancel,\n      submitDisabled: isSubmitting || !postTitle.some((item) => item.value.trim().length > 0) || !postBody.some((item) => item.value.trim().length > 0),\n      isSubmitting,\n      children: ($$payload3) => {\n        $$payload3.out.push(`<form>`);\n        if (!post) {\n          $$payload3.out.push(\"<!--[-->\");\n          if (communityId) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"form-text text-muted\">${escape_html(t.hubDisabledByCommunity)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n            $$payload3.out.push(`<div class=\"mb-3\">`);\n            Reactor_hub_picker($$payload3, {\n              locale,\n              label: t.hub,\n              placeholder: t.hubPlaceholder,\n              get selectedHubId() {\n                return hubId;\n              },\n              set selectedHubId($$value) {\n                hubId = $$value;\n                $$settled = false;\n              }\n            });\n            $$payload3.out.push(`<!----></div>`);\n          }\n          $$payload3.out.push(`<!--]--> `);\n          if (hubId) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"form-text text-muted\">${escape_html(t.communityDisabledByHub)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n            $$payload3.out.push(`<div class=\"mb-3\">`);\n            Reactor_community_picker($$payload3, {\n              hubId,\n              locale,\n              label: t.community,\n              placeholder: t.communityPlaceholder,\n              get selectedCommunityId() {\n                return communityId;\n              },\n              set selectedCommunityId($$value) {\n                communityId = $$value;\n                $$settled = false;\n              }\n            });\n            $$payload3.out.push(`<!----></div>`);\n          }\n          $$payload3.out.push(`<!--]-->`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> `);\n        Localized_input($$payload3, {\n          locale,\n          id: \"post-title\",\n          label: t.title,\n          placeholder: t.titlePlaceholder,\n          required: true,\n          get value() {\n            return postTitle;\n          },\n          set value($$value) {\n            postTitle = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----> `);\n        Localized_editor($$payload3, {\n          locale,\n          id: \"post-body\",\n          label: t.body,\n          required: true,\n          onEditorInit: (editor) => {\n          },\n          languageSelectPosition: \"top\",\n          get value() {\n            return postBody;\n          },\n          set value($$value) {\n            postBody = $$value;\n            $$settled = false;\n          },\n          children: ($$payload4) => {\n            $$payload4.out.push(`<button type=\"button\" class=\"btn btn-outline-secondary btn-sm\"${attr(\"disabled\", uploadedImages.length >= 10, true)}${attr(\"title\", t.uploadImages)}><i class=\"bi bi-image\"></i> ${escape_html(t.uploadImages)}</button>`);\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out.push(`<!----> <input${attr(\"id\", post ? \"edit-image-upload-input\" : \"image-upload-input\")} type=\"file\" multiple${attr(\"accept\", consts_exports.ALLOWED_IMAGE_FILE_TYPES.join(\",\"))} style=\"display: none;\"/> <div class=\"mb-3\">`);\n        {\n          $$payload3.out.push(\"<!--[!-->\");\n          if (uploadedImages.length > 0) {\n            $$payload3.out.push(\"<!--[-->\");\n            const each_array = ensure_array_like(uploadedImages);\n            $$payload3.out.push(`<div class=\"mt-3\"><h6>${escape_html(t.imageGallery)}</h6> <div class=\"row g-2\"><!--[-->`);\n            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n              let image = each_array[$$index];\n              $$payload3.out.push(`<div class=\"col-6 col-md-4 col-lg-3\"><div class=\"position-relative\"><button type=\"button\" class=\"btn p-0 border-0 bg-transparent w-100\"${attr(\"title\", t.insertImage)}${attr(\"aria-label\", t.insertImage)}><img${attr(\"src\", `/images/${image.url}`)} alt=\"\" class=\"img-thumbnail w-100\" style=\"height: 120px; object-fit: cover;\"/></button> <button type=\"button\" class=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-1\"${attr(\"title\", t.removeImage)}${attr(\"aria-label\", t.removeImage)} style=\"padding: 0.25rem 0.5rem; font-size: 0.75rem;\"><i class=\"bi bi-x\"></i></button></div></div>`);\n            }\n            $$payload3.out.push(`<!--]--></div></div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]-->`);\n        }\n        $$payload3.out.push(`<!--]--> <div class=\"form-label\">${escape_html(t.images)}</div> <div${attr_class(`border border-2 border-dashed rounded p-4 text-center ${stringify(\"border-secondary\")}`)} role=\"button\" tabindex=\"0\" style=\"cursor: pointer; min-height: 100px; display: flex; flex-direction: column; justify-content: center;\">`);\n        {\n          $$payload3.out.push(\"<!--[!-->\");\n          $$payload3.out.push(`<div class=\"text-muted\"><i class=\"bi bi-cloud-upload fs-2 mb-2\"></i> <div>${escape_html(t.dragDropImages)}</div> <small class=\"text-muted\">${escape_html(t.maxImages)}</small></div>`);\n        }\n        $$payload3.out.push(`<!--]--></div> `);\n        {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> `);\n        {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--></div> `);\n        Tag_picker($$payload3, {\n          locale,\n          label: t.tags,\n          get selectedTagIds() {\n            return selectedTags;\n          },\n          set selectedTagIds($$value) {\n            selectedTags = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----> `);\n        if (formError) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-danger mt-3\" role=\"alert\">${escape_html(formError)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> `);\n        if (formSuccess) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-success mt-3\" role=\"alert\">${escape_html(formSuccess)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--></form>`);\n      }\n    });\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nfunction Post_card($$payload, $$props) {\n  push();\n  const {\n    post,\n    locale,\n    toLocaleHref,\n    getAppropriateLocalization,\n    currentUser,\n    onEditPost\n  } = $$props;\n  const i18n = {\n    en: {\n      usefulness: \"Usefulness\",\n      editPost: \"Edit Post\",\n      getPlural(n) {\n        if (n === 1) return 0;\n        return 1;\n      },\n      ratingTooltipText(rating2) {\n        const likesWord = [\"like\", \"likes\"][this.getPlural(rating2.likes % 10)];\n        const dislikesWord = [\"dislike\", \"dislikes\"][this.getPlural(rating2.dislikes % 10)];\n        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;\n      },\n      time: {\n        days(n) {\n          return `${n} ${n === 1 ? \"day\" : \"days\"} ago`;\n        },\n        hours(n) {\n          return `${n} ${n === 1 ? \"hour\" : \"hours\"} ago`;\n        },\n        minutes(n) {\n          return `${n} ${n === 1 ? \"minute\" : \"minutes\"} ago`;\n        },\n        seconds(n) {\n          return `${n} ${n === 1 ? \"second\" : \"seconds\"} ago`;\n        },\n        rightNow: \"right now\"\n      }\n    },\n    ru: {\n      usefulness: \"Полезность\",\n      editPost: \"Редактировать пост\",\n      getPlural(n) {\n        if (n === 1) return 0;\n        if (n >= 2 && n <= 4) return 1;\n        return 2;\n      },\n      ratingTooltipText(rating2) {\n        const likesWord = [\n          \"лайк\",\n          \"лайка\",\n          \"лайков\"\n        ][this.getPlural(rating2.likes % 10)];\n        const dislikesWord = [\n          \"дизлайк\",\n          \"дизлайка\",\n          \"дизлайков\"\n        ][this.getPlural(rating2.dislikes % 10)];\n        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;\n      },\n      time: {\n        days(n) {\n          const word = [\n            \"день\",\n            \"дня\",\n            \"дней\"\n          ][i18n.ru.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        hours(n) {\n          const word = [\n            \"час\",\n            \"часа\",\n            \"часов\"\n          ][i18n.ru.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        minutes(n) {\n          const word = [\n            \"минуту\",\n            \"минуты\",\n            \"минут\"\n          ][i18n.ru.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        seconds(n) {\n          const word = [\n            \"секунду\",\n            \"секунды\",\n            \"секунд\"\n          ][i18n.ru.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        rightNow: \"только что\"\n      }\n    }\n  };\n  const { fetcher: api } = getClient();\n  const t = i18n[locale];\n  let rating = post.rating;\n  let usefulness = post.usefulness;\n  const ratingValue = rating.likes - rating.dislikes;\n  const usefulnessValue = usefulness.totalValue ?? 0;\n  const ratingTooltipText = t.ratingTooltipText(rating);\n  const authorName = getAppropriateLocalization(post.author.name);\n  const title = getAppropriateLocalization(post.title);\n  const body = getAppropriateLocalization(post.body);\n  const hubName = post.hub ? getAppropriateLocalization(post.hub.name) : null;\n  const communityName = post.community ? getAppropriateLocalization(post.community.name) : null;\n  let copiedTagId = null;\n  const canEditPost = currentUser && (currentUser.role === \"admin\" || currentUser.id === post.author.id);\n  function formatDate(date) {\n    const now = /* @__PURE__ */ new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffSec = Math.floor(diffMs / 1e3);\n    const diffMin = Math.floor(diffSec / 60);\n    const diffHour = Math.floor(diffMin / 60);\n    const diffDay = Math.floor(diffHour / 24);\n    if (diffDay > 0) {\n      return t.time.days(diffDay);\n    } else if (diffHour > 0) {\n      return t.time.hours(diffHour);\n    } else if (diffMin > 0) {\n      return t.time.minutes(diffMin);\n    } else if (diffSec > 3) {\n      return t.time.seconds(diffSec);\n    } else {\n      return t.time.rightNow;\n    }\n  }\n  const each_array = ensure_array_like(Array(5));\n  const each_array_1 = ensure_array_like([...post.tags]);\n  $$payload.out.push(`<div class=\"post-card mb-4 svelte-1oqwi5d\"><div class=\"card\"><div class=\"card-header\">`);\n  if (post.hub || post.community) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"hub-community-header mb-3 svelte-1oqwi5d\">`);\n    if (post.hub) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"hub-row d-flex align-items-center mb-2\"><div class=\"hub-image-container me-2 svelte-1oqwi5d\">`);\n      if (post.hub.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<img${attr(\"src\", `/images/${post.hub.image}`)}${attr(\"alt\", hubName || \"Hub\")} class=\"hub-image svelte-1oqwi5d\"/>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"hub-image-placeholder svelte-1oqwi5d\"><i class=\"bi bi-collection text-muted svelte-1oqwi5d\"></i></div>`);\n      }\n      $$payload.out.push(`<!--]--></div> <a${attr(\"href\", toLocaleHref(`/reactor/hubs/${post.hub.id}`))} class=\"hub-link text-decoration-none fw-medium svelte-1oqwi5d\">${escape_html(hubName)}</a></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--> `);\n    if (post.community) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"community-row d-flex align-items-center\"><div class=\"community-image-container me-2 svelte-1oqwi5d\">`);\n      if (post.community.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<img${attr(\"src\", `/images/${post.community.image}`)}${attr(\"alt\", communityName || \"Community\")} class=\"community-image svelte-1oqwi5d\"/>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"community-image-placeholder svelte-1oqwi5d\"><i class=\"bi bi-people text-muted svelte-1oqwi5d\"></i></div>`);\n      }\n      $$payload.out.push(`<!--]--></div> <a${attr(\"href\", toLocaleHref(`/reactor/communities/${post.community.id}`))} class=\"community-link text-decoration-none fw-medium svelte-1oqwi5d\">${escape_html(communityName)}</a></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"post-header d-flex justify-content-between align-items-center mb-3\"><div class=\"d-flex align-items-center\"><div class=\"rating-block d-flex align-items-center me-3\">`);\n  if (ratingValue > 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span class=\"rating-value me-2 text-success svelte-1oqwi5d\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\"${attr(\"title\", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    if (ratingValue < 0) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"rating-value me-2 text-danger svelte-1oqwi5d\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\"${attr(\"title\", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<span class=\"rating-value me-2 svelte-1oqwi5d\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\"${attr(\"title\", ratingTooltipText)}>0</span>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--> <div class=\"rating-buttons\"><button${attr_class(`btn btn-sm me-1 ${rating?.status === \"like\" ? \"btn-success\" : \"btn-outline-success\"}`, \"svelte-1oqwi5d\")} aria-label=\"Like\"><i class=\"bi bi-hand-thumbs-up svelte-1oqwi5d\"></i></button> <button${attr_class(`btn btn-sm ${rating?.status === \"dislike\" ? \"btn-danger\" : \"btn-outline-danger\"}`, \"svelte-1oqwi5d\")} aria-label=\"Dislike\"><i class=\"bi bi-hand-thumbs-down svelte-1oqwi5d\"></i></button></div></div> <div class=\"author-info d-flex align-items-center\">`);\n  if (post.author.image) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<img${attr(\"src\", `/images/${post.author.image}`)} alt=\"avatar\" class=\"avatar rounded-circle me-2\" width=\"32\" height=\"32\"${attr_style(\"\", { \"object-fit\": \"cover\" })}/>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div class=\"avatar rounded-circle me-2\"></div>`);\n  }\n  $$payload.out.push(`<!--]--> <div><a${attr(\"href\", toLocaleHref(`/users/${post.author.id}`))} class=\"author-name fw-bold\"${attr_style(\"\", { \"text-decoration\": \"none\" })}>${escape_html(authorName ?? \"Anonymous\")}</a> <div class=\"post-time small text-muted\"${attr(\"title\", post.createdAt.toISOString())}>${escape_html(formatDate(post.createdAt))}</div></div></div></div> <div class=\"usefulness-block\"><div class=\"d-flex flex-column align-items-start\">`);\n  if (usefulness && usefulness.count > 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span class=\"usefulness-label mb-1 text-muted small px-1\">${escape_html(t.usefulness)} (${escape_html(usefulness.count)})</span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<span class=\"usefulness-label mb-1 text-muted small px-1\">${escape_html(t.usefulness)}</span>`);\n  }\n  $$payload.out.push(`<!--]--> <div role=\"group\" aria-label=\"Usefulness rating\"><!--[-->`);\n  for (let i = 0, $$length = each_array.length; i < $$length; i++) {\n    each_array[i];\n    $$payload.out.push(`<button class=\"btn btn-sm p-0 px-1\"${attr(\"aria-label\", `Rate usefulness ${i + 1}`)}><i${attr_class(`bi bi-star${stringify((i + 1) * 2 <= usefulnessValue ? \"-fill\" : \"\")} text-warning rating-star`)}></i></button>`);\n  }\n  $$payload.out.push(`<!--]--></div></div></div></div></div> <div class=\"card-body\"><div class=\"d-flex justify-content-between align-items-start mb-2\"><a${attr(\"href\", toLocaleHref(`/reactor/${post.id}`))} class=\"post-title-link-wrapper flex-grow-1 svelte-1oqwi5d\"><h5 class=\"card-title mb-0 svelte-1oqwi5d\">${escape_html(title)}</h5></a> `);\n  if (canEditPost && onEditPost) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<button type=\"button\" class=\"btn btn-outline-secondary btn-sm ms-2\"${attr(\"title\", t.editPost)}${attr(\"aria-label\", t.editPost)}><i class=\"bi bi-pencil\"></i></button>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div> <div class=\"card-text\">${html(body)}</div> <div class=\"tags mb-3 svelte-1oqwi5d\"><!--[-->`);\n  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n    let tag = each_array_1[$$index_1];\n    $$payload.out.push(`<button${attr_class(`badge me-1 ${copiedTagId === tag.id ? \"bg-success text-white\" : \"bg-light text-secondary\"}`, \"svelte-1oqwi5d\")}>${escape_html(getAppropriateLocalization(tag.name))}</button>`);\n  }\n  $$payload.out.push(`<!--]--></div> <div class=\"card-actions d-flex\"><a${attr(\"href\", toLocaleHref(`/reactor/${post.id}`))} target=\"_blank\"><button${attr_class(`btn btn-sm ${\"btn-outline-secondary\"}`, \"svelte-1oqwi5d\")} aria-label=\"Copy link\">`);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<i class=\"bi bi-link-45deg svelte-1oqwi5d\"></i>`);\n  }\n  $$payload.out.push(`<!--]--></button></a></div></div></div></div>`);\n  pop();\n}\nfunction Right_menu($$payload, $$props) {\n  push();\n  let { isExpanded = false, $$slots, $$events, ...props } = $$props;\n  const { locale, toLocaleHref } = props;\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  bind_props($$props, { isExpanded });\n  pop();\n}\nexport {\n  Create_post_modal as C,\n  Post_card as P,\n  Right_menu as R\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAYA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;AACjD,MAAM,oBAAoB,EAAE;AAC5B,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE;AACjB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,oBAAoB,EAAE;AAC5B;AACA,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/D,EAAE,MAAM;AACR,IAAI,EAAE;AACN,IAAI,KAAK;AACT,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,MAAM;AACV,IAAI,sBAAsB,GAAG,KAAK;AAClC,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,GAAG,KAAK;AACX,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,gBAAgB,GAAG,MAAM;AAC/B,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,OAAO,gBAAgB,CAAC,WAAW,EAAE;AACzC,EAAE;AACF,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC7C,IAAI,IAAI,sBAAsB,KAAK,KAAK,EAAE;AAC1C,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6GAA6G,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChK,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,CAAC,CAAC;AACjE,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iHAAiH,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I,EAAE,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,qDAAqD,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACvwB,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,CAAC,CAAC;AACjE,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;AACzC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,YAAY;AAClB,MAAM,IAAI,OAAO,GAAG;AACpB,QAAQ,OAAO,aAAa;AAC5B,MAAM,CAAC;AACP,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,aAAa,GAAG,OAAO;AAC/B,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACzC,IAAI,IAAI,sBAAsB,KAAK,QAAQ,EAAE;AAC7C,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2GAA2G,CAAC,CAAC;AACxI,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,QAAQ,CAAC,UAAU,CAAC;AAC5B,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mHAAmH,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I,EAAE,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,qDAAqD,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACzwB,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjE,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,mDAAmD,CAAC,CAAC;AAC9J,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACrC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0CAA0C,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACrH,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAChD,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACzC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,OAAO,EAAE;AACf,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,IAAI,EAAE,cAAc,GAAG,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,OAAO;AACvE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,SAAS,0BAA0B,CAAC,aAAa,EAAE;AACrD,IAAI,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC;AACvE,IAAI,OAAO,YAAY,EAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;AAC/D,EAAE;AACF,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACpD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAAE,IAAI,KAAK,IAAI,YAAY,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iDAAiD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;AACxG,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8HAA8H,CAAC,CAAC;AACtJ,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yDAAyD,EAAE,WAAW,CAAC,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,gIAAgI,CAAC,CAAC;AACvQ,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0FAA0F,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;AACrJ,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,CAAC;AACzC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,wBAAwB,CAAC,SAAS,EAAE,OAAO,EAAE;AACtD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,iBAAiB,EAAE,uBAAuB;AAChD,MAAM,kBAAkB,EAAE,sBAAsB;AAChD,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,cAAc,EAAE;AACtB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,kBAAkB,EAAE,uBAAuB;AACjD,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,cAAc,EAAE;AACtB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,IAAI;AACN,IAAI,mBAAmB,GAAG,MAAM;AAChC,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,kBAAkB,GAAG,KAAK;AAChC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAAE,IAAI,KAAK,IAAI,kBAAkB,EAAE;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uDAAuD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC9G,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wIAAwI,CAAC,CAAC;AAChK,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0FAA0F,EAAE,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,CAAC;AAChK,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,mBAAmB,EAAE,CAAC;AAC9C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,OAAO;AAC9E,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,eAAe,EAAE,iBAAiB;AACxC,MAAM,aAAa,EAAE,WAAW;AAChC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,cAAc,EAAE,0BAA0B;AAChD,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,oBAAoB,EAAE,gCAAgC;AAC5D,MAAM,sBAAsB,EAAE,uDAAuD;AACrF,MAAM,sBAAsB,EAAE,uDAAuD;AACrF,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,gBAAgB,EAAE,qBAAqB;AAC7C,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,eAAe,EAAE,4BAA4B;AACnD,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,YAAY,EAAE,kBAAkB;AACtC,MAAM,aAAa,EAAE,4BAA4B;AACjD,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,aAAa,EAAE,4BAA4B;AACjD,MAAM,WAAW,EAAE,uBAAuB;AAC1C;AACA,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,cAAc,EAAE,4CAA4C;AAClE,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,kBAAkB,EAAE,+BAA+B;AACzD,MAAM,gBAAgB,EAAE,yBAAyB;AACjD,MAAM,eAAe,EAAE,4DAA4D;AACnF,MAAM,YAAY,EAAE,yCAAyC;AAC7D,MAAM,YAAY,EAAE,6CAA6C;AACjE,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,aAAa,EAAE,oBAAoB;AACzC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,cAAc,EAAE,iCAAiC;AACvD,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,oBAAoB,EAAE,wCAAwC;AACpE,MAAM,sBAAsB,EAAE,+CAA+C;AAC7E,MAAM,sBAAsB,EAAE,6CAA6C;AAC3E,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,gBAAgB,EAAE,4BAA4B;AACpD,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,eAAe,EAAE,8BAA8B;AACrD,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,YAAY,EAAE,wBAAwB;AAC5C,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,WAAW,EAAE,yBAAyB;AAC5C,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,WAAW,EAAE,0BAA0B;AAC7C;AACA,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,YAAY,EAAE,uBAAuB;AAC3C,MAAM,cAAc,EAAE,oDAAoD;AAC1E,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,kBAAkB,EAAE,gCAAgC;AAC1D,MAAM,gBAAgB,EAAE,kCAAkC;AAC1D,MAAM,eAAe,EAAE,0EAA0E;AACjG,MAAM,YAAY,EAAE,kDAAkD;AACtE,MAAM,YAAY,EAAE,wDAAwD;AAC5E,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,WAAW,EAAE,qBAAqB;AACxC,MAAM,YAAY,EAAE,qBAAqB;AACzC,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,SAAS,GAAG,EAAE;AACpB,EAAE,IAAI,QAAQ,GAAG,EAAE;AACnB,EAAE,IAAI,KAAK,GAAG,IAAI;AAClB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,eAAe,gBAAgB,GAAG;AACpC,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3E,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,SAAS,GAAG,CAAC,CAAC,aAAa;AACjC,MAAM;AACN,IAAI;AACJ,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,SAAS,GAAG,CAAC,CAAC,YAAY;AAChC,MAAM;AACN,IAAI;AACJ,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,IAAI;AACR,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACrC,UAAU,EAAE,EAAE,IAAI,CAAC,EAAE;AACrB,UAAU,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACzE,UAAU,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACvE,UAAU,MAAM,EAAE,YAAY;AAC9B,UAAU,QAAQ,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;AACtD,SAAS,CAAC;AACV,QAAQ,WAAW,GAAG,CAAC,CAAC,aAAa;AACrC,QAAQ,UAAU;AAClB,UAAU,MAAM;AAChB,YAAY,OAAO,EAAE;AACrB,YAAY,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AACpC,UAAU,CAAC;AACX,UAAU;AACV,SAAS;AACT,MAAM,CAAC,MAAM;AACb,QAAQ,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AACnD,UAAU,KAAK;AACf,UAAU,WAAW;AACrB,UAAU,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACzE,UAAU,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACvE,UAAU,MAAM,EAAE,YAAY;AAC9B,UAAU,QAAQ,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;AACtD,SAAS,CAAC;AACV,QAAQ,WAAW,GAAG,CAAC,CAAC,aAAa;AACrC,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,aAAa,EAAE;AACzB,QAAQ;AACR,QAAQ,UAAU;AAClB,UAAU,MAAM;AAChB,YAAY,IAAI,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChD,UAAU,CAAC;AACX,UAAU;AACV,SAAS;AACT,MAAM;AACN,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AACpD,MAAM,SAAS,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW;AAC/F,IAAI,CAAC,SAAS;AACd,MAAM,YAAY,GAAG,KAAK;AAC1B,IAAI;AACJ,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe;AACvD,MAAM,OAAO;AACb,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM;AAC1C,MAAM,UAAU,EAAE,CAAC,CAAC,MAAM;AAC1B,MAAM,cAAc,EAAE,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACvJ,MAAM,YAAY;AAClB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,CAAC;AACnH,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACrD,YAAY,kBAAkB,CAAC,UAAU,EAAE;AAC3C,cAAc,MAAM;AACpB,cAAc,KAAK,EAAE,CAAC,CAAC,GAAG;AAC1B,cAAc,WAAW,EAAE,CAAC,CAAC,cAAc;AAC3C,cAAc,IAAI,aAAa,GAAG;AAClC,gBAAgB,OAAO,KAAK;AAC5B,cAAc,CAAC;AACf,cAAc,IAAI,aAAa,CAAC,OAAO,EAAE;AACzC,gBAAgB,KAAK,GAAG,OAAO;AAC/B,gBAAgB,SAAS,GAAG,KAAK;AACjC,cAAc;AACd,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C,UAAU,IAAI,KAAK,EAAE;AACrB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,CAAC;AACnH,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACrD,YAAY,wBAAwB,CAAC,UAAU,EAAE;AACjD,cAAc,KAAK;AACnB,cAAc,MAAM;AACpB,cAAc,KAAK,EAAE,CAAC,CAAC,SAAS;AAChC,cAAc,WAAW,EAAE,CAAC,CAAC,oBAAoB;AACjD,cAAc,IAAI,mBAAmB,GAAG;AACxC,gBAAgB,OAAO,WAAW;AAClC,cAAc,CAAC;AACf,cAAc,IAAI,mBAAmB,CAAC,OAAO,EAAE;AAC/C,gBAAgB,WAAW,GAAG,OAAO;AACrC,gBAAgB,SAAS,GAAG,KAAK;AACjC,cAAc;AACd,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACzC,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ,eAAe,CAAC,UAAU,EAAE;AACpC,UAAU,MAAM;AAChB,UAAU,EAAE,EAAE,YAAY;AAC1B,UAAU,KAAK,EAAE,CAAC,CAAC,KAAK;AACxB,UAAU,WAAW,EAAE,CAAC,CAAC,gBAAgB;AACzC,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,SAAS;AAC5B,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,SAAS,GAAG,OAAO;AAC/B,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,gBAAgB,CAAC,UAAU,EAAE;AACrC,UAAU,MAAM;AAChB,UAAU,EAAE,EAAE,WAAW;AACzB,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI;AACvB,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,YAAY,EAAE,CAAC,MAAM,KAAK;AACpC,UAAU,CAAC;AACX,UAAU,sBAAsB,EAAE,KAAK;AACvC,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,QAAQ;AAC3B,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,QAAQ,GAAG,OAAO;AAC9B,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU,CAAC;AACX,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,EAAE,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,MAAM,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,6BAA6B,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC;AAC3P,UAAU,CAAC;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,yBAAyB,GAAG,oBAAoB,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,4CAA4C,CAAC,CAAC;AACxP,QAAQ;AACR,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AACzC,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,MAAM,UAAU,GAAG,iBAAiB,CAAC,cAAc,CAAC;AAChE,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,mCAAmC,CAAC,CAAC;AAC1H,YAAY,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC/F,cAAc,IAAI,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC;AAC7C,cAAc,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uIAAuI,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,8KAA8K,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,kGAAkG,CAAC,CAAC;AAC7mB,YAAY;AACZ,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AACvD,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACzC,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,sDAAsD,EAAE,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,wIAAwI,CAAC,CAAC;AAClV,QAAQ;AACR,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,CAAC;AACrN,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,QAAQ;AACR,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ;AACR,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,QAAQ,UAAU,CAAC,UAAU,EAAE;AAC/B,UAAU,MAAM;AAChB,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI;AACvB,UAAU,IAAI,cAAc,GAAG;AAC/B,YAAY,OAAO,YAAY;AAC/B,UAAU,CAAC;AACX,UAAU,IAAI,cAAc,CAAC,OAAO,EAAE;AACtC,YAAY,YAAY,GAAG,OAAO;AAClC,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AAClH,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mDAAmD,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AACrH,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,MAAM;AACN,KAAK,CAAC;AACN,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,YAAY;AAChB,IAAI,0BAA0B;AAC9B,IAAI,WAAW;AACf,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,UAAU,EAAE,YAAY;AAC9B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,CAAC,CAAC,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;AAC7B,QAAQ,OAAO,CAAC;AAChB,MAAM,CAAC;AACP,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AAC/E,QAAQ,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;AAC3F,QAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACnF,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;AACvD,QAAQ,CAAC;AACT,QAAQ,KAAK,CAAC,CAAC,EAAE;AACjB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACzD,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC;AAC7D,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC;AAC7D,QAAQ,CAAC;AACT,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,UAAU,EAAE,YAAY;AAC9B,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,SAAS,CAAC,CAAC,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;AACtC,QAAQ,OAAO,CAAC;AAChB,MAAM,CAAC;AACP,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,SAAS,GAAG;AAC1B,UAAU,MAAM;AAChB,UAAU,OAAO;AACjB,UAAU;AACV,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AAC7C,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,SAAS;AACnB,UAAU,UAAU;AACpB,UAAU;AACV,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;AAChD,QAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACnF,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,MAAM;AAClB,YAAY,KAAK;AACjB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjC,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,KAAK,CAAC,CAAC,EAAE;AACjB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,KAAK;AACjB,YAAY,MAAM;AAClB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjC,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,QAAQ;AACpB,YAAY,QAAQ;AACpB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjC,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,SAAS;AACrB,YAAY,SAAS;AACrB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjC,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,QAAQ,EAAE;AAClB;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;AAC1B,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU;AAClC,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ;AACpD,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,IAAI,CAAC;AACpD,EAAE,MAAM,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC;AACvD,EAAE,MAAM,UAAU,GAAG,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACjE,EAAE,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;AACtD,EAAE,MAAM,IAAI,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI;AAC7E,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,GAAG,0BAA0B,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;AAC/F,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,MAAM,WAAW,GAAG,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,OAAO,IAAI,WAAW,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AACxG,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;AACjD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC5C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC7C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC7C,IAAI,IAAI,OAAO,GAAG,CAAC,EAAE;AACrB,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACjC,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,EAAE;AAC7B,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;AACnC,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAC5B,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AACpC,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAC5B,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AACpC,IAAI,CAAC,MAAM;AACX,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;AAC5B,IAAI;AACJ,EAAE;AACF,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChD,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACxD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sFAAsF,CAAC,CAAC;AAC9G,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sDAAsD,CAAC,CAAC;AAChF,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yGAAyG,CAAC,CAAC;AACrI,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;AAC1B,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,CAAC,mCAAmC,CAAC,CAAC;AAChJ,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kHAAkH,CAAC,CAAC;AAChJ,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gEAAgE,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC;AAC3M,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gHAAgH,CAAC,CAAC;AAC5I,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAChC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,aAAa,IAAI,WAAW,CAAC,CAAC,yCAAyC,CAAC,CAAC;AACxK,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oHAAoH,CAAC,CAAC;AAClJ,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,sEAAsE,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC;AACpO,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yLAAyL,CAAC,CAAC;AACjN,EAAE,IAAI,WAAW,GAAG,CAAC,EAAE;AACvB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4GAA4G,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5M,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,IAAI,WAAW,GAAG,CAAC,EAAE;AACzB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2GAA2G,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7M,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+FAA+F,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC;AACvK,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,UAAU,CAAC,CAAC,gBAAgB,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,qBAAqB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,uFAAuF,EAAE,UAAU,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,KAAK,SAAS,GAAG,YAAY,GAAG,oBAAoB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,oJAAoJ,CAAC,CAAC;AACzhB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AACzB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,uEAAuE,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACjM,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8CAA8C,CAAC,CAAC;AACxE,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,CAAC,4CAA4C,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,yGAAyG,CAAC,CAAC;AACrc,EAAE,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE;AAC1C,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0DAA0D,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC1J,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0DAA0D,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC;AACvH,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kEAAkE,CAAC,CAAC;AAC1F,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACnE,IAAI,UAAU,CAAC,CAAC,CAAC;AACjB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,eAAe,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;AAC9O,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,uGAAuG,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;AACrV,EAAE,IAAI,WAAW,IAAI,UAAU,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mEAAmE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,sCAAsC,CAAC,CAAC;AAChM,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,qDAAqD,CAAC,CAAC;AAChI,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC;AACrC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,WAAW,EAAE,WAAW,KAAK,GAAG,CAAC,EAAE,GAAG,uBAAuB,GAAG,yBAAyB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC5N,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kDAAkD,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,wBAAwB,CAAC,CAAC;AACtP,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,CAAC,CAAC;AACzE,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,CAAC,CAAC;AACrE,EAAE,GAAG,EAAE;AACP;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AACnE,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,KAAK;AACxC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;;;;"}