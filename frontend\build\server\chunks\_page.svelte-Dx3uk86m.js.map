{"version": 3, "file": "_page.svelte-Dx3uk86m.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/_page.svelte.js"], "sourcesContent": ["import { u as push, w as pop, G as attr_class, z as escape_html, y as attr, J as stringify, O as copy_payload, P as assign_payload, x as head, K as ensure_array_like } from \"../../../../chunks/index.js\";\nimport { a as consts_exports } from \"../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../chunks/acrpc.js\";\n/* empty css                                                                        */\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../chunks/exports.js\";\nimport \"../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { P as Post_card, R as Right_menu, C as Create_post_modal } from \"../../../../chunks/right-menu.js\";\nimport \"clsx\";\nfunction Lens_modal($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      createLens: \"Create Lens\",\n      editLens: \"Edit Lens\",\n      lensGuide: \"Lens Guide\",\n      cancel: \"Cancel\",\n      create: \"Create\",\n      update: \"Update\",\n      backToForm: \"Back to Form\",\n      showGuide: \"Show Guide\",\n      name: \"Name\",\n      namePlaceholder: \"Enter lens name...\",\n      code: \"Code\",\n      nameRequired: \"Name is required\",\n      codeRequired: \"Code is required\",\n      createSuccess: \"Lens created successfully!\",\n      updateSuccess: \"Lens updated successfully!\",\n      createError: \"Failed to create lens\",\n      updateError: \"Failed to update lens\",\n      // Guide translations\n      guideTitle: \"Lens Code Guide\",\n      fieldsOperatorsTitle: \"Available Fields & Operators\",\n      fieldColumn: \"Field\",\n      operatorsColumn: \"Operators\",\n      valueTypeColumn: \"Value Type\",\n      descriptionColumn: \"Description\",\n      hubDescription: \"Filter by hub\",\n      communityDescription: \"Filter by community\",\n      authorDescription: \"Filter by author\",\n      ratingDescription: \"Post rating\",\n      usefulnessDescription: \"Post usefulness\",\n      tagDescription: \"Filter by tags\",\n      titleDescription: \"Search in title (like operator)\",\n      bodyDescription: \"Search in body (like operator)\",\n      ageDescription: 'Post age (e.g., \"3d\", \"2w\", \"1mo\")',\n      logicalOperatorsTitle: \"Logical Operators\",\n      andOperator: \"AND operator (both conditions must be true)\",\n      orOperator: \"OR operator (either condition can be true)\",\n      parenthesesOperator: \"Parentheses for grouping expressions\",\n      ageDurationTitle: \"Age Duration Units\",\n      minutesUnit: \"minutes\",\n      hoursUnit: \"hours\",\n      daysUnit: \"days\",\n      weeksUnit: \"weeks\",\n      monthsUnit: \"months\",\n      yearsUnit: \"years\",\n      simpleExamplesTitle: \"Simple Examples\",\n      highRatedExample: \"High rated posts:\",\n      recentPostsExample: \"Recent posts:\",\n      titleSearchExample: \"Search in title:\",\n      usefulRecentExample: \"Useful and recent:\",\n      complexExampleTitle: \"Complex Example\",\n      complexExampleSubtitle: \"High-quality recent posts with educational content:\",\n      complexExampleDescription: 'This filters for posts that meet one of two criteria: either posts from specific hubs that have a rating of 100 or higher, OR posts from a specific community that have a usefulness score of 7 or higher. Additionally, all results must be posted within the last 2 weeks and contain either \"tutorial\" in the title or \"guide\" in the body content.',\n      // Value types\n      arrayOfIds: \"Array of IDs\",\n      integer: \"Integer\",\n      integerRange: \"Integer (0-10)\",\n      string: \"String\",\n      durationString: \"Duration string\"\n    },\n    ru: {\n      createLens: \"Создать линзу\",\n      editLens: \"Редактировать линзу\",\n      lensGuide: \"Руководство по линзам\",\n      cancel: \"Отмена\",\n      create: \"Создать\",\n      update: \"Обновить\",\n      backToForm: \"Вернуться к форме\",\n      showGuide: \"Показать руководство\",\n      name: \"Название\",\n      namePlaceholder: \"Введите название линзы...\",\n      code: \"Код\",\n      nameRequired: \"Название обязательно\",\n      codeRequired: \"Код обязателен\",\n      createSuccess: \"Линза успешно создана!\",\n      updateSuccess: \"Линза успешно обновлена!\",\n      createError: \"Не удалось создать линзу\",\n      updateError: \"Не удалось обновить линзу\",\n      // Guide translations\n      guideTitle: \"Руководство по коду линз\",\n      fieldsOperatorsTitle: \"Доступные поля и операторы\",\n      fieldColumn: \"Поле\",\n      operatorsColumn: \"Операторы\",\n      valueTypeColumn: \"Тип значения\",\n      descriptionColumn: \"Описание\",\n      hubDescription: \"Фильтр по хабу\",\n      communityDescription: \"Фильтр по сообществу\",\n      authorDescription: \"Фильтр по автору\",\n      ratingDescription: \"Рейтинг поста\",\n      usefulnessDescription: \"Полезность поста\",\n      tagDescription: \"Фильтр по тегам\",\n      titleDescription: \"Поиск в заголовке (оператор подобия)\",\n      bodyDescription: \"Поиск в тексте (оператор подобия)\",\n      ageDescription: 'Возраст поста (например, \"3d\", \"2w\", \"1mo\")',\n      logicalOperatorsTitle: \"Логические операторы\",\n      andOperator: \"Оператор И (оба условия должны быть истинными)\",\n      orOperator: \"Оператор ИЛИ (любое из условий может быть истинным)\",\n      parenthesesOperator: \"Скобки для группировки выражений\",\n      ageDurationTitle: \"Единицы времени для возраста\",\n      minutesUnit: \"минуты\",\n      hoursUnit: \"часы\",\n      daysUnit: \"дни\",\n      weeksUnit: \"недели\",\n      monthsUnit: \"месяцы\",\n      yearsUnit: \"годы\",\n      simpleExamplesTitle: \"Простые примеры\",\n      highRatedExample: \"Посты с высоким рейтингом:\",\n      recentPostsExample: \"Недавние посты:\",\n      titleSearchExample: \"Поиск в заголовке:\",\n      usefulRecentExample: \"Полезные и недавние:\",\n      complexExampleTitle: \"Сложный пример\",\n      complexExampleSubtitle: \"Качественные недавние посты с образовательным контентом:\",\n      complexExampleDescription: 'Этот фильтр отбирает посты, которые соответствуют одному из двух критериев: либо посты из определенных хабов с рейтингом 100 или выше, ЛИБО посты из определенного сообщества с оценкой полезности 7 или выше. Дополнительно все результаты должны быть опубликованы в течение последних 2 недель и содержать либо \"tutorial\" в заголовке, либо \"guide\" в тексте.',\n      // Value types\n      arrayOfIds: \"Массив ID\",\n      integer: \"Целое число\",\n      integerRange: \"Целое число (0-10)\",\n      string: \"Строка\",\n      durationString: \"Строка времени\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { locale } = $$props;\n  const t = i18n[locale];\n  t.createLens;\n  t.create;\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  pop();\n}\nfunction Left_menu($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      lens: \"Lens\",\n      lensing: \"Lensing\",\n      createLens: \"Create lens\",\n      editLens: \"Edit lens\",\n      deleteLens: \"Delete lens\",\n      noLens: \"No lens\",\n      before: \"Before\",\n      from: \"From\",\n      now: \"now\",\n      showRead: \"Show read\",\n      toggleLensing: \"Toggle lensing\",\n      toggleDateRange: \"Toggle date range\",\n      confirmDelete: \"Are you sure you want to delete this lens?\",\n      deleteSuccess: \"Lens deleted successfully!\",\n      deleteError: \"Failed to delete lens\"\n    },\n    ru: {\n      lens: \"Линза\",\n      lensing: \"Линзирование\",\n      createLens: \"Создать линзу\",\n      editLens: \"Редактировать линзу\",\n      deleteLens: \"Удалить линзу\",\n      noLens: \"Без линзы\",\n      before: \"До\",\n      from: \"От\",\n      now: \"сейчас\",\n      showRead: \"Прочитанное\",\n      toggleLensing: \"Переключить линзирование\",\n      toggleDateRange: \"Переключить диапазон дат\",\n      confirmDelete: \"Вы уверены, что хотите удалить эту линзу?\",\n      deleteSuccess: \"Линза успешно удалена!\",\n      deleteError: \"Не удалось удалить линзу\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const {\n    locale\n  } = $$props;\n  const t = i18n[locale];\n  $$payload.out.push(`<div${attr_class(`left-menu ${stringify(\"collapsed\")}`, \"svelte-12keyqc\")}><div class=\"left-menu-header d-flex justify-content-between align-items-center p-3 bg-light svelte-12keyqc\"><h6 class=\"mb-0\">${escape_html(t.lensing)}</h6> <button class=\"btn btn-sm btn-link p-0\"${attr(\"aria-label\", t.toggleLensing)}${attr(\"title\", t.toggleLensing)}><i${attr_class(`bi bi-chevron-${stringify(\"down\")}`)}></i></button></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  Lens_modal($$payload, {\n    locale\n  });\n  $$payload.out.push(`<!---->`);\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Feed — Reactor\" },\n      createPost: \"Create Post\",\n      noPosts: \"No posts found\",\n      loadingMore: \"Loading more posts...\"\n    },\n    ru: {\n      _page: {\n        title: \"Лента — Реактор\"\n      },\n      createPost: \"Создать пост\",\n      noPosts: \"Посты не найдены\",\n      loadingMore: \"Загружаем больше постов...\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let posts = data.posts;\n  data.lenses;\n  let isHasMorePosts = data.isHasMorePosts;\n  let isLoadingMore = false;\n  let error = null;\n  let selectedLensId = null;\n  let isRightMenuExpanded = false;\n  let showCreatePostModal = false;\n  function closeCreatePostModal() {\n    showCreatePostModal = false;\n  }\n  function handlePostCreated() {\n    refetchPosts();\n  }\n  async function refetchPosts() {\n    isLoadingMore = true;\n    error = null;\n    try {\n      const newPosts = await api.reactor.post.list.get({ pagination: { page: 1 }, lensId: selectedLensId });\n      posts = newPosts;\n      isHasMorePosts = newPosts.length === consts_exports.PAGE_SIZE;\n    } catch (err) {\n      error = err instanceof Error ? err.message : \"An error occurred while fetching posts\";\n      console.error(err);\n    } finally {\n      isLoadingMore = false;\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(t._page.title)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"row g-4 mt-3\"><div class=\"col-1\"></div> <div class=\"col-2\">`);\n    Left_menu($$payload2, {\n      locale\n    });\n    $$payload2.out.push(`<!----></div> <div class=\"col-6\"><div class=\"feed svelte-wnb4vd\">`);\n    if (posts.length === 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noPosts)}</p></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      const each_array = ensure_array_like(posts);\n      $$payload2.out.push(`<!--[-->`);\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let post = each_array[$$index];\n        Post_card($$payload2, { post, locale, toLocaleHref, getAppropriateLocalization });\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (isHasMorePosts) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"text-center py-3\">`);\n      if (isLoadingMore) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"spinner-border spinner-border-sm\" role=\"status\"><span class=\"visually-hidden\">${escape_html(t.loadingMore)}</span></div> <p class=\"text-muted mt-2 mb-0\">${escape_html(t.loadingMore)}</p>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (error) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"alert alert-danger\" role=\"alert\">${escape_html(error)}</div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div></div> <div class=\"col-2\"><div${attr_class(`mb-3 create-post-btn-container ${stringify(isRightMenuExpanded ? \"with-right-menu-expanded\" : \"\")}`, \"svelte-wnb4vd\")}><button class=\"btn btn-outline-secondary w-100 create-post-btn svelte-wnb4vd\"${attr(\"aria-label\", t.createPost)}><i class=\"bi bi-plus-circle me-2\"></i> ${escape_html(t.createPost)}</button></div> `);\n    Right_menu($$payload2, {\n      locale,\n      toLocaleHref,\n      get isExpanded() {\n        return isRightMenuExpanded;\n      },\n      set isExpanded($$value) {\n        isRightMenuExpanded = $$value;\n        $$settled = false;\n      }\n    });\n    $$payload2.out.push(`<!----></div></div> `);\n    Create_post_modal($$payload2, {\n      show: showCreatePostModal,\n      locale,\n      toLocaleHref,\n      onClose: closeCreatePostModal,\n      onPostCreated: handlePostCreated\n    });\n    $$payload2.out.push(`<!---->`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAWA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,YAAY,EAAE,kBAAkB;AACtC,MAAM,YAAY,EAAE,kBAAkB;AACtC,MAAM,aAAa,EAAE,4BAA4B;AACjD,MAAM,aAAa,EAAE,4BAA4B;AACjD,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,WAAW,EAAE,uBAAuB;AAC1C;AACA,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,oBAAoB,EAAE,8BAA8B;AAC1D,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,eAAe,EAAE,WAAW;AAClC,MAAM,eAAe,EAAE,YAAY;AACnC,MAAM,iBAAiB,EAAE,aAAa;AACtC,MAAM,cAAc,EAAE,eAAe;AACrC,MAAM,oBAAoB,EAAE,qBAAqB;AACjD,MAAM,iBAAiB,EAAE,kBAAkB;AAC3C,MAAM,iBAAiB,EAAE,aAAa;AACtC,MAAM,qBAAqB,EAAE,iBAAiB;AAC9C,MAAM,cAAc,EAAE,gBAAgB;AACtC,MAAM,gBAAgB,EAAE,iCAAiC;AACzD,MAAM,eAAe,EAAE,gCAAgC;AACvD,MAAM,cAAc,EAAE,oCAAoC;AAC1D,MAAM,qBAAqB,EAAE,mBAAmB;AAChD,MAAM,WAAW,EAAE,6CAA6C;AAChE,MAAM,UAAU,EAAE,4CAA4C;AAC9D,MAAM,mBAAmB,EAAE,sCAAsC;AACjE,MAAM,gBAAgB,EAAE,oBAAoB;AAC5C,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE,MAAM;AACtB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,UAAU,EAAE,QAAQ;AAC1B,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,mBAAmB,EAAE,iBAAiB;AAC5C,MAAM,gBAAgB,EAAE,mBAAmB;AAC3C,MAAM,kBAAkB,EAAE,eAAe;AACzC,MAAM,kBAAkB,EAAE,kBAAkB;AAC5C,MAAM,mBAAmB,EAAE,oBAAoB;AAC/C,MAAM,mBAAmB,EAAE,iBAAiB;AAC5C,MAAM,sBAAsB,EAAE,qDAAqD;AACnF,MAAM,yBAAyB,EAAE,wVAAwV;AACzX;AACA,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,cAAc,EAAE;AACtB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,SAAS,EAAE,uBAAuB;AACxC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,eAAe,EAAE,2BAA2B;AAClD,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,YAAY,EAAE,sBAAsB;AAC1C,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,aAAa,EAAE,0BAA0B;AAC/C,MAAM,WAAW,EAAE,0BAA0B;AAC7C,MAAM,WAAW,EAAE,2BAA2B;AAC9C;AACA,MAAM,UAAU,EAAE,0BAA0B;AAC5C,MAAM,oBAAoB,EAAE,4BAA4B;AACxD,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,eAAe,EAAE,WAAW;AAClC,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,iBAAiB,EAAE,UAAU;AACnC,MAAM,cAAc,EAAE,gBAAgB;AACtC,MAAM,oBAAoB,EAAE,sBAAsB;AAClD,MAAM,iBAAiB,EAAE,kBAAkB;AAC3C,MAAM,iBAAiB,EAAE,eAAe;AACxC,MAAM,qBAAqB,EAAE,kBAAkB;AAC/C,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,gBAAgB,EAAE,sCAAsC;AAC9D,MAAM,eAAe,EAAE,mCAAmC;AAC1D,MAAM,cAAc,EAAE,6CAA6C;AACnE,MAAM,qBAAqB,EAAE,sBAAsB;AACnD,MAAM,WAAW,EAAE,gDAAgD;AACnE,MAAM,UAAU,EAAE,qDAAqD;AACvE,MAAM,mBAAmB,EAAE,kCAAkC;AAC7D,MAAM,gBAAgB,EAAE,8BAA8B;AACtD,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE,KAAK;AACrB,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,UAAU,EAAE,QAAQ;AAC1B,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,mBAAmB,EAAE,iBAAiB;AAC5C,MAAM,gBAAgB,EAAE,4BAA4B;AACpD,MAAM,kBAAkB,EAAE,iBAAiB;AAC3C,MAAM,kBAAkB,EAAE,oBAAoB;AAC9C,MAAM,mBAAmB,EAAE,sBAAsB;AACjD,MAAM,mBAAmB,EAAE,gBAAgB;AAC3C,MAAM,sBAAsB,EAAE,0DAA0D;AACxF,MAAM,yBAAyB,EAAE,mWAAmW;AACpY;AACA,MAAM,UAAU,EAAE,WAAW;AAC7B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,YAAY,EAAE,oBAAoB;AACxC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,cAAc,EAAE;AACtB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO;AAC5B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,CAAC,CAAC,UAAU;AACd,EAAE,CAAC,CAAC,MAAM;AACV,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,eAAe,EAAE,mBAAmB;AAC1C,MAAM,aAAa,EAAE,4CAA4C;AACjE,MAAM,aAAa,EAAE,4BAA4B;AACjD,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,GAAG,EAAE,QAAQ;AACnB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,aAAa,EAAE,0BAA0B;AAC/C,MAAM,eAAe,EAAE,0BAA0B;AACjD,MAAM,aAAa,EAAE,2CAA2C;AAChE,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,WAAW,EAAE;AACnB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,8HAA8H,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,6CAA6C,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;AACxb,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE,UAAU,CAAC,SAAS,EAAE;AACxB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE;AACxC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,WAAW,EAAE;AACnB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,EAAE,IAAI,CAAC,MAAM;AACb,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc;AAC1C,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,KAAK,GAAG,IAAI;AAClB,EAAE,IAAI,cAAc,GAAG,IAAI;AAC3B,EAAE,IAAI,mBAAmB,GAAG,KAAK;AACjC,EAAE,IAAI,mBAAmB,GAAG,KAAK;AACjC,EAAE,SAAS,oBAAoB,GAAG;AAClC,IAAI,mBAAmB,GAAG,KAAK;AAC/B,EAAE;AACF,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,YAAY,EAAE;AAClB,EAAE;AACF,EAAE,eAAe,YAAY,GAAG;AAChC,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;AAC3G,MAAM,KAAK,GAAG,QAAQ;AACtB,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;AACnE,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,KAAK,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,wCAAwC;AAC3F,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,aAAa,GAAG,KAAK;AAC3B,IAAI;AACJ,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACvE,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uEAAuE,CAAC,CAAC;AAClG,IAAI,SAAS,CAAC,UAAU,EAAE;AAC1B,MAAM;AACN,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iEAAiE,CAAC,CAAC;AAC5F,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC;AACpH,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;AACjD,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AACtC,QAAQ,SAAS,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,CAAC;AACzF,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC3D,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0FAA0F,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,8CAA8C,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;AACrO,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACrG,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,UAAU,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,mBAAmB,GAAG,0BAA0B,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,8EAA8E,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAChZ,IAAI,UAAU,CAAC,UAAU,EAAE;AAC3B,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,IAAI,UAAU,GAAG;AACvB,QAAQ,OAAO,mBAAmB;AAClC,MAAM,CAAC;AACP,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE;AAC9B,QAAQ,mBAAmB,GAAG,OAAO;AACrC,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC/C,IAAI,iBAAiB,CAAC,UAAU,EAAE;AAClC,MAAM,IAAI,EAAE,mBAAmB;AAC/B,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,aAAa,EAAE;AACrB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}