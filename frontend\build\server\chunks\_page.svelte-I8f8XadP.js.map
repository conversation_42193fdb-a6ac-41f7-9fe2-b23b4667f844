{"version": 3, "file": "_page.svelte-I8f8XadP.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/join-requests/_page.svelte.js"], "sourcesContent": ["import { x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, w as pop, u as push } from \"../../../../../../chunks/index.js\";\nimport \"../../../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../chunks/exports.js\";\nimport \"../../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../../chunks/format-date.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Join Requests — Commune\" },\n      joinRequests: \"Join Requests\",\n      loading: \"Loading...\",\n      noJoinRequests: \"No join requests found\",\n      member: \"member\",\n      members: \"members\",\n      headMember: \"Head\",\n      errorFetchingJoinRequests: \"Failed to fetch join requests\",\n      errorOccurred: \"An error occurred while fetching join requests\",\n      loadingMore: \"Loading more join requests...\",\n      cancel: \"Cancel Request\",\n      pending: \"Pending\",\n      accepted: \"Accepted\",\n      rejected: \"Rejected\",\n      requestedOn: \"Requested on\",\n      cancelingRequest: \"Canceling...\",\n      errorCancelingRequest: \"Failed to cancel join request\",\n      requestCanceled: \"Join request canceled\",\n      backToCommunes: \"Back to Communes\",\n      viewCommune: \"View Commune\",\n      awaitingApproval: \"Awaiting approval from commune head\",\n      noImage: \"No image\",\n      communeImageAlt: \"Commune image\"\n    },\n    ru: {\n      _page: {\n        title: \"Заявки на вступление — Коммуна\"\n      },\n      joinRequests: \"Заявки на вступление\",\n      loading: \"Загрузка...\",\n      noJoinRequests: \"Заявки не найдены\",\n      member: \"участник\",\n      members: \"участников\",\n      headMember: \"Глава\",\n      errorFetchingJoinRequests: \"Не удалось загрузить заявки\",\n      errorOccurred: \"Произошла ошибка при загрузке заявок\",\n      loadingMore: \"Загружаем больше заявок...\",\n      cancel: \"Отменить заявку\",\n      pending: \"Ожидает\",\n      accepted: \"Принято\",\n      rejected: \"Отклонено\",\n      requestedOn: \"Подана\",\n      cancelingRequest: \"Отменяем...\",\n      errorCancelingRequest: \"Не удалось отменить заявку\",\n      requestCanceled: \"Заявка отменена\",\n      backToCommunes: \"Назад к коммунам\",\n      viewCommune: \"Посмотреть коммуну\",\n      awaitingApproval: \"Ожидает одобрения главы коммуны\",\n      noImage: \"Нет изображения\",\n      communeImageAlt: \"Изображение коммуны\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let joinRequests = data.joinRequests;\n  let isHasMoreJoinRequests = data.isHasMoreJoinRequests;\n  let loadingStates = {};\n  function getStatusBadgeClass(status) {\n    switch (status) {\n      case \"pending\":\n        return \"bg-warning text-dark\";\n      case \"accepted\":\n        return \"bg-success\";\n      case \"rejected\":\n        return \"bg-danger\";\n      default:\n        return \"bg-secondary\";\n    }\n  }\n  function getStatusText(status) {\n    switch (status) {\n      case \"pending\":\n        return t.pending;\n      case \"accepted\":\n        return t.accepted;\n      case \"rejected\":\n        return t.rejected;\n      default:\n        return status;\n    }\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4\"><h1>${escape_html(t.joinRequests)}</h1> <a${attr(\"href\", toLocaleHref(\"/communes\"))} class=\"btn btn-outline-secondary\">${escape_html(t.backToCommunes)}</a></div> `);\n  if (joinRequests.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noJoinRequests)}</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(joinRequests);\n    $$payload.out.push(`<div class=\"row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4\"><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let joinRequest = each_array[$$index];\n      $$payload.out.push(`<div class=\"col\"><div class=\"card h-100 shadow-sm\">`);\n      if (joinRequest.commune.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"image-container svelte-lafg1c\"><img${attr(\"src\", `/images/${joinRequest.commune.image}`)}${attr(\"alt\", `${t.communeImageAlt}`)} class=\"svelte-lafg1c\"/></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"bg-light text-center d-flex align-items-center justify-content-center\" style=\"height: 140px;\"><span class=\"text-muted\">${escape_html(t.noImage)}</span></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div class=\"card-body d-flex flex-column\"><div class=\"d-flex justify-content-between align-items-start mb-2\"><span${attr_class(`badge ${getStatusBadgeClass(joinRequest.status)}`, \"svelte-lafg1c\")}>${escape_html(getStatusText(joinRequest.status))}</span> <small class=\"text-muted\">${escape_html(t.requestedOn)}\n                  ${escape_html(formatDate(joinRequest.createdAt, locale))}</small></div> <h5 class=\"card-title fs-5 text-truncate mb-2\">${escape_html(getAppropriateLocalization(joinRequest.commune?.name) || \"Unknown Commune\")}</h5> <p class=\"card-text text-muted small mb-3\" style=\"height: 3rem; overflow: hidden\">${escape_html(getAppropriateLocalization(joinRequest.commune.description) || \"\")}</p> <div class=\"mb-3\"><span class=\"badge bg-primary mb-2\">${escape_html(joinRequest.commune?.memberCount || 0)}\n                  ${escape_html((joinRequest.commune?.memberCount || 0) === 1 ? t.member : t.members)}</span> <div class=\"small text-muted\"><div>${escape_html(t.headMember)}:</div> <div class=\"d-flex flex-column\">${escape_html(getAppropriateLocalization(joinRequest.commune.headMember.name) || \"Unknown\")}</div></div></div> `);\n      if (joinRequest.status === \"pending\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"mt-auto\"><div class=\"alert alert-info small mb-2\">${escape_html(t.awaitingApproval)}</div> <button class=\"btn btn-outline-danger w-100\"${attr(\"disabled\", loadingStates[joinRequest.id] === \"canceling\", true)}>`);\n        if (loadingStates[joinRequest.id] === \"canceling\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span> ${escape_html(t.cancelingRequest)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.cancel)}`);\n        }\n        $$payload.out.push(`<!--]--></button></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"mt-auto\"><a${attr(\"href\", toLocaleHref(`/communes/${joinRequest.commune?.id || joinRequest.communeId}`))} class=\"btn btn-outline-primary w-100\">${escape_html(t.viewCommune)}</a></div>`);\n      }\n      $$payload.out.push(`<!--]--></div></div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (isHasMoreJoinRequests) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-3\">`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE;AACjD,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,cAAc,EAAE,wBAAwB;AAC9C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,UAAU,EAAE,MAAM;AACxB,MAAM,yBAAyB,EAAE,+BAA+B;AAChE,MAAM,aAAa,EAAE,gDAAgD;AACrE,MAAM,WAAW,EAAE,+BAA+B;AAClD,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,qBAAqB,EAAE,+BAA+B;AAC5D,MAAM,eAAe,EAAE,uBAAuB;AAC9C,MAAM,cAAc,EAAE,kBAAkB;AACxC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,gBAAgB,EAAE,qCAAqC;AAC7D,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,eAAe,EAAE;AACvB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,YAAY,EAAE,sBAAsB;AAC1C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,cAAc,EAAE,mBAAmB;AACzC,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,UAAU,EAAE,OAAO;AACzB,MAAM,yBAAyB,EAAE,6BAA6B;AAC9D,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,gBAAgB,EAAE,aAAa;AACrC,MAAM,qBAAqB,EAAE,4BAA4B;AACzD,MAAM,eAAe,EAAE,iBAAiB;AACxC,MAAM,cAAc,EAAE,kBAAkB;AACxC,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,gBAAgB,EAAE,iCAAiC;AACzD,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,eAAe,EAAE;AACvB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,EAAE,IAAI,qBAAqB,GAAG,IAAI,CAAC,qBAAqB;AACxD,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACvC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,sBAAsB;AACrC,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,YAAY;AAC3B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,WAAW;AAC1B,MAAM;AACN,QAAQ,OAAO,cAAc;AAC7B;AACA,EAAE;AACF,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,CAAC,CAAC,OAAO;AACxB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM;AACN,QAAQ,OAAO,MAAM;AACrB;AACA,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yGAAyG,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;AAC/R,EAAE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC;AACxH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACtD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oEAAoE,CAAC,CAAC;AAC9F,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC;AAC3C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mDAAmD,CAAC,CAAC;AAC/E,MAAM,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE;AACrC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACvM,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACvM,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2HAA2H,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;AACvV,kBAAkB,EAAE,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,0BAA0B,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,iBAAiB,CAAC,CAAC,wFAAwF,EAAE,WAAW,CAAC,0BAA0B,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,2DAA2D,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,CAAC;AAC3f,kBAAkB,EAAE,WAAW,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,0BAA0B,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACtU,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE;AAC5C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,mDAAmD,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1P,QAAQ,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC3D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC5I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACrD,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,OAAO,EAAE,EAAE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AACjO,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC;AACtD,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,qBAAqB,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}