{"version": 3, "file": "_page.svelte-B2iFsfhh.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/invitations/_page.svelte.js"], "sourcesContent": ["import { x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, w as pop, u as push } from \"../../../../../../chunks/index.js\";\nimport \"../../../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../chunks/exports.js\";\nimport \"../../../../../../chunks/state.svelte.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../../chunks/format-date.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Commune Invitations — Commune\" },\n      invitations: \"Commune Invitations\",\n      loading: \"Loading...\",\n      noInvitations: \"No invitations found\",\n      member: \"member\",\n      members: \"members\",\n      headMember: \"Head\",\n      errorFetchingInvitations: \"Failed to fetch invitations\",\n      errorOccurred: \"An error occurred while fetching invitations\",\n      loadingMore: \"Loading more invitations...\",\n      accept: \"Accept\",\n      reject: \"Reject\",\n      pending: \"Pending\",\n      accepted: \"Accepted\",\n      rejected: \"Rejected\",\n      expired: \"Expired\",\n      invitedOn: \"Invited on\",\n      acceptingInvitation: \"Accepting...\",\n      rejectingInvitation: \"Rejecting...\",\n      errorAcceptingInvitation: \"Failed to accept invitation\",\n      errorRejectingInvitation: \"Failed to reject invitation\",\n      invitationAccepted: \"Invitation accepted! Redirecting to commune...\",\n      backToCommunes: \"Back to Communes\",\n      viewCommune: \"View Commune\",\n      noImage: \"No image\",\n      communeImageAlt: \"Commune image\"\n    },\n    ru: {\n      _page: {\n        title: \"Приглашения в коммуны — Коммуна\"\n      },\n      invitations: \"Приглашения в коммуны\",\n      loading: \"Загрузка...\",\n      noInvitations: \"Приглашения не найдены\",\n      member: \"участник\",\n      members: \"участников\",\n      headMember: \"Глава\",\n      errorFetchingInvitations: \"Не удалось загрузить приглашения\",\n      errorOccurred: \"Произошла ошибка при загрузке приглашений\",\n      loadingMore: \"Загружаем больше приглашений...\",\n      accept: \"Принять\",\n      reject: \"Отклонить\",\n      pending: \"Ожидает\",\n      accepted: \"Принято\",\n      rejected: \"Отклонено\",\n      expired: \"Истекло\",\n      invitedOn: \"Приглашен\",\n      acceptingInvitation: \"Принимаем...\",\n      rejectingInvitation: \"Отклоняем...\",\n      errorAcceptingInvitation: \"Не удалось принять приглашение\",\n      errorRejectingInvitation: \"Не удалось отклонить приглашение\",\n      invitationAccepted: \"Приглашение принято! Перенаправляем в коммуну...\",\n      backToCommunes: \"Назад к коммунам\",\n      viewCommune: \"Посмотреть коммуну\",\n      noImage: \"Нет изображения\",\n      communeImageAlt: \"Изображение коммуны\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const {\n    locale,\n    routeLocale,\n    toLocaleHref,\n    getAppropriateLocalization\n  } = data;\n  const t = i18n[locale];\n  let invitations = data.invitations;\n  let isHasMoreInvitations = data.isHasMoreInvitations;\n  let loadingStates = {};\n  function getStatusBadgeClass(status) {\n    switch (status) {\n      case \"pending\":\n        return \"bg-warning text-dark\";\n      case \"accepted\":\n        return \"bg-success\";\n      case \"rejected\":\n        return \"bg-danger\";\n      case \"expired\":\n        return \"bg-secondary\";\n      default:\n        return \"bg-secondary\";\n    }\n  }\n  function getStatusText(status) {\n    switch (status) {\n      case \"pending\":\n        return t.pending;\n      case \"accepted\":\n        return t.accepted;\n      case \"rejected\":\n        return t.rejected;\n      case \"expired\":\n        return t.expired;\n      default:\n        return status;\n    }\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4\"><h1>${escape_html(t.invitations)}</h1> <a${attr(\"href\", toLocaleHref(\"/communes\"))} class=\"btn btn-outline-secondary\">${escape_html(t.backToCommunes)}</a></div> `);\n  if (invitations.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noInvitations)}</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(invitations);\n    $$payload.out.push(`<div class=\"row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4\"><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let invitation = each_array[$$index];\n      $$payload.out.push(`<div class=\"col\"><div class=\"card h-100 shadow-sm\">`);\n      if (invitation.commune.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"image-container svelte-lafg1c\"><img${attr(\"src\", `/images/${invitation.commune.image}`)}${attr(\"alt\", `${t.communeImageAlt}`)} class=\"svelte-lafg1c\"/></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"bg-light text-center d-flex align-items-center justify-content-center\" style=\"height: 140px;\"><span class=\"text-muted\">${escape_html(t.noImage)}</span></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div class=\"card-body d-flex flex-column\"><div class=\"d-flex justify-content-between align-items-start mb-2\"><span${attr_class(`badge ${getStatusBadgeClass(invitation.status)}`, \"svelte-lafg1c\")}>${escape_html(getStatusText(invitation.status))}</span> <small class=\"text-muted\">${escape_html(t.invitedOn)}\n                  ${escape_html(formatDate(invitation.createdAt, locale))}</small></div> <h5 class=\"card-title fs-5 text-truncate mb-2\">${escape_html(getAppropriateLocalization(invitation.commune?.name) || \"Unknown Commune\")}</h5> <p class=\"card-text text-muted small mb-3\" style=\"height: 3rem; overflow: hidden\">${escape_html(getAppropriateLocalization(invitation.commune.description) || \"\")}</p> <div class=\"mb-3\"><span class=\"badge bg-primary mb-2\">${escape_html(invitation.commune?.memberCount || 0)}\n                  ${escape_html((invitation.commune?.memberCount || 0) === 1 ? t.member : t.members)}</span> <div class=\"small text-muted\"><div>${escape_html(t.headMember)}:</div> <div class=\"d-flex flex-column\">${escape_html(getAppropriateLocalization(invitation.commune.headMember.name) || \"Unknown\")}</div></div></div> `);\n      if (invitation.status === \"pending\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"mt-auto\"><div class=\"d-grid gap-2\"><button class=\"btn btn-success\"${attr(\"disabled\", loadingStates[invitation.id] === \"accepting\", true)}>`);\n        if (loadingStates[invitation.id] === \"accepting\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span> ${escape_html(t.acceptingInvitation)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.accept)}`);\n        }\n        $$payload.out.push(`<!--]--></button> <button class=\"btn btn-outline-danger\"${attr(\"disabled\", loadingStates[invitation.id] === \"rejecting\", true)}>`);\n        if (loadingStates[invitation.id] === \"rejecting\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span> ${escape_html(t.rejectingInvitation)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.reject)}`);\n        }\n        $$payload.out.push(`<!--]--></button></div></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"mt-auto\"><a${attr(\"href\", toLocaleHref(`/communes/${invitation.commune?.id || invitation.communeId}`))} class=\"btn btn-outline-primary w-100\">${escape_html(t.viewCommune)}</a></div>`);\n      }\n      $$payload.out.push(`<!--]--></div></div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (isHasMoreInvitations) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-3\">`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE;AACvD,MAAM,WAAW,EAAE,qBAAqB;AACxC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,UAAU,EAAE,MAAM;AACxB,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,aAAa,EAAE,8CAA8C;AACnE,MAAM,WAAW,EAAE,6BAA6B;AAChD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,mBAAmB,EAAE,cAAc;AACzC,MAAM,mBAAmB,EAAE,cAAc;AACzC,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,kBAAkB,EAAE,gDAAgD;AAC1E,MAAM,cAAc,EAAE,kBAAkB;AACxC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,eAAe,EAAE;AACvB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,UAAU,EAAE,OAAO;AACzB,MAAM,wBAAwB,EAAE,kCAAkC;AAClE,MAAM,aAAa,EAAE,2CAA2C;AAChE,MAAM,WAAW,EAAE,iCAAiC;AACpD,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,mBAAmB,EAAE,cAAc;AACzC,MAAM,mBAAmB,EAAE,cAAc;AACzC,MAAM,wBAAwB,EAAE,gCAAgC;AAChE,MAAM,wBAAwB,EAAE,kCAAkC;AAClE,MAAM,kBAAkB,EAAE,kDAAkD;AAC5E,MAAM,cAAc,EAAE,kBAAkB;AACxC,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,eAAe,EAAE;AACvB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;AACpC,EAAE,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;AACtD,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACvC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,sBAAsB;AACrC,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,YAAY;AAC3B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,WAAW;AAC1B,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,cAAc;AAC7B,MAAM;AACN,QAAQ,OAAO,cAAc;AAC7B;AACA,EAAE;AACF,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,CAAC,CAAC,OAAO;AACxB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,CAAC,CAAC,OAAO;AACxB,MAAM;AACN,QAAQ,OAAO,MAAM;AACrB;AACA,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yGAAyG,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;AAC9R,EAAE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC;AACvH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACrD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oEAAoE,CAAC,CAAC;AAC9F,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC;AAC1C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mDAAmD,CAAC,CAAC;AAC/E,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE;AACpC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACtM,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACvM,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2HAA2H,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,mBAAmB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;AACnV,kBAAkB,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,0BAA0B,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,iBAAiB,CAAC,CAAC,wFAAwF,EAAE,WAAW,CAAC,0BAA0B,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,2DAA2D,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,CAAC;AACvf,kBAAkB,EAAE,WAAW,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,0BAA0B,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACpU,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;AAC3C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8EAA8E,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACpL,QAAQ,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC1D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC/I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wDAAwD,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9J,QAAQ,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC1D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC/I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6BAA6B,CAAC,CAAC;AAC3D,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AAC/N,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC;AACtD,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,oBAAoB,EAAE;AAC5B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}