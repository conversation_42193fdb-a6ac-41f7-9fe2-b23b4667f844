-- CreateTable
CREATE TABLE "_reactor_post_images" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_post_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_reactor_post_images_B_index" ON "_reactor_post_images"("B");

-- AddForeignKey
ALTER TABLE "_reactor_post_images" ADD CONSTRAINT "_reactor_post_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_images" ADD CONSTRAINT "_reactor_post_images_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
