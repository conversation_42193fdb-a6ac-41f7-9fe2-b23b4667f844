{"version": 3, "file": "11-C05_NXVH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/join-requests/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/11.js"], "sourcesContent": ["import { a as consts_exports } from \"../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, url }) => {\n  const { fetcher: api } = getClient();\n  const joinRequests = await api.commune.joinRequest.list.get({}, { fetch, ctx: { url } });\n  const communes = joinRequests.length ? await api.commune.list.get(\n    { ids: joinRequests.map(({ communeId }) => communeId) },\n    { fetch, ctx: { url } }\n  ) : [];\n  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));\n  const joinRequestsWithDetails = joinRequests.map((joinRequest) => ({\n    ...joinRequest,\n    commune: communeMap.get(joinRequest.communeId)\n  }));\n  return {\n    joinRequests: joinRequestsWithDetails,\n    isHasMoreJoinRequests: joinRequests.length === consts_exports.PAGE_SIZE\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/communes/join-requests/_page.ts.js';\n\nexport const index = 11;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/join-requests/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/communes/join-requests/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/11.BMdJkmD-.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/DiZKRWcx.js\",\"_app/immutable/chunks/CL12WlkV.js\"];\nexport const stylesheets = [\"_app/immutable/assets/10.DefDkanu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1F,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;AACnE,IAAI,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,SAAS,CAAC,EAAE;AAC3D,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AACzB,GAAG,GAAG,EAAE;AACR,EAAE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9E,EAAE,MAAM,uBAAuB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,MAAM;AACrE,IAAI,GAAG,WAAW;AAClB,IAAI,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS;AACjD,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,YAAY,EAAE,uBAAuB;AACzC,IAAI,qBAAqB,EAAE,YAAY,CAAC,MAAM,KAAK,cAAc,CAAC;AAClE,GAAG;AACH,CAAC;;;;;;;AChBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA4E,CAAC,EAAE;AAE1I,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACjnB,MAAC,WAAW,GAAG,CAAC,uCAAuC;AACvD,MAAC,KAAK,GAAG;;;;"}