"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// src/acrpc/core.ts
require('zod');
var _superjson = require('superjson'); var _superjson2 = _interopRequireDefault(_superjson);




var _caseconverters = require('@ocelotjungle/case-converters');
function log(...args) {
}
function dir(...args) {
}
var kebabTransformer = new (0, _caseconverters.CaseTransformer)(
  new (0, _caseconverters.UnknownCaseStrategy)(),
  new (0, _caseconverters.KebabCaseStrategy)()
);
var methods = [
  "get",
  "post",
  "put",
  "patch",
  "delete"
];
var jsonTransformer = {
  serialize: JSON.stringify,
  deserialize: JSON.parse
};
var superjsonTransformer = {
  serialize: _superjson2.default.stringify,
  deserialize: _superjson2.default.parse
};








exports.log = log; exports.dir = dir; exports.kebabTransformer = kebabTransformer; exports.methods = methods; exports.jsonTransformer = jsonTransformer; exports.superjsonTransformer = superjsonTransformer;
//# sourceMappingURL=chunk-467PIPJE.cjs.map