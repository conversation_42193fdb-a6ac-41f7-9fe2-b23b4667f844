import { u as push, N as ensure_array_like, y as attr, z as escape_html, J as attr_class, G as attr_style, K as stringify, w as pop, V as bind_props, Q as copy_payload, T as assign_payload } from './index-0Ke2LYl0.js';
import { g as goto } from './client-BUddp2Wf.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import { a as consts_exports } from './current-user-BM0W6LNm.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { M as Modal } from './modal-BDhz9azZ.js';
import { L as Localized_input } from './localized-input-BFX4O5ct.js';
import { E as Editor } from './editor-DhPp1GEa.js';
import { R as Reactor_hub_picker } from './reactor-hub-picker-CYoaonei.js';
import { h as html } from './html-FW6Ia4bL.js';

function Localized_editor($$payload, $$props) {
  push();
  const i18n = {
    en: {
      languages: { en: "English", ru: "Russian" },
      providedTranslations: "Provided translations:"
    },
    ru: {
      languages: {
        en: "Английский",
        ru: "Русский"
      },
      providedTranslations: "Указанные переводы:"
    }
  };
  let { value = void 0, $$slots, $$events, ...props } = $$props;
  const {
    id,
    label,
    required = false,
    locale,
    languageSelectPosition = "top",
    children,
    onEditorInit
  } = props;
  const t = i18n[locale];
  let selectedLanguage = locale;
  function getLanguageDisplay() {
    return selectedLanguage.toUpperCase();
  }
  let editorContent = "";
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<div class="mb-3">`);
    if (languageSelectPosition === "top") {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="d-flex justify-content-between align-items-center mb-2"><p class="form-label mb-0 svelte-3fvz9w">${escape_html(label)} `);
      if (required) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<span class="text-danger">*</span>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></p> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"${attr("id", `dropdown-${id}`)} data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;">${escape_html(getLanguageDisplay())}</button> <ul class="dropdown-menu dropdown-menu-end"${attr("aria-labelledby", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "en" ? "active" : "")}`)} type="button">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "ru" ? "active" : "")}`)} type="button">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<p class="form-label mb-2 svelte-3fvz9w">${escape_html(label)} `);
      if (required) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<span class="text-danger">*</span>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></p>`);
    }
    $$payload2.out.push(`<!--]--> <div${attr("id", id)}>`);
    Editor($$payload2, {
      onEditorInit,
      get content() {
        return editorContent;
      },
      set content($$value) {
        editorContent = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!----></div> `);
    if (languageSelectPosition === "bottom") {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="d-flex justify-content-between align-items-center mt-2"><div class="d-flex align-items-center">`);
      if (children) {
        $$payload2.out.push("<!--[-->");
        children($$payload2);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"${attr("id", `dropdown-${id}`)} data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;">${escape_html(getLanguageDisplay())}</button> <ul class="dropdown-menu dropdown-menu-end"${attr("aria-labelledby", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "en" ? "active" : "")}`)} type="button">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "ru" ? "active" : "")}`)} type="button">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (value.length > 0) {
      $$payload2.out.push("<!--[-->");
      const each_array = ensure_array_like(value.filter(Boolean));
      $$payload2.out.push(`<div class="mt-2 small text-muted"><div>${escape_html(t.providedTranslations)}</div> <ul class="list-unstyled mb-0 mt-1"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let val = each_array[$$index];
        $$payload2.out.push(`<li class="badge bg-light text-dark me-1">${escape_html(t.languages[val.locale])}</li>`);
      }
      $$payload2.out.push(`<!--]--></ul></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { value });
  pop();
}
function Tag_picker($$payload, $$props) {
  push();
  const i18n = {
    en: {
      tags: "Tags",
      addTag: "Add tag",
      searchTags: "Search tags...",
      noTagsFound: "No tags found",
      loading: "Loading..."
    },
    ru: {
      tags: "Теги",
      addTag: "Добавить тег",
      searchTags: "Поиск тегов...",
      noTagsFound: "Теги не найдены",
      loading: "Загрузка..."
    }
  };
  const { fetcher: api } = getClient();
  let { selectedTagIds = void 0, locale, label, placeholder } = $$props;
  const t = i18n[locale];
  let showTagInput = false;
  let selectedTags = [];
  function getAppropriateLocalization(localizations) {
    const localization = localizations.find((l) => l.locale === locale);
    return localization?.value || localizations[0]?.value || "";
  }
  const each_array = ensure_array_like(selectedTags);
  $$payload.out.push(`<div class="mb-3">`);
  if (label && showTagInput) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<label for="tag-search-input" class="form-label">${escape_html(label)}</label>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="tag-picker svelte-1snh682"><div class="selected-tags d-flex flex-wrap gap-2 mb-2 svelte-1snh682"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let tag = each_array[$$index];
    $$payload.out.push(`<span class="badge bg-primary d-flex align-items-center">${escape_html(getAppropriateLocalization(tag.name))} <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" aria-label="Remove tag"></button></span>`);
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<button type="button" class="btn btn-outline-secondary btn-sm"><i class="bi bi-plus"></i> ${escape_html(t.addTag)}</button>`);
  }
  $$payload.out.push(`<!--]--></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div>`);
  bind_props($$props, { selectedTagIds });
  pop();
}
function Reactor_community_picker($$payload, $$props) {
  push();
  const i18n = {
    en: {
      community: "Community",
      selectCommunity: "Select community",
      searchCommunities: "Search communities...",
      noCommunitiesFound: "No communities found",
      loading: "Loading...",
      clearSelection: "Clear selection"
    },
    ru: {
      community: "Сообщество",
      selectCommunity: "Выбрать сообщество",
      searchCommunities: "Поиск сообществ...",
      noCommunitiesFound: "Сообщества не найдены",
      loading: "Загрузка...",
      clearSelection: "Очистить выбор"
    }
  };
  const { fetcher: api } = getClient();
  let {
    selectedCommunityId = void 0,
    locale,
    hubId,
    label,
    placeholder
  } = $$props;
  const t = i18n[locale];
  let showCommunityInput = false;
  $$payload.out.push(`<div class="mb-3">`);
  if (label && showCommunityInput) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<label for="community-search-input" class="form-label">${escape_html(label)}</label>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="community-picker svelte-njlx63"><div class="selected-community d-flex align-items-center gap-2 mb-2 svelte-njlx63">`);
  {
    $$payload.out.push("<!--[!-->");
    {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button type="button" class="btn btn-outline-secondary"><i class="bi bi-people-fill"></i> ${escape_html(t.selectCommunity)}</button>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div>`);
  bind_props($$props, { selectedCommunityId });
  pop();
}
function Create_post_modal($$payload, $$props) {
  push();
  const { show, locale, toLocaleHref, onClose, onPostCreated, post } = $$props;
  const { fetcher: api } = getClient();
  const i18n = {
    en: {
      createPostTitle: "Create New Post",
      editPostTitle: "Edit Post",
      cancel: "Cancel",
      create: "Create",
      save: "Save Changes",
      hub: "Hub",
      hubPlaceholder: "Select hub (optional)...",
      community: "Community",
      communityPlaceholder: "Select community (optional)...",
      hubDisabledByCommunity: "Hub selection is disabled when Community is specified",
      communityDisabledByHub: "Community selection is disabled when Hub is specified",
      tags: "Tags",
      title: "Title",
      titlePlaceholder: "Enter post title...",
      body: "Body",
      bodyPlaceholder: "Write your post content...",
      titleRequired: "Title is required",
      bodyRequired: "Body is required",
      createSuccess: "Post created successfully!",
      createError: "Failed to create post",
      updateSuccess: "Post updated successfully!",
      updateError: "Failed to update post",
      // Image upload translations
      images: "Images",
      uploadImages: "Upload Images",
      dragDropImages: "Drag & drop images here or click to select",
      maxImages: "Maximum 10 images",
      uploading: "Uploading...",
      imageUploadSuccess: "Images uploaded successfully!",
      imageUploadError: "Failed to upload images",
      invalidFileType: "Invalid file type. Please upload JPG, PNG, or WebP images.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      tooManyFiles: "Too many files. Maximum 10 images per post.",
      insertImage: "Insert image",
      removeImage: "Remove image",
      imageGallery: "Image Gallery",
      loadingImages: "Loading images..."
    },
    ru: {
      createPostTitle: "Создать новый пост",
      editPostTitle: "Редактировать пост",
      cancel: "Отмена",
      create: "Создать",
      save: "Сохранить изменения",
      hub: "Хаб",
      hubPlaceholder: "Выберите хаб (необязательно)...",
      community: "Сообщество",
      communityPlaceholder: "Выберите сообщество (необязательно)...",
      hubDisabledByCommunity: "Выбор хаба отключен, когда указано сообщество",
      communityDisabledByHub: "Выбор сообщества отключен, когда указан хаб",
      tags: "Теги",
      title: "Заголовок",
      titlePlaceholder: "Введите заголовок поста...",
      body: "Содержание",
      bodyPlaceholder: "Напишите содержание поста...",
      titleRequired: "Заголовок обязателен",
      bodyRequired: "Содержание обязательно",
      createSuccess: "Пост успешно создан!",
      createError: "Не удалось создать пост",
      updateSuccess: "Пост успешно обновлен!",
      updateError: "Не удалось обновить пост",
      // Image upload translations
      images: "Изображения",
      uploadImages: "Загрузить изображения",
      dragDropImages: "Перетащите изображения сюда или нажмите для выбора",
      maxImages: "Максимум 10 изображений",
      uploading: "Загрузка...",
      imageUploadSuccess: "Изображения загружены успешно!",
      imageUploadError: "Не удалось загрузить изображения",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображения.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      tooManyFiles: "Слишком много файлов. Максимум 10 изображений на пост.",
      insertImage: "Вставить изображение",
      removeImage: "Удалить изображение",
      imageGallery: "Галерея изображений",
      loadingImages: "Загрузка изображений..."
    }
  };
  const t = i18n[locale];
  let postTitle = [];
  let postBody = [];
  let hubId = null;
  let communityId = null;
  let selectedTags = [];
  let isSubmitting = false;
  let formError = null;
  let formSuccess = null;
  let uploadedImages = [];
  async function handleSubmitPost() {
    formError = null;
    formSuccess = null;
    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);
    const hasBody = postBody.some((item) => item.value.trim().length > 0);
    if (!hasTitle) {
      formError = t.titleRequired;
      return;
    }
    if (!hasBody) {
      formError = t.bodyRequired;
      return;
    }
    isSubmitting = true;
    try {
      if (post) {
        await api.reactor.post.patch({
          id: post.id,
          title: postTitle.filter((item) => item.value.trim().length > 0),
          body: postBody.filter((item) => item.value.trim().length > 0),
          tagIds: selectedTags,
          imageIds: uploadedImages.map((img) => img.id)
        });
        formSuccess = t.updateSuccess;
        setTimeout(
          () => {
            onClose();
            window.location.reload();
          },
          1500
        );
      } else {
        const { id } = await api.reactor.post.post({
          hubId,
          communityId,
          title: postTitle.filter((item) => item.value.trim().length > 0),
          body: postBody.filter((item) => item.value.trim().length > 0),
          tagIds: selectedTags,
          imageIds: uploadedImages.map((img) => img.id)
        });
        formSuccess = t.createSuccess;
        if (onPostCreated) {
          onPostCreated();
        }
        setTimeout(
          () => {
            goto(toLocaleHref(`/reactor/${id}`));
          },
          1500
        );
      }
    } catch (error) {
      console.error("Error submitting post:", error);
      formError = error instanceof Error ? error.message : post ? t.updateError : t.createError;
    } finally {
      isSubmitting = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Modal($$payload2, {
      show,
      title: post ? t.editPostTitle : t.createPostTitle,
      onClose,
      onSubmit: handleSubmitPost,
      submitText: post ? t.save : t.create,
      cancelText: t.cancel,
      submitDisabled: isSubmitting || !postTitle.some((item) => item.value.trim().length > 0) || !postBody.some((item) => item.value.trim().length > 0),
      isSubmitting,
      children: ($$payload3) => {
        $$payload3.out.push(`<form>`);
        if (!post) {
          $$payload3.out.push("<!--[-->");
          if (communityId) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="form-text text-muted">${escape_html(t.hubDisabledByCommunity)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
            $$payload3.out.push(`<div class="mb-3">`);
            Reactor_hub_picker($$payload3, {
              locale,
              label: t.hub,
              placeholder: t.hubPlaceholder,
              get selectedHubId() {
                return hubId;
              },
              set selectedHubId($$value) {
                hubId = $$value;
                $$settled = false;
              }
            });
            $$payload3.out.push(`<!----></div>`);
          }
          $$payload3.out.push(`<!--]--> `);
          if (hubId) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="form-text text-muted">${escape_html(t.communityDisabledByHub)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
            $$payload3.out.push(`<div class="mb-3">`);
            Reactor_community_picker($$payload3, {
              hubId,
              locale,
              label: t.community,
              placeholder: t.communityPlaceholder,
              get selectedCommunityId() {
                return communityId;
              },
              set selectedCommunityId($$value) {
                communityId = $$value;
                $$settled = false;
              }
            });
            $$payload3.out.push(`<!----></div>`);
          }
          $$payload3.out.push(`<!--]-->`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> `);
        Localized_input($$payload3, {
          locale,
          id: "post-title",
          label: t.title,
          placeholder: t.titlePlaceholder,
          required: true,
          get value() {
            return postTitle;
          },
          set value($$value) {
            postTitle = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----> `);
        Localized_editor($$payload3, {
          locale,
          id: "post-body",
          label: t.body,
          required: true,
          onEditorInit: (editor) => {
          },
          languageSelectPosition: "top",
          get value() {
            return postBody;
          },
          set value($$value) {
            postBody = $$value;
            $$settled = false;
          },
          children: ($$payload4) => {
            $$payload4.out.push(`<button type="button" class="btn btn-outline-secondary btn-sm"${attr("disabled", uploadedImages.length >= 10, true)}${attr("title", t.uploadImages)}><i class="bi bi-image"></i> ${escape_html(t.uploadImages)}</button>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <input${attr("id", post ? "edit-image-upload-input" : "image-upload-input")} type="file" multiple${attr("accept", consts_exports.ALLOWED_IMAGE_FILE_TYPES.join(","))} style="display: none;"/> <div class="mb-3">`);
        {
          $$payload3.out.push("<!--[!-->");
          if (uploadedImages.length > 0) {
            $$payload3.out.push("<!--[-->");
            const each_array = ensure_array_like(uploadedImages);
            $$payload3.out.push(`<div class="mt-3"><h6>${escape_html(t.imageGallery)}</h6> <div class="row g-2"><!--[-->`);
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let image = each_array[$$index];
              $$payload3.out.push(`<div class="col-6 col-md-4 col-lg-3"><div class="position-relative"><button type="button" class="btn p-0 border-0 bg-transparent w-100"${attr("title", t.insertImage)}${attr("aria-label", t.insertImage)}><img${attr("src", `/images/${image.url}`)} alt="" class="img-thumbnail w-100" style="height: 120px; object-fit: cover;"/></button> <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"${attr("title", t.removeImage)}${attr("aria-label", t.removeImage)} style="padding: 0.25rem 0.5rem; font-size: 0.75rem;"><i class="bi bi-x"></i></button></div></div>`);
            }
            $$payload3.out.push(`<!--]--></div></div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]-->`);
        }
        $$payload3.out.push(`<!--]--> <div class="form-label">${escape_html(t.images)}</div> <div${attr_class(`border border-2 border-dashed rounded p-4 text-center ${stringify("border-secondary")}`)} role="button" tabindex="0" style="cursor: pointer; min-height: 100px; display: flex; flex-direction: column; justify-content: center;">`);
        {
          $$payload3.out.push("<!--[!-->");
          $$payload3.out.push(`<div class="text-muted"><i class="bi bi-cloud-upload fs-2 mb-2"></i> <div>${escape_html(t.dragDropImages)}</div> <small class="text-muted">${escape_html(t.maxImages)}</small></div>`);
        }
        $$payload3.out.push(`<!--]--></div> `);
        {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> `);
        {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--></div> `);
        Tag_picker($$payload3, {
          locale,
          label: t.tags,
          get selectedTagIds() {
            return selectedTags;
          },
          set selectedTagIds($$value) {
            selectedTags = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----> `);
        if (formError) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-danger mt-3" role="alert">${escape_html(formError)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> `);
        if (formSuccess) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-success mt-3" role="alert">${escape_html(formSuccess)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--></form>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function Post_card($$payload, $$props) {
  push();
  const {
    post,
    locale,
    toLocaleHref,
    getAppropriateLocalization,
    currentUser,
    onEditPost
  } = $$props;
  const i18n = {
    en: {
      usefulness: "Usefulness",
      editPost: "Edit Post",
      getPlural(n) {
        if (n === 1) return 0;
        return 1;
      },
      ratingTooltipText(rating2) {
        const likesWord = ["like", "likes"][this.getPlural(rating2.likes % 10)];
        const dislikesWord = ["dislike", "dislikes"][this.getPlural(rating2.dislikes % 10)];
        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now"
      }
    },
    ru: {
      usefulness: "Полезность",
      editPost: "Редактировать пост",
      getPlural(n) {
        if (n === 1) return 0;
        if (n >= 2 && n <= 4) return 1;
        return 2;
      },
      ratingTooltipText(rating2) {
        const likesWord = [
          "лайк",
          "лайка",
          "лайков"
        ][this.getPlural(rating2.likes % 10)];
        const dislikesWord = [
          "дизлайк",
          "дизлайка",
          "дизлайков"
        ][this.getPlural(rating2.dislikes % 10)];
        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n) {
          const word = [
            "день",
            "дня",
            "дней"
          ][i18n.ru.getPlural(n)];
          return `${n} ${word} назад`;
        },
        hours(n) {
          const word = [
            "час",
            "часа",
            "часов"
          ][i18n.ru.getPlural(n)];
          return `${n} ${word} назад`;
        },
        minutes(n) {
          const word = [
            "минуту",
            "минуты",
            "минут"
          ][i18n.ru.getPlural(n)];
          return `${n} ${word} назад`;
        },
        seconds(n) {
          const word = [
            "секунду",
            "секунды",
            "секунд"
          ][i18n.ru.getPlural(n)];
          return `${n} ${word} назад`;
        },
        rightNow: "только что"
      }
    }
  };
  const { fetcher: api } = getClient();
  const t = i18n[locale];
  let rating = post.rating;
  let usefulness = post.usefulness;
  const ratingValue = rating.likes - rating.dislikes;
  const usefulnessValue = usefulness.totalValue ?? 0;
  const ratingTooltipText = t.ratingTooltipText(rating);
  const authorName = getAppropriateLocalization(post.author.name);
  const title = getAppropriateLocalization(post.title);
  const body = getAppropriateLocalization(post.body);
  const hubName = post.hub ? getAppropriateLocalization(post.hub.name) : null;
  const communityName = post.community ? getAppropriateLocalization(post.community.name) : null;
  let copiedTagId = null;
  const canEditPost = currentUser && (currentUser.role === "admin" || currentUser.id === post.author.id);
  function formatDate(date) {
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1e3);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }
  const each_array = ensure_array_like(Array(5));
  const each_array_1 = ensure_array_like([...post.tags]);
  $$payload.out.push(`<div class="post-card mb-4 svelte-1oqwi5d"><div class="card"><div class="card-header">`);
  if (post.hub || post.community) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="hub-community-header mb-3 svelte-1oqwi5d">`);
    if (post.hub) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="hub-row d-flex align-items-center mb-2"><div class="hub-image-container me-2 svelte-1oqwi5d">`);
      if (post.hub.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<img${attr("src", `/images/${post.hub.image}`)}${attr("alt", hubName || "Hub")} class="hub-image svelte-1oqwi5d"/>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="hub-image-placeholder svelte-1oqwi5d"><i class="bi bi-collection text-muted svelte-1oqwi5d"></i></div>`);
      }
      $$payload.out.push(`<!--]--></div> <a${attr("href", toLocaleHref(`/reactor/hubs/${post.hub.id}`))} class="hub-link text-decoration-none fw-medium svelte-1oqwi5d">${escape_html(hubName)}</a></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> `);
    if (post.community) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="community-row d-flex align-items-center"><div class="community-image-container me-2 svelte-1oqwi5d">`);
      if (post.community.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<img${attr("src", `/images/${post.community.image}`)}${attr("alt", communityName || "Community")} class="community-image svelte-1oqwi5d"/>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="community-image-placeholder svelte-1oqwi5d"><i class="bi bi-people text-muted svelte-1oqwi5d"></i></div>`);
      }
      $$payload.out.push(`<!--]--></div> <a${attr("href", toLocaleHref(`/reactor/communities/${post.community.id}`))} class="community-link text-decoration-none fw-medium svelte-1oqwi5d">${escape_html(communityName)}</a></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="post-header d-flex justify-content-between align-items-center mb-3"><div class="d-flex align-items-center"><div class="rating-block d-flex align-items-center me-3">`);
  if (ratingValue > 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="rating-value me-2 text-success svelte-1oqwi5d" data-bs-toggle="tooltip" data-bs-placement="top"${attr("title", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
    if (ratingValue < 0) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="rating-value me-2 text-danger svelte-1oqwi5d" data-bs-toggle="tooltip" data-bs-placement="top"${attr("title", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<span class="rating-value me-2 svelte-1oqwi5d" data-bs-toggle="tooltip" data-bs-placement="top"${attr("title", ratingTooltipText)}>0</span>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--> <div class="rating-buttons"><button${attr_class(`btn btn-sm me-1 ${rating?.status === "like" ? "btn-success" : "btn-outline-success"}`, "svelte-1oqwi5d")} aria-label="Like"><i class="bi bi-hand-thumbs-up svelte-1oqwi5d"></i></button> <button${attr_class(`btn btn-sm ${rating?.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`, "svelte-1oqwi5d")} aria-label="Dislike"><i class="bi bi-hand-thumbs-down svelte-1oqwi5d"></i></button></div></div> <div class="author-info d-flex align-items-center">`);
  if (post.author.image) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<img${attr("src", `/images/${post.author.image}`)} alt="avatar" class="avatar rounded-circle me-2" width="32" height="32"${attr_style("", { "object-fit": "cover" })}/>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="avatar rounded-circle me-2"></div>`);
  }
  $$payload.out.push(`<!--]--> <div><a${attr("href", toLocaleHref(`/users/${post.author.id}`))} class="author-name fw-bold"${attr_style("", { "text-decoration": "none" })}>${escape_html(authorName ?? "Anonymous")}</a> <div class="post-time small text-muted"${attr("title", post.createdAt.toISOString())}>${escape_html(formatDate(post.createdAt))}</div></div></div></div> <div class="usefulness-block"><div class="d-flex flex-column align-items-start">`);
  if (usefulness && usefulness.count > 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="usefulness-label mb-1 text-muted small px-1">${escape_html(t.usefulness)} (${escape_html(usefulness.count)})</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<span class="usefulness-label mb-1 text-muted small px-1">${escape_html(t.usefulness)}</span>`);
  }
  $$payload.out.push(`<!--]--> <div role="group" aria-label="Usefulness rating"><!--[-->`);
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    each_array[i];
    $$payload.out.push(`<button class="btn btn-sm p-0 px-1"${attr("aria-label", `Rate usefulness ${i + 1}`)}><i${attr_class(`bi bi-star${stringify((i + 1) * 2 <= usefulnessValue ? "-fill" : "")} text-warning rating-star`)}></i></button>`);
  }
  $$payload.out.push(`<!--]--></div></div></div></div></div> <div class="card-body"><div class="d-flex justify-content-between align-items-start mb-2"><a${attr("href", toLocaleHref(`/reactor/${post.id}`))} class="post-title-link-wrapper flex-grow-1 svelte-1oqwi5d"><h5 class="card-title mb-0 svelte-1oqwi5d">${escape_html(title)}</h5></a> `);
  if (canEditPost && onEditPost) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<button type="button" class="btn btn-outline-secondary btn-sm ms-2"${attr("title", t.editPost)}${attr("aria-label", t.editPost)}><i class="bi bi-pencil"></i></button>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> <div class="card-text">${html(body)}</div> <div class="tags mb-3 svelte-1oqwi5d"><!--[-->`);
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let tag = each_array_1[$$index_1];
    $$payload.out.push(`<button${attr_class(`badge me-1 ${copiedTagId === tag.id ? "bg-success text-white" : "bg-light text-secondary"}`, "svelte-1oqwi5d")}>${escape_html(getAppropriateLocalization(tag.name))}</button>`);
  }
  $$payload.out.push(`<!--]--></div> <div class="card-actions d-flex"><a${attr("href", toLocaleHref(`/reactor/${post.id}`))} target="_blank"><button${attr_class(`btn btn-sm ${"btn-outline-secondary"}`, "svelte-1oqwi5d")} aria-label="Copy link">`);
  {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<i class="bi bi-link-45deg svelte-1oqwi5d"></i>`);
  }
  $$payload.out.push(`<!--]--></button></a></div></div></div></div>`);
  pop();
}
function Right_menu($$payload, $$props) {
  push();
  let { isExpanded = false, $$slots, $$events, ...props } = $$props;
  const { locale, toLocaleHref } = props;
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { isExpanded });
  pop();
}

export { Create_post_modal as C, Post_card as P, Right_menu as R };
//# sourceMappingURL=right-menu-B2wfEj2g.js.map
