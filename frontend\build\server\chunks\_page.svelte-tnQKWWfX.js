import { u as push, x as head, z as escape_html, N as ensure_array_like, y as attr, w as pop } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-nBPcN0GL.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Users — Commune" },
      users: "Users",
      loading: "Loading...",
      noUsersFound: "No users found",
      errorFetchingUsers: "Failed to fetch users",
      errorOccurred: "An error occurred while fetching users",
      loadingMore: "Loading more users...",
      noImage: "No image",
      userImageAlt: "User image",
      role: { admin: "Admin" }
    },
    ru: {
      _page: {
        title: "Пользователи — Коммуна"
      },
      users: "Пользователи",
      loading: "Загрузка...",
      noUsersFound: "Пользователи не найдены",
      errorFetchingUsers: "Не удалось загрузить пользователей",
      errorOccurred: "Произошла ошибка при загрузке пользователей",
      loadingMore: "Загружаем больше пользователей...",
      noImage: "Нет изображения",
      userImageAlt: "Изображение пользователя",
      role: { admin: "Админ" }
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let users = data.users;
  let isHasMoreUsers = data.isHasMoreUsers;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><h1>${escape_html(t.users)}</h1></div> `);
  if (users.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noUsersFound)}</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(users);
    $$payload.out.push(`<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let user = each_array[$$index];
      $$payload.out.push(`<div class="col"><div class="card h-100 shadow-sm hover-card svelte-1g1c3uj"><a${attr("href", toLocaleHref(`/users/${user.id}`))} class="text-decoration-none text-black">`);
      if (user.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div style="height: 140px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"><img${attr("src", `/images/${user.image}`)}${attr("alt", `${t.userImageAlt}`)} style="width: 100%; height: 100%; object-fit: cover;"/></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted">${escape_html(t.noImage)}</span></div>`);
      }
      $$payload.out.push(`<!--]--> <div class="card-body d-flex flex-column"><h5 class="card-title fs-5 text-truncate">${escape_html(getAppropriateLocalization(user.name))}</h5> <p class="card-text text-muted small" style="height: 3rem; overflow: hidden">${escape_html(getAppropriateLocalization(user.description) || "")}</p> <div class="mt-auto"><div class="small text-muted">`);
      if (user.role === "admin") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<span class="badge bg-danger">${escape_html(t.role.admin)}</span>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div></div></div></a></div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  if (isHasMoreUsers) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-3">`);
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-tnQKWWfX.js.map
