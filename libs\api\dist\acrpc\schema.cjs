"use strict";Object.defineProperty(exports, "__esModule", {value: true});







var _chunkCIBHE3A3cjs = require('../chunk-CIBHE3A3.cjs');


var _chunk467PIPJEcjs = require('../chunk-467PIPJE.cjs');
require('../chunk-Q7SFCCGT.cjs');

// src/acrpc/schema.ts
var CACHE_CONTROL_TEN_MINUTES = "no-cache";
var CACHE_CONTROL_HOUR = "no-cache";
var CACHE_CONTROL_IMMUTABLE = "no-cache";
var DEFAULT_CACHE_CONTROL = CACHE_CONTROL_TEN_MINUTES;
var schema = {
  auth: {
    otp: {
      post: {
        input: _chunkCIBHE3A3cjs.auth_exports.SendOtpInputSchema,
        output: _chunkCIBHE3A3cjs.auth_exports.SendOtpOutputSchema,
        isMetadataUsed: false
      }
    },
    signUp: {
      post: {
        input: _chunkCIBHE3A3cjs.auth_exports.SignupInputSchema,
        output: _chunkCIBHE3A3cjs.auth_exports.SuccessfulOutputSchema,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    },
    signIn: {
      post: {
        input: _chunkCIBHE3A3cjs.auth_exports.SigninInputSchema,
        output: _chunkCIBHE3A3cjs.auth_exports.SuccessfulOutputSchema,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    },
    signOut: {
      get: {
        input: null,
        output: null,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    }
  },
  commune: {
    transferHeadStatus: {
      post: {
        input: _chunkCIBHE3A3cjs.commune_exports.TransferHeadStatusInputSchema,
        output: null,
        autoScopeInvalidationDepth: 2
      }
    },
    list: {
      get: {
        input: _chunkCIBHE3A3cjs.commune_exports.GetCommunesInputSchema,
        output: _chunkCIBHE3A3cjs.commune_exports.GetCommunesOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL,
        isMetadataRequired: false
      }
    },
    post: {
      input: _chunkCIBHE3A3cjs.commune_exports.CreateCommuneInputSchema,
      output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
      autoScopeInvalidationDepth: 1
    },
    patch: {
      input: _chunkCIBHE3A3cjs.commune_exports.UpdateCommuneInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    delete: {
      input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    member: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.commune_exports.GetCommuneMembersInputSchema,
          output: _chunkCIBHE3A3cjs.commune_exports.GetCommuneMembersOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.commune_exports.CreateCommuneMemberInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    invitation: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.commune_exports.GetCommuneInvitationsInputSchema,
          output: _chunkCIBHE3A3cjs.commune_exports.GetCommuneInvitationsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.commune_exports.CreateCommuneInvitationInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      accept: {
        post: {
          input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      },
      reject: {
        post: {
          input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      }
    },
    joinRequest: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.commune_exports.GetCommuneJoinRequestsInputSchema,
          output: _chunkCIBHE3A3cjs.commune_exports.GetCommuneJoinRequestsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.commune_exports.CreateCommuneJoinRequestInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      accept: {
        post: {
          input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      },
      reject: {
        post: {
          input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      }
    }
  },
  rating: {
    karma: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.rating_exports.GetKarmaPointsInputSchema,
          output: _chunkCIBHE3A3cjs.rating_exports.GetKarmaPointsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.rating_exports.SpendKarmaPointInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1,
        invalidate: ["/rating/summary"]
      }
    },
    feedback: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.rating_exports.GetUserFeedbacksInputSchema,
          output: _chunkCIBHE3A3cjs.rating_exports.GetUserFeedbacksOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.rating_exports.CreateUserFeedbackInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1,
        invalidate: ["/rating/summary"]
      }
    },
    summary: {
      get: {
        input: _chunkCIBHE3A3cjs.rating_exports.GetUserSummaryInputSchema,
        output: _chunkCIBHE3A3cjs.rating_exports.GetUserSummaryOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      }
    }
  },
  reactor: {
    post: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.reactor_exports.GetPostsInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.GetPostsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.reactor_exports.CreatePostInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkCIBHE3A3cjs.reactor_exports.UpdatePostInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.reactor_exports.DeletePostInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      rating: {
        post: {
          input: _chunkCIBHE3A3cjs.reactor_exports.UpdatePostRatingInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.UpdatePostRatingOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      usefulness: {
        post: {
          input: _chunkCIBHE3A3cjs.reactor_exports.UpdatePostUsefulnessInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.UpdatePostUsefulnessOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      image: {
        list: {
          get: {
            input: _chunkCIBHE3A3cjs.reactor_exports.GetPostImagesInputSchema,
            output: _chunkCIBHE3A3cjs.reactor_exports.GetPostImagesOutputSchema,
            cacheControl: DEFAULT_CACHE_CONTROL
          }
        }
      }
    },
    comment: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.reactor_exports.GetCommentsInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.GetCommentsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.reactor_exports.CreateCommentInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkCIBHE3A3cjs.reactor_exports.UpdateCommentInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.reactor_exports.DeleteCommentInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      rating: {
        post: {
          input: _chunkCIBHE3A3cjs.reactor_exports.UpdateCommentRatingInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.UpdateCommentRatingOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      anonimify: {
        post: {
          input: _chunkCIBHE3A3cjs.reactor_exports.AnonimifyCommentInputSchema,
          output: null,
          autoScopeInvalidationDepth: 2
        }
      }
    },
    lens: {
      list: {
        get: {
          input: null,
          output: _chunkCIBHE3A3cjs.reactor_exports.GetLensesOutputSchema,
          cacheControl: CACHE_CONTROL_IMMUTABLE
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.reactor_exports.CreateLensInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkCIBHE3A3cjs.reactor_exports.UpdateLensInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    hub: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.reactor_exports.GetHubsInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.GetHubsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.reactor_exports.CreateHubInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkCIBHE3A3cjs.reactor_exports.UpdateHubInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
      // delete: {
      //     input: Common.ObjectWithIdSchema,
      //     output: null,
      //     enableAutoScopeInvalidation: true,
      // },
    },
    community: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.reactor_exports.GetCommunitiesInputSchema,
          output: _chunkCIBHE3A3cjs.reactor_exports.GetCommunitiesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.reactor_exports.CreateCommunityInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkCIBHE3A3cjs.reactor_exports.UpdateCommunityInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
      // delete: {
      //     input: Common.ObjectWithIdSchema,
      //     output: null,
      //     enableAutoScopeInvalidation: true,
      // },
    }
  },
  tag: {
    list: {
      get: {
        input: _chunkCIBHE3A3cjs.tag_exports.GetTagsInputSchema,
        output: _chunkCIBHE3A3cjs.tag_exports.GetTagsOutputSchema,
        cacheControl: CACHE_CONTROL_HOUR
      }
    },
    post: {
      input: _chunkCIBHE3A3cjs.tag_exports.CreateTagInputSchema,
      output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
      autoScopeInvalidationDepth: 1
    },
    patch: {
      input: _chunkCIBHE3A3cjs.tag_exports.UpdateTagInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    delete: {
      input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    }
  },
  user: {
    list: {
      get: {
        input: _chunkCIBHE3A3cjs.user_exports.GetUsersInputSchema,
        output: _chunkCIBHE3A3cjs.user_exports.GetUsersOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      }
    },
    me: {
      get: {
        input: null,
        output: _chunkCIBHE3A3cjs.user_exports.GetMeOutputSchema,
        cacheControl: CACHE_CONTROL_HOUR
      }
    },
    patch: {
      input: _chunkCIBHE3A3cjs.user_exports.UpdateUserInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    title: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.user_exports.GetUserTitlesInputSchema,
          output: _chunkCIBHE3A3cjs.user_exports.GetUserTitlesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkCIBHE3A3cjs.user_exports.CreateUserTitleInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkCIBHE3A3cjs.user_exports.UpdateUserTitleInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    note: {
      get: {
        input: _chunkCIBHE3A3cjs.user_exports.GetUserNoteInputSchema,
        output: _chunkCIBHE3A3cjs.user_exports.GetUserNoteOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      },
      put: {
        input: _chunkCIBHE3A3cjs.user_exports.UpdateUserNoteInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    invite: {
      list: {
        get: {
          input: _chunkCIBHE3A3cjs.user_exports.GetUserInvitesInputSchema,
          output: _chunkCIBHE3A3cjs.user_exports.GetUserInvitesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      put: {
        input: _chunkCIBHE3A3cjs.user_exports.UpsertUserInviteInputSchema,
        output: _chunkCIBHE3A3cjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkCIBHE3A3cjs.user_exports.DeleteUserInviteInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    }
  }
};
var transformer = _chunk467PIPJEcjs.superjsonTransformer;







exports.CACHE_CONTROL_HOUR = CACHE_CONTROL_HOUR; exports.CACHE_CONTROL_IMMUTABLE = CACHE_CONTROL_IMMUTABLE; exports.CACHE_CONTROL_TEN_MINUTES = CACHE_CONTROL_TEN_MINUTES; exports.DEFAULT_CACHE_CONTROL = DEFAULT_CACHE_CONTROL; exports.schema = schema; exports.transformer = transformer;
//# sourceMappingURL=schema.cjs.map