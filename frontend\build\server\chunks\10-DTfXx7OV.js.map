{"version": 3, "file": "10-DTfXx7OV.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/invitations/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/10.js"], "sourcesContent": ["import { a as consts_exports } from \"../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, url }) => {\n  const { fetcher: api } = getClient();\n  const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });\n  const communes = invitations.length ? await api.commune.list.get(\n    { ids: invitations.map(({ communeId }) => communeId) },\n    { fetch, ctx: { url } }\n  ) : [];\n  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));\n  const invitationsWithDetails = invitations.map((invitation) => ({\n    ...invitation,\n    commune: communeMap.get(invitation.communeId)\n  }));\n  return {\n    invitations: invitationsWithDetails,\n    isHasMoreInvitations: invitations.length === consts_exports.PAGE_SIZE\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/communes/invitations/_page.ts.js';\n\nexport const index = 10;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/invitations/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/communes/invitations/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/10.Qgo4_yo4.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CSZ3sDel.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/KKeKRB0S.js\",\"_app/immutable/chunks/DiZKRWcx.js\",\"_app/immutable/chunks/CL12WlkV.js\"];\nexport const stylesheets = [\"_app/immutable/assets/10.DefDkanu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AACxF,EAAE,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;AAClE,IAAI,EAAE,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,SAAS,CAAC,EAAE;AAC1D,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AACzB,GAAG,GAAG,EAAE;AACR,EAAE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9E,EAAE,MAAM,sBAAsB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,MAAM;AAClE,IAAI,GAAG,UAAU;AACjB,IAAI,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS;AAChD,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,WAAW,EAAE,sBAAsB;AACvC,IAAI,oBAAoB,EAAE,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC;AAChE,GAAG;AACH,CAAC;;;;;;;AChBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA0E,CAAC,EAAE;AAExI,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACrpB,MAAC,WAAW,GAAG,CAAC,uCAAuC;AACvD,MAAC,KAAK,GAAG;;;;"}